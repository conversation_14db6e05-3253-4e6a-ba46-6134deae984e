import { ConfigService } from '@nestjs/config';
export declare enum AuthEventType {
    LOGIN_ATTEMPT = "LOGIN_ATTEMPT",
    LOGIN_SUCCESS = "LOGIN_SUCCESS",
    LOGIN_FAILURE = "LOGIN_FAILURE",
    LOGOUT = "LOGOUT",
    REGISTRATION = "REGISTRATION",
    TOKEN_REFRESH = "TOKEN_REFRESH",
    TOKEN_REFRESH_FAILURE = "TOKEN_REFRESH_FAILURE",
    PASSWORD_RESET_REQUEST = "PASSWORD_RESET_REQUEST",
    PASSWORD_RESET = "PASSWORD_RESET",
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION",
    SUSPICIOUS_ACTIVITY = "SUSPICIOUS_ACTIVITY"
}
export declare enum AuthEventSeverity {
    INFO = "INFO",
    WARNING = "WARNING",
    ERROR = "ERROR",
    CRITICAL = "CRITICAL"
}
export interface AuthEventData {
    userId?: string;
    email?: string;
    ip?: string;
    userAgent?: string;
    fingerprint?: string;
    result?: 'success' | 'failure';
    reason?: string;
    metadata?: Record<string, any>;
}
export declare class AuthEventLoggerService {
    private readonly configService;
    private readonly logger;
    constructor(configService: ConfigService);
    logEvent(eventType: AuthEventType, severity: AuthEventSeverity, data: AuthEventData, correlationId?: string): string;
    logLoginAttempt(data: AuthEventData, correlationId?: string): string;
    logLoginSuccess(data: AuthEventData, correlationId?: string): string;
    logLoginFailure(data: AuthEventData, correlationId?: string): string;
    logLogout(data: AuthEventData, correlationId?: string): string;
    logTokenRefresh(data: AuthEventData, correlationId?: string): string;
    logTokenRefreshFailure(data: AuthEventData, correlationId?: string): string;
    private storeEvent;
}
