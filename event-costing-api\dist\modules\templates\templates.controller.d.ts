import { TemplatesService } from './templates.service';
import { ListTemplatesDto } from './dto/list-templates.dto';
import { PaginatedTemplatesResponse } from './dto/template-summary.dto';
import { TemplateDetailDto, EnhancedTemplateDetailDto } from './dto/template-summary.dto';
export declare class TemplatesController {
    private readonly templatesService;
    private readonly logger;
    constructor(templatesService: TemplatesService);
    findPublicTemplates(queryDto: ListTemplatesDto): Promise<PaginatedTemplatesResponse>;
    findOnePublic(id: string): Promise<TemplateDetailDto>;
    findOnePublicEnhanced(id: string): Promise<EnhancedTemplateDetailDto>;
}
