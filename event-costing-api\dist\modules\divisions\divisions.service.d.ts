import { SupabaseService } from '../../core/supabase/supabase.service';
import { DivisionDto } from './dto/division.dto';
import { CreateDivisionDto } from './dto/create-division.dto';
import { UpdateDivisionDto } from './dto/update-division.dto';
export declare class DivisionsService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    private readonly selectFields;
    private readonly uniqueConstraint;
    constructor(supabaseService: SupabaseService);
    findAll(active?: boolean): Promise<DivisionDto[]>;
    findOneById(id: string): Promise<DivisionDto>;
    createDivision(createDto: CreateDivisionDto): Promise<DivisionDto>;
    updateDivision(id: string, updateDto: UpdateDivisionDto): Promise<DivisionDto>;
    deleteDivision(id: string): Promise<void>;
}
