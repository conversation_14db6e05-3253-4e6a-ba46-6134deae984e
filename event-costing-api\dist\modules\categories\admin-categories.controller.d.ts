import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryDto } from './dto/category.dto';
export declare class AdminCategoriesController {
    private readonly categoriesService;
    private readonly logger;
    constructor(categoriesService: CategoriesService);
    createCategory(createDto: CreateCategoryDto): Promise<CategoryDto>;
    findOne(id: string): Promise<CategoryDto>;
    updateCategory(id: string, updateDto: UpdateCategoryDto): Promise<CategoryDto>;
    deleteCategory(id: string): Promise<void>;
}
