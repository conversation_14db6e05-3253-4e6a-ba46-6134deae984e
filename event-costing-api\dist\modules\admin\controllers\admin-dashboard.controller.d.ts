import { AdminDashboardService } from '../services/admin-dashboard.service';
import { AdminDashboardDataDto } from '../dto/admin-dashboard-data.dto';
import { AdminDashboardFiltersDto } from '../dto/admin-dashboard-filters.dto';
import { User } from '@supabase/supabase-js';
export declare class AdminDashboardController {
    private readonly adminDashboardService;
    private readonly logger;
    constructor(adminDashboardService: AdminDashboardService);
    getDashboardData(filters: AdminDashboardFiltersDto, user: User): Promise<AdminDashboardDataDto>;
    getDashboardSummary(user: User): Promise<any>;
    getHealthCheck(user: User): Promise<any>;
}
