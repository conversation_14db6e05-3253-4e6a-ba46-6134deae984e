"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchUpdatePackagesDto = exports.PackageUpdateItem = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class PackageUpdateItem {
    id;
    categoryId;
    divisionId;
}
exports.PackageUpdateItem = PackageUpdateItem;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PackageUpdateItem.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New category ID (optional)',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], PackageUpdateItem.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New division ID (optional)',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], PackageUpdateItem.prototype, "divisionId", void 0);
class BatchUpdatePackagesDto {
    packages;
}
exports.BatchUpdatePackagesDto = BatchUpdatePackagesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of package updates',
        type: [PackageUpdateItem],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PackageUpdateItem),
    __metadata("design:type", Array)
], BatchUpdatePackagesDto.prototype, "packages", void 0);
//# sourceMappingURL=batch-update-packages.dto.js.map