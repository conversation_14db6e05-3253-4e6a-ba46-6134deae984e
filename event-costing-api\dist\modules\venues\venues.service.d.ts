import { SupabaseService } from '../../core/supabase/supabase.service';
import { VenueReferenceDto } from './dto/venue-reference.dto';
import { AdminVenueDto, CreateVenueDto, ListVenuesQueryDto, PaginatedVenuesResponse, UpdateVenueDto } from './dto/admin-venue.dto';
export declare class VenuesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<VenueReferenceDto[]>;
    findAllWithFilters(cityId?: string, active?: boolean): Promise<VenueReferenceDto[]>;
    findByIds(ids: string[]): Promise<VenueReferenceDto[]>;
    findWithEnhancedFilters(cityId?: string, active?: boolean, classification?: string, minCapacity?: number, maxCapacity?: number): Promise<VenueReferenceDto[]>;
    findAllAdmin(queryDto: ListVenuesQueryDto): Promise<PaginatedVenuesResponse>;
    findOneAdmin(id: string): Promise<AdminVenueDto>;
    create(createDto: CreateVenueDto): Promise<AdminVenueDto>;
    update(id: string, updateDto: UpdateVenueDto): Promise<AdminVenueDto>;
    remove(id: string): Promise<void>;
    restore(id: string): Promise<void>;
}
