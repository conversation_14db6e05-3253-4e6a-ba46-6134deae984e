export declare class CalculationBreakdownDto {
    packageId: string;
    packageName: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    currency: string;
    unitCost?: number;
    totalCost?: number;
}
export declare class TemplateCalculationResultDto {
    packagesTotal: number;
    customItemsTotal: number;
    grandTotal: number;
    breakdown: CalculationBreakdownDto[];
    currency: string;
    hasValidPrices: boolean;
    missingPrices: string[];
    totalCost?: number;
    estimatedProfit?: number;
}
export declare class TemplateCalculationSummaryDto {
    totalPackages: number;
    totalValue: number;
    currency: string;
    hasValidPrices: boolean;
    missingPricesCount: number;
    averagePackageValue: number;
    totalCost?: number;
    profitMarginPercentage?: number;
}
