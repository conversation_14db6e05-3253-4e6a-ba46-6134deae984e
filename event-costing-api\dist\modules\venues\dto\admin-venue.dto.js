"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListVenuesQueryDto = exports.PaginatedVenuesResponse = exports.UpdateVenueDto = exports.CreateVenueDto = exports.AdminVenueDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class AdminVenueDto {
    id;
    name;
    description;
    address;
    city_id;
    city_name;
    classification;
    capacity;
    image_url;
    features;
    is_active;
    is_deleted;
    deleted_at;
    created_at;
    updated_at;
}
exports.AdminVenueDto = AdminVenueDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        description: 'Venue ID',
    }),
    __metadata("design:type", String)
], AdminVenueDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Venue name',
    }),
    __metadata("design:type", String)
], AdminVenueDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue description',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue address',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        nullable: true,
        description: 'City ID',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'City name',
    }),
    __metadata("design:type", String)
], AdminVenueDto.prototype, "city_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue classification (outdoor, hotel, indoor, premium, luxury)',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "classification", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        nullable: true,
        description: 'Maximum attendee capacity',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "capacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue image URL',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "image_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'array',
        items: { type: 'string' },
        nullable: true,
        description: 'Venue features and amenities',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Boolean,
        description: 'Whether the venue is active',
    }),
    __metadata("design:type", Boolean)
], AdminVenueDto.prototype, "is_active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Boolean,
        description: 'Whether the venue is deleted',
    }),
    __metadata("design:type", Boolean)
], AdminVenueDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Deletion timestamp',
    }),
    __metadata("design:type", Object)
], AdminVenueDto.prototype, "deleted_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Creation timestamp',
    }),
    __metadata("design:type", String)
], AdminVenueDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Last update timestamp',
    }),
    __metadata("design:type", String)
], AdminVenueDto.prototype, "updated_at", void 0);
class CreateVenueDto {
    name;
    description;
    address;
    city_id;
    is_active;
    classification;
    capacity;
    image_url;
    features;
}
exports.CreateVenueDto = CreateVenueDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Venue name',
    }),
    __metadata("design:type", String)
], CreateVenueDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue description',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue address',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "address", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        nullable: true,
        description: 'City ID',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "city_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, swagger_1.ApiProperty)({
        type: Boolean,
        description: 'Whether the venue is active',
        default: true,
        required: false,
    }),
    __metadata("design:type", Boolean)
], CreateVenueDto.prototype, "is_active", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['outdoor', 'hotel', 'indoor', 'premium', 'luxury']),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue classification',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "classification", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        nullable: true,
        description: 'Maximum attendee capacity',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "capacity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue image URL',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "image_url", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, swagger_1.ApiProperty)({
        type: 'array',
        items: { type: 'string' },
        nullable: true,
        description: 'Venue features and amenities',
        required: false,
    }),
    __metadata("design:type", Object)
], CreateVenueDto.prototype, "features", void 0);
class UpdateVenueDto {
    name;
    description;
    address;
    city_id;
    is_active;
    classification;
    capacity;
    image_url;
    features;
}
exports.UpdateVenueDto = UpdateVenueDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Venue name',
        required: false,
    }),
    __metadata("design:type", String)
], UpdateVenueDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue description',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue address',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "address", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        nullable: true,
        description: 'City ID',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "city_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, swagger_1.ApiProperty)({
        type: Boolean,
        description: 'Whether the venue is active',
        required: false,
    }),
    __metadata("design:type", Boolean)
], UpdateVenueDto.prototype, "is_active", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['outdoor', 'hotel', 'indoor', 'premium', 'luxury']),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue classification',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "classification", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        nullable: true,
        description: 'Maximum attendee capacity',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "capacity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue image URL',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "image_url", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, swagger_1.ApiProperty)({
        type: 'array',
        items: { type: 'string' },
        nullable: true,
        description: 'Venue features and amenities',
        required: false,
    }),
    __metadata("design:type", Object)
], UpdateVenueDto.prototype, "features", void 0);
class PaginatedVenuesResponse {
    data;
    totalCount;
    page;
    pageSize;
    totalPages;
}
exports.PaginatedVenuesResponse = PaginatedVenuesResponse;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [AdminVenueDto],
        description: 'List of venues',
    }),
    __metadata("design:type", Array)
], PaginatedVenuesResponse.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Total number of venues matching the query',
    }),
    __metadata("design:type", Number)
], PaginatedVenuesResponse.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Current page number',
    }),
    __metadata("design:type", Number)
], PaginatedVenuesResponse.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Number of venues per page',
    }),
    __metadata("design:type", Number)
], PaginatedVenuesResponse.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Total number of pages',
    }),
    __metadata("design:type", Number)
], PaginatedVenuesResponse.prototype, "totalPages", void 0);
class ListVenuesQueryDto {
    search;
    cityId;
    showDeleted;
    page;
    pageSize;
    sortBy;
    sortOrder;
    classification;
    minCapacity;
    maxCapacity;
}
exports.ListVenuesQueryDto = ListVenuesQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        required: false,
        description: 'Search term for venue name',
    }),
    __metadata("design:type", String)
], ListVenuesQueryDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        required: false,
        description: 'Filter by city ID',
    }),
    __metadata("design:type", String)
], ListVenuesQueryDto.prototype, "cityId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    (0, swagger_1.ApiProperty)({
        type: Boolean,
        required: false,
        description: 'Include deleted venues',
        default: false,
    }),
    __metadata("design:type", Boolean)
], ListVenuesQueryDto.prototype, "showDeleted", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        required: false,
        description: 'Page number',
        default: 1,
    }),
    __metadata("design:type", Number)
], ListVenuesQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        required: false,
        description: 'Number of venues per page',
        default: 10,
    }),
    __metadata("design:type", Number)
], ListVenuesQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        required: false,
        description: 'Sort by field',
        default: 'name',
    }),
    __metadata("design:type", String)
], ListVenuesQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        type: String,
        required: false,
        description: 'Sort order',
        default: 'asc',
        enum: ['asc', 'desc'],
    }),
    __metadata("design:type", String)
], ListVenuesQueryDto.prototype, "sortOrder", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['outdoor', 'hotel', 'indoor', 'premium', 'luxury']),
    (0, swagger_1.ApiProperty)({
        type: String,
        required: false,
        description: 'Filter by venue classification',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    }),
    __metadata("design:type", String)
], ListVenuesQueryDto.prototype, "classification", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        required: false,
        description: 'Minimum capacity filter',
    }),
    __metadata("design:type", Number)
], ListVenuesQueryDto.prototype, "minCapacity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, swagger_1.ApiProperty)({
        type: Number,
        required: false,
        description: 'Maximum capacity filter',
    }),
    __metadata("design:type", Number)
], ListVenuesQueryDto.prototype, "maxCapacity", void 0);
//# sourceMappingURL=admin-venue.dto.js.map