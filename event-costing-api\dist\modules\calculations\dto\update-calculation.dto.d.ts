import { CalculationStatus } from '../enums/calculation-status.enum';
declare class TaxDetailItemDto {
    name: string;
    rate: number;
    type: string;
    basis?: string;
}
declare class DiscountDetailDto {
    name: string;
    amount: number;
    reason?: string;
}
export declare class UpdateCalculationDto {
    name?: string;
    city_id?: string | null;
    venue_ids?: string[] | null;
    event_start_date?: string | null;
    event_end_date?: string | null;
    attendees?: number | null;
    event_type_id?: string | null;
    notes?: string | null;
    version_notes?: string | null;
    client_id?: string | null;
    event_id?: string | null;
    status?: CalculationStatus;
    taxes?: TaxDetailItemDto[] | null;
    discount?: DiscountDetailDto | null;
}
export {};
