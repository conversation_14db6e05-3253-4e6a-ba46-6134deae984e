import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TemplateSummaryDto } from './template-summary.dto';
import { CategoryDto } from '../../categories/dto/category.dto';
import { TemplateFiltersDto } from './template-filters.dto';

/**
 * DTO for paginated template results
 */
export class PaginatedTemplatesDto {
  @ApiProperty({
    description: 'Array of templates',
    type: [Object], // Using Object since we don't have a specific TemplateDto
  })
  data: any[];

  @ApiProperty({
    description: 'Total number of templates',
    example: 25,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}

/**
 * DTO for event type information
 */
export class EventTypeInfoDto {
  @ApiProperty({
    description: 'Event type ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Event type name',
    example: 'Corporate Event',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Event type description',
    example: 'Corporate meetings, conferences, and business events',
  })
  description?: string;
}

/**
 * DTO for package information in template context
 */
export class TemplatePackageDto {
  @ApiProperty({
    description: 'Package ID',
    type: String,
    format: 'uuid',
  })
  package_id: string;

  @ApiProperty({
    description: 'Package name',
    example: 'Sound System Basic',
  })
  name: string;

  @ApiProperty({
    description: 'Package description',
    example: 'Basic sound system for small events',
  })
  description: string | null;

  @ApiProperty({
    description: 'Category ID',
    type: String,
    format: 'uuid',
  })
  category_id: string;

  @ApiProperty({
    description: 'Package price',
    example: 1500000,
  })
  price: number;

  @ApiProperty({
    description: 'Quantity basis',
    example: 'per_day',
  })
  quantity_basis: string;

  @ApiProperty({
    description: 'Whether package is available in city',
    example: true,
  })
  is_available_in_city: boolean;

  @ApiProperty({
    description: 'Whether package is available in venue',
    example: true,
  })
  is_available_in_venue: boolean;
}

/**
 * DTO for template summary statistics
 */
export class TemplateStatsDto {
  @ApiProperty({
    description: 'Total number of templates',
    example: 25,
  })
  totalTemplates: number;

  @ApiProperty({
    description: 'Number of active templates',
    example: 20,
  })
  activeTemplates: number;

  @ApiProperty({
    description: 'Number of inactive templates',
    example: 5,
  })
  inactiveTemplates: number;

  @ApiProperty({
    description: 'Number of public templates',
    example: 15,
  })
  publicTemplates: number;

  @ApiProperty({
    description: 'Number of private templates',
    example: 10,
  })
  privateTemplates: number;
}

/**
 * DTO for available filter options
 */
export class TemplateAvailableFiltersDto {
  @ApiProperty({
    description: 'Available categories for filtering',
    type: [Object],
  })
  categories: Array<{ id: string; name: string }>;

  @ApiProperty({
    description: 'Available event types for filtering',
    type: [Object],
  })
  eventTypes: Array<{ id: string; name: string }>;
}

/**
 * DTO for filter information
 */
export class TemplateFilterInfoDto {
  @ApiProperty({
    description: 'Applied filters',
    type: TemplateFiltersDto,
  })
  applied: TemplateFiltersDto;

  @ApiProperty({
    description: 'Available filter options',
    type: TemplateAvailableFiltersDto,
  })
  available: TemplateAvailableFiltersDto;
}

/**
 * DTO for template management metadata
 */
export class TemplateManagementMetadataDto {
  @ApiProperty({
    description: 'Time taken to load the data in milliseconds',
    example: 280,
  })
  loadTime: number;

  @ApiProperty({
    description: 'Cache version for the response',
    example: '1.0',
  })
  cacheVersion: string;

  @ApiProperty({
    description: 'User ID who requested the data',
    type: String,
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Any errors encountered during data loading',
    type: [String],
    example: [],
  })
  errors?: string[];

  @ApiProperty({
    description: 'Timestamp when the data was loaded',
    type: String,
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Total number of templates',
    example: 25,
  })
  totalTemplates: number;

  @ApiProperty({
    description: 'Number of filters applied',
    example: 1,
  })
  appliedFilters: number;
}

/**
 * Complete template management data response DTO
 * Consolidates all template management-related data in a single response
 */
export class TemplateCompleteDataDto {
  @ApiProperty({
    description: 'Paginated template results',
    type: PaginatedTemplatesDto,
  })
  templates: PaginatedTemplatesDto;

  @ApiProperty({
    description: 'All available categories',
    type: [CategoryDto],
  })
  categories: CategoryDto[];

  @ApiProperty({
    description: 'All available event types',
    type: [EventTypeInfoDto],
  })
  eventTypes: EventTypeInfoDto[];

  @ApiProperty({
    description: 'Available packages for template creation',
    type: [TemplatePackageDto],
  })
  packages: TemplatePackageDto[];

  @ApiPropertyOptional({
    description: 'Template summary statistics',
    type: TemplateStatsDto,
  })
  summary?: TemplateStatsDto;

  @ApiProperty({
    description: 'Filter information',
    type: TemplateFilterInfoDto,
  })
  filters: TemplateFilterInfoDto;

  @ApiProperty({
    description: 'Metadata about the response',
    type: TemplateManagementMetadataDto,
  })
  metadata: TemplateManagementMetadataDto;
}
