import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CreateCalculationDto } from '../dto/create-calculation.dto';
import { ListCalculationsDto } from '../dto/list-calculations.dto';
import { CalculationSummaryDto } from '../dto/paginated-calculations.dto';
import { UpdateCalculationDto } from '../dto/update-calculation.dto';
import { PaginatedResponseDto } from '../../../shared/dtos/paginated-response.dto';
import { CalculationHistoryRaw } from '../interfaces/calculation-internal.interfaces';
export declare class CalculationCrudService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    createCalculation(createCalculationDto: CreateCalculationDto, user: User): Promise<string>;
    findUserCalculations(user: User, queryParams: ListCalculationsDto): Promise<PaginatedResponseDto<CalculationSummaryDto>>;
    findCalculationRawById(id: string, user: User): Promise<CalculationHistoryRaw>;
    updateCalculationData(id: string, updateData: Omit<UpdateCalculationDto, 'venue_ids'>, user: User): Promise<void>;
    deleteCalculation(id: string, user: User): Promise<void>;
    triggerRecalculation(id: string): Promise<void>;
}
