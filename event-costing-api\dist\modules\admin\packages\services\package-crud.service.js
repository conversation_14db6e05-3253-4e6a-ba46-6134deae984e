"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageCrudService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCrudService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../../core/supabase/supabase.service");
const supabase_js_1 = require("@supabase/supabase-js");
let PackageCrudService = PackageCrudService_1 = class PackageCrudService {
    supabaseService;
    logger = new common_1.Logger(PackageCrudService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async createPackage(createPackageDto) {
        this.logger.log(`Creating package: ${JSON.stringify(createPackageDto)}`);
        const supabase = this.supabaseService.getClient();
        const insertData = {
            name: createPackageDto.name,
            description: createPackageDto.description,
            category_id: createPackageDto.category_id,
            division_id: createPackageDto.division_id,
            variation_group_code: createPackageDto.variation_group_code,
            quantity_basis: createPackageDto.quantity_basis,
            is_deleted: createPackageDto.is_deleted || false,
        };
        const { data: packageData, error: packageError } = await supabase
            .from('packages')
            .insert([insertData])
            .select('id')
            .single();
        if (packageError) {
            this.logger.error(`Error creating package: ${packageError.message}`, packageError.stack);
            this.handleDatabaseError(packageError, createPackageDto);
        }
        if (!packageData) {
            this.logger.error('Package creation did not return data unexpectedly after a successful insert.');
            throw new common_1.InternalServerErrorException('Failed to create package: No data returned.');
        }
        this.logger.log(`Package created successfully with ID: ${packageData.id}`);
        return packageData.id;
    }
    async updatePackage(id, updatePackageDto) {
        this.logger.log(`Updating package ID ${id} with data: ${JSON.stringify(updatePackageDto)}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {
            name: updatePackageDto.name,
            description: updatePackageDto.description,
            category_id: updatePackageDto.category_id,
            division_id: updatePackageDto.division_id,
            variation_group_code: updatePackageDto.variation_group_code,
            quantity_basis: updatePackageDto.quantity_basis,
            is_deleted: updatePackageDto.is_deleted,
        };
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === undefined) {
                delete updateData[key];
            }
        });
        const { data: packageData, error: packageError } = await supabase
            .from('packages')
            .update(updateData)
            .eq('id', id)
            .select('*')
            .single();
        if (packageError) {
            this.logger.error(`Error updating package ${id}: ${packageError.message}`, packageError.stack);
            this.handleUpdateError(packageError, id);
        }
        if (!packageData) {
            this.logger.warn(`Package with ID ${id} not found for update.`);
            throw new common_1.NotFoundException(`Package with ID ${id} not found.`);
        }
        this.logger.log(`Package ${id} updated successfully`);
        return packageData;
    }
    async deletePackage(id) {
        this.logger.log(`Soft deleting package with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from('packages')
            .update({ is_deleted: true })
            .match({ id: id, is_deleted: false });
        if (error) {
            this.logger.error(`Error soft deleting package with ID ${id}: ${error.message}`, error.stack);
            if (error instanceof supabase_js_1.PostgrestError && error.code === '23503') {
                throw new common_1.ConflictException(`Cannot delete package ${id} as it is referenced elsewhere. Details: ${error.details}`);
            }
            throw new common_1.InternalServerErrorException('Failed to remove package.');
        }
        if (count === 0) {
            this.logger.warn(`Package with ID ${id} not found or already deleted for soft deletion.`);
            throw new common_1.NotFoundException(`Package with ID ${id} not found or already deleted.`);
        }
        this.logger.log(`Package with ID ${id} soft deleted successfully.`);
    }
    async updatePackageStatus(id, isActive) {
        this.logger.log(`Updating status for package ID ${id} to ${isActive ? 'active' : 'inactive'}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('packages')
            .update({
            is_deleted: !isActive,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error updating status for package ${id}: ${error.message}`, error.stack);
            if (error instanceof supabase_js_1.PostgrestError && error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Package with ID ${id} not found.`);
            }
            throw new common_1.InternalServerErrorException('Failed to update package status.');
        }
        if (!data) {
            this.logger.warn(`Package with ID ${id} not found for status update.`);
            throw new common_1.NotFoundException(`Package with ID ${id} not found.`);
        }
        this.logger.log(`Package status updated successfully for ID: ${id}`);
        return data;
    }
    handleDatabaseError(error, createPackageDto) {
        if (error.code === '23505') {
            throw new common_1.ConflictException(`Package creation failed due to a unique constraint violation. Check name or other unique fields. Details: ${error.details}`);
        }
        if (error.code === '23503') {
            if (error.details?.includes('category_id')) {
                throw new common_1.BadRequestException(`Invalid category ID provided: ${createPackageDto.category_id}`);
            }
            if (error.details?.includes('division_id')) {
                throw new common_1.BadRequestException(`Invalid division ID provided: ${createPackageDto.division_id}`);
            }
            throw new common_1.BadRequestException(`Package creation failed due to an invalid foreign key. Details: ${error.details}`);
        }
        throw new common_1.InternalServerErrorException(`Failed to create package: ${error?.message || 'Unknown database error'}`);
    }
    handleUpdateError(error, id) {
        if (error.code === 'PGRST116') {
            throw new common_1.NotFoundException(`Package with ID ${id} not found for update.`);
        }
        if (error.code === '23505') {
            throw new common_1.ConflictException(`Update failed due to unique constraint violation. Details: ${error.details}`);
        }
        if (error.code === '23503') {
            throw new common_1.BadRequestException(`Update failed due to invalid foreign key. Details: ${error.details}`);
        }
        throw new common_1.InternalServerErrorException('Failed to update package.');
    }
};
exports.PackageCrudService = PackageCrudService;
exports.PackageCrudService = PackageCrudService = PackageCrudService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageCrudService);
//# sourceMappingURL=package-crud.service.js.map