"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesModule = void 0;
const common_1 = require("@nestjs/common");
const packages_service_1 = require("./packages.service");
const packages_controller_1 = require("./packages.controller");
const package_catalog_service_1 = require("./services/package-catalog.service");
const package_catalog_controller_1 = require("./controllers/package-catalog.controller");
const auth_module_1 = require("../auth/auth.module");
const categories_module_1 = require("../categories/categories.module");
const cities_module_1 = require("../cities/cities.module");
let PackagesModule = class PackagesModule {
};
exports.PackagesModule = PackagesModule;
exports.PackagesModule = PackagesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            categories_module_1.CategoriesModule,
            cities_module_1.CitiesModule,
        ],
        controllers: [
            packages_controller_1.PackagesController,
            package_catalog_controller_1.PackageCatalogController,
        ],
        providers: [
            packages_service_1.PackagesService,
            package_catalog_service_1.PackageCatalogService,
        ],
        exports: [
            packages_service_1.PackagesService,
            package_catalog_service_1.PackageCatalogService,
        ],
    })
], PackagesModule);
//# sourceMappingURL=packages.module.js.map