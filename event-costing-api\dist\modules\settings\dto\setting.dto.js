"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class SettingDto {
    key;
    value;
    description;
    created_at;
    updated_at;
}
exports.SettingDto = SettingDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique key for the setting',
        example: 'site_name',
    }),
    __metadata("design:type", String)
], SettingDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The value of the setting (can be any JSON type)',
        example: { name: 'My Event Co', version: 1.2 },
    }),
    __metadata("design:type", Object)
], SettingDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional description for the setting',
    }),
    __metadata("design:type", Object)
], SettingDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the setting was created' }),
    __metadata("design:type", Date)
], SettingDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the setting was last updated' }),
    __metadata("design:type", Date)
], SettingDto.prototype, "updated_at", void 0);
//# sourceMappingURL=setting.dto.js.map