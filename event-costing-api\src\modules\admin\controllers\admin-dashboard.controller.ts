import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  Api<PERSON><PERSON><PERSON>,
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { AdminDashboardService } from '../services/admin-dashboard.service';
import { AdminDashboardDataDto } from '../dto/admin-dashboard-data.dto';
import { AdminDashboardFiltersDto } from '../dto/admin-dashboard-filters.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from '../../auth/guards/admin-role.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';

/**
 * Controller for consolidated admin dashboard operations
 * Implements consolidated admin dashboard endpoints
 */
@ApiTags('Admin Dashboard')
@ApiBearerAuth()
@Controller('admin/dashboard')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminDashboardController {
  private readonly logger = new Logger(AdminDashboardController.name);

  constructor(
    private readonly adminDashboardService: AdminDashboardService,
  ) {}

  /**
   * Get complete admin dashboard data in a single API call
   * Replaces the need for multiple separate API calls for admin dashboard
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get complete admin dashboard data',
    description: 'Consolidated endpoint that returns system overview, statistics, recent activity, and all reference data in a single response. Replaces the need for multiple separate API calls for admin dashboard.'
  })
  @ApiOkResponse({ 
    description: 'Complete admin dashboard data with metadata',
    type: AdminDashboardDataDto 
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load admin dashboard data.',
  })
  @ApiQuery({ name: 'timePeriod', required: false, description: 'Time period for activity and statistics' })
  @ApiQuery({ name: 'activityDays', required: false, description: 'Number of days for recent activity' })
  @ApiQuery({ name: 'viewMode', required: false, description: 'Dashboard view mode' })
  @ApiQuery({ name: 'includeStatistics', required: false, description: 'Include detailed statistics' })
  @ApiQuery({ name: 'includeActivity', required: false, description: 'Include recent activity' })
  @ApiQuery({ name: 'includeHealth', required: false, description: 'Include system health metrics' })
  @ApiQuery({ name: 'includePerformance', required: false, description: 'Include performance metrics' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'Filter by specific category ID' })
  @ApiQuery({ name: 'cityId', required: false, description: 'Filter by specific city ID' })
  @ApiQuery({ name: 'divisionId', required: false, description: 'Filter by specific division ID' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by specific user ID' })
  @ApiQuery({ name: 'dateStart', required: false, description: 'Custom date range start' })
  @ApiQuery({ name: 'dateEnd', required: false, description: 'Custom date range end' })
  @ApiQuery({ name: 'refreshInterval', required: false, description: 'Refresh interval in seconds' })
  @ApiQuery({ name: 'includeCacheMetrics', required: false, description: 'Include cache performance metrics' })
  @ApiQuery({ name: 'includeDatabaseMetrics', required: false, description: 'Include database performance metrics' })
  @ApiQuery({ name: 'includeApiMetrics', required: false, description: 'Include API usage metrics' })
  @ApiQuery({ name: 'activityLimit', required: false, description: 'Limit for recent activity items' })
  @ApiQuery({ name: 'includeUserActivity', required: false, description: 'Include user activity breakdown' })
  @ApiQuery({ name: 'includeAlerts', required: false, description: 'Include system alerts and warnings' })
  @ApiQuery({ name: 'includeTrends', required: false, description: 'Include growth trends and analytics' })
  async getDashboardData(
    @Query() filters: AdminDashboardFiltersDto,
    @GetCurrentUser() user: User,
  ): Promise<AdminDashboardDataDto> {
    this.logger.log(`Admin ${user.email} requesting dashboard data`);

    return this.adminDashboardService.getAdminDashboardData(filters, user);
  }

  /**
   * Get admin dashboard summary statistics
   * Provides quick overview metrics for admin dashboard
   */
  @Get('summary')
  @ApiOperation({ 
    summary: 'Get admin dashboard summary statistics',
    description: 'Returns quick overview statistics for admin dashboard including system health and key metrics.'
  })
  @ApiOkResponse({ 
    description: 'Admin dashboard summary statistics',
    schema: {
      type: 'object',
      properties: {
        systemHealth: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'healthy' },
            uptime: { type: 'number', example: 86400 },
            lastCheck: { type: 'string', format: 'date-time' },
          },
        },
        quickStats: {
          type: 'object',
          properties: {
            totalPackages: { type: 'number', example: 150 },
            totalTemplates: { type: 'number', example: 25 },
            totalCalculations: { type: 'number', example: 500 },
            totalUsers: { type: 'number', example: 15 },
            activeUsers: { type: 'number', example: 12 },
            recentActivity: { type: 'number', example: 45 },
          },
        },
        performance: {
          type: 'object',
          properties: {
            avgResponseTime: { type: 'number', example: 120 },
            errorRate: { type: 'number', example: 0.02 },
            cacheHitRate: { type: 'number', example: 0.85 },
          },
        },
        alerts: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'warning' },
              message: { type: 'string', example: 'High memory usage detected' },
              timestamp: { type: 'string', format: 'date-time' },
            },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            loadTime: { type: 'number' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getDashboardSummary(
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`Admin ${user.email} requesting dashboard summary`);

    const startTime = Date.now();

    // Get basic dashboard data
    const dashboardData = await this.adminDashboardService.getAdminDashboardData({}, user);

    // Calculate summary metrics
    const quickStats = {
      totalPackages: dashboardData.overview.totalPackages,
      totalTemplates: dashboardData.overview.totalTemplates,
      totalCalculations: dashboardData.overview.totalCalculations,
      totalUsers: dashboardData.overview.totalUsers,
      activeUsers: dashboardData.users.activeCount,
      recentActivity: dashboardData.recentActivity.length,
    };

    // Mock system health data (would be replaced with real monitoring)
    const systemHealth = {
      status: 'healthy',
      uptime: 86400, // 24 hours in seconds
      lastCheck: new Date().toISOString(),
    };

    // Mock performance data (would be replaced with real metrics)
    const performance = {
      avgResponseTime: dashboardData.metadata.loadTime,
      errorRate: dashboardData.metadata.errors?.length ? 0.05 : 0.01,
      cacheHitRate: 0.85,
    };

    // Mock alerts (would be replaced with real alerting system)
    const alerts = dashboardData.metadata.errors?.map(error => ({
      type: 'error',
      message: error,
      timestamp: new Date().toISOString(),
    })) || [];

    const loadTime = Date.now() - startTime;

    return {
      systemHealth,
      quickStats,
      performance,
      alerts,
      metadata: {
        loadTime,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Get admin dashboard health check
   * Provides system health status for monitoring
   */
  @Get('health')
  @ApiOperation({ 
    summary: 'Get admin dashboard health check',
    description: 'Returns system health status and basic connectivity checks for admin monitoring.'
  })
  @ApiOkResponse({ 
    description: 'System health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', format: 'date-time' },
        checks: {
          type: 'object',
          properties: {
            database: { type: 'string', example: 'healthy' },
            cache: { type: 'string', example: 'healthy' },
            storage: { type: 'string', example: 'healthy' },
            api: { type: 'string', example: 'healthy' },
          },
        },
        metrics: {
          type: 'object',
          properties: {
            responseTime: { type: 'number', example: 45 },
            memoryUsage: { type: 'number', example: 0.65 },
            cpuUsage: { type: 'number', example: 0.25 },
          },
        },
      },
    },
  })
  async getHealthCheck(
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`Admin ${user.email} requesting health check`);

    const startTime = Date.now();

    // Perform basic health checks
    const checks = {
      database: 'healthy', // Would check database connectivity
      cache: 'healthy',    // Would check cache connectivity
      storage: 'healthy',  // Would check storage connectivity
      api: 'healthy',      // Would check API responsiveness
    };

    // Mock system metrics (would be replaced with real monitoring)
    const metrics = {
      responseTime: Date.now() - startTime,
      memoryUsage: 0.65, // 65% memory usage
      cpuUsage: 0.25,    // 25% CPU usage
    };

    // Determine overall status
    const allHealthy = Object.values(checks).every(status => status === 'healthy');
    const status = allHealthy ? 'healthy' : 'degraded';

    return {
      status,
      timestamp: new Date().toISOString(),
      checks,
      metrics,
    };
  }
}
