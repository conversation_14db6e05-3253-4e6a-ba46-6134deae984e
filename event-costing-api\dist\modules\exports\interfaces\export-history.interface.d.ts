import { ExportFormat } from '../enums/export-format.enum';
import { ExportStatus } from '../enums/export-status.enum';
export interface ExportHistory {
    id: string;
    calculation_id: string;
    created_by: string;
    created_at: string;
    export_type: ExportFormat;
    status: ExportStatus;
    recipient?: string | null;
    file_name?: string | null;
    storage_path?: string | null;
    error_message?: string | null;
    completed_at?: string | null;
}
