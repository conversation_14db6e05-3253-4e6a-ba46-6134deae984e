"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatesModule = void 0;
const common_1 = require("@nestjs/common");
const templates_service_1 = require("./templates.service");
const templates_controller_1 = require("./templates.controller");
const admin_module_1 = require("../auth/admin.module");
const admin_templates_controller_1 = require("./admin-templates.controller");
const calculations_module_1 = require("../calculations/calculations.module");
const auth_module_1 = require("../auth/auth.module");
const packages_module_1 = require("../packages/packages.module");
const categories_module_1 = require("../categories/categories.module");
const services_1 = require("./services");
const template_consolidated_service_1 = require("./services/template-consolidated.service");
const template_management_controller_1 = require("./controllers/template-management.controller");
let TemplatesModule = class TemplatesModule {
};
exports.TemplatesModule = TemplatesModule;
exports.TemplatesModule = TemplatesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            admin_module_1.AdminModule,
            calculations_module_1.CalculationsModule,
            packages_module_1.PackagesModule,
            categories_module_1.CategoriesModule,
        ],
        controllers: [
            templates_controller_1.TemplatesController,
            admin_templates_controller_1.AdminTemplatesController,
            template_management_controller_1.TemplateManagementController,
        ],
        providers: [
            templates_service_1.TemplatesService,
            services_1.TemplateQueryService,
            services_1.TemplateCreationService,
            services_1.TemplateDetailService,
            services_1.TemplateAdminService,
            services_1.TemplateVenueService,
            services_1.TemplateCalculationService,
            template_consolidated_service_1.TemplateConsolidatedService,
        ],
        exports: [
            templates_service_1.TemplatesService,
            template_consolidated_service_1.TemplateConsolidatedService,
        ],
    })
], TemplatesModule);
//# sourceMappingURL=templates.module.js.map