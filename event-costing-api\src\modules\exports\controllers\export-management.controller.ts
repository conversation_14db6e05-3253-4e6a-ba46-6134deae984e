import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';
import { ExportManagementService } from '../services/export-management.service';
import { ExportManagementDataDto } from '../dto/export-management-data.dto';
import { ExportManagementFiltersDto } from '../dto/export-management-filters.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import { ExportFormat } from '../enums/export-format.enum';

/**
 * Controller for consolidated export management operations
 * Implements consolidated export management endpoints
 */
@ApiTags('Export Management')
@ApiBearerAuth()
@Controller('exports/management')
@UseGuards(JwtAuthGuard)
export class ExportManagementController {
  private readonly logger = new Logger(ExportManagementController.name);

  constructor(
    private readonly exportManagementService: ExportManagementService,
  ) {}

  /**
   * Get complete export management data in a single API call
   * Replaces the need for multiple separate API calls for export management
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get complete export management data',
    description: 'Consolidated endpoint that returns user exports, calculations, statistics, and recent activity in a single response. Replaces the need for multiple separate API calls for export management.'
  })
  @ApiOkResponse({ 
    description: 'Complete export management data with metadata',
    type: ExportManagementDataDto 
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load export management data.',
  })
  @ApiQuery({ name: 'calculationId', required: false, description: 'Filter by calculation ID' })
  @ApiQuery({ name: 'format', required: false, description: 'Filter by export format' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by export status' })
  @ApiQuery({ name: 'dateStart', required: false, description: 'Filter by date range - start date' })
  @ApiQuery({ name: 'dateEnd', required: false, description: 'Filter by date range - end date' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for calculation name' })
  @ApiQuery({ name: 'failedOnly', required: false, description: 'Include only failed exports' })
  @ApiQuery({ name: 'completedOnly', required: false, description: 'Include only completed exports' })
  @ApiQuery({ name: 'pendingOnly', required: false, description: 'Include only pending/processing exports' })
  @ApiQuery({ name: 'activityDays', required: false, description: 'Number of days for recent activity' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number for pagination' })
  @ApiQuery({ name: 'pageSize', required: false, description: 'Number of items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' })
  @ApiQuery({ name: 'includeStatistics', required: false, description: 'Include export statistics' })
  @ApiQuery({ name: 'includeActivity', required: false, description: 'Include recent activity' })
  @ApiQuery({ name: 'includeCalculations', required: false, description: 'Include calculation details' })
  @ApiQuery({ name: 'exportIds', required: false, description: 'Filter by specific export IDs (comma-separated)' })
  @ApiQuery({ name: 'minFileSize', required: false, description: 'Filter by minimum file size in bytes' })
  @ApiQuery({ name: 'maxFileSize', required: false, description: 'Filter by maximum file size in bytes' })
  @ApiQuery({ name: 'downloadableOnly', required: false, description: 'Include exports with download URLs only' })
  @ApiQuery({ name: 'withErrorsOnly', required: false, description: 'Include exports with errors only' })
  @ApiQuery({ name: 'groupByFormat', required: false, description: 'Group results by format' })
  @ApiQuery({ name: 'groupByStatus', required: false, description: 'Group results by status' })
  @ApiQuery({ name: 'includePerformanceMetrics', required: false, description: 'Include performance metrics' })
  async getManagementData(
    @Query() filters: ExportManagementFiltersDto,
    @GetCurrentUser() user: User,
  ): Promise<ExportManagementDataDto> {
    this.logger.log(`User ${user.email} requesting export management data`);

    // Handle comma-separated arrays in query parameters
    if (typeof filters.exportIds === 'string') {
      filters.exportIds = (filters.exportIds as string).split(',').filter(id => id.trim());
    }

    return this.exportManagementService.getExportManagementData(filters, user);
  }

  /**
   * Initiate multiple exports in batch
   * Allows creating multiple exports for different calculations or formats
   */
  @Post('batch')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ 
    summary: 'Initiate multiple exports in batch',
    description: 'Creates multiple export jobs for different calculations or formats in a single request. Useful for bulk export operations.'
  })
  @ApiOkResponse({ 
    description: 'Batch export initiation results',
    schema: {
      type: 'object',
      properties: {
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              calculationId: { type: 'string', format: 'uuid' },
              exportId: { type: 'string', format: 'uuid' },
              status: { type: 'string', example: 'initiated' },
            },
          },
        },
        summary: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 5 },
            successful: { type: 'number', example: 4 },
            failed: { type: 'number', example: 1 },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            timestamp: { type: 'string', format: 'date-time' },
            userId: { type: 'string', format: 'uuid' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid batch export request.',
  })
  async initiateBatchExports(
    @Body() requests: Array<{
      calculationId: string;
      format: ExportFormat;
      recipient?: string;
    }>,
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`User ${user.email} initiating batch export for ${requests.length} items`);

    const results = await this.exportManagementService.initiateBatchExports(requests, user);

    const successful = results.filter(r => r.status === 'initiated').length;
    const failed = results.filter(r => r.status === 'failed').length;

    return {
      results,
      summary: {
        total: requests.length,
        successful,
        failed,
      },
      metadata: {
        timestamp: new Date().toISOString(),
        userId: user.id,
      },
    };
  }

  /**
   * Get export management summary statistics
   * Provides overview metrics for export management
   */
  @Get('summary')
  @ApiOperation({ 
    summary: 'Get export management summary statistics',
    description: 'Returns summary statistics about export management including counts by format, status, and recent activity metrics.'
  })
  @ApiOkResponse({ 
    description: 'Export management summary statistics',
    schema: {
      type: 'object',
      properties: {
        overview: {
          type: 'object',
          properties: {
            totalExports: { type: 'number', example: 50 },
            completedExports: { type: 'number', example: 45 },
            failedExports: { type: 'number', example: 3 },
            pendingExports: { type: 'number', example: 2 },
            successRate: { type: 'number', example: 0.9 },
          },
        },
        formatBreakdown: {
          type: 'object',
          properties: {
            pdf: { type: 'number', example: 30 },
            xlsx: { type: 'number', example: 15 },
            csv: { type: 'number', example: 5 },
          },
        },
        recentActivity: {
          type: 'object',
          properties: {
            exportsToday: { type: 'number', example: 5 },
            exportsThisWeek: { type: 'number', example: 20 },
            exportsThisMonth: { type: 'number', example: 45 },
            averageExportsPerDay: { type: 'number', example: 1.5 },
          },
        },
        performance: {
          type: 'object',
          properties: {
            averageProcessingTime: { type: 'number', example: 45 },
            fastestExport: { type: 'number', example: 15 },
            slowestExport: { type: 'number', example: 120 },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            loadTime: { type: 'number' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getManagementSummary(
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`User ${user.email} requesting export management summary`);

    const startTime = Date.now();

    // Get basic management data
    const managementData = await this.exportManagementService.getExportManagementData({}, user);

    // Calculate summary metrics
    const statistics = managementData.statistics;
    const successRate = statistics.totalExports > 0 
      ? statistics.completedExports / statistics.totalExports 
      : 0;

    // Calculate recent activity metrics
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const recentExports = managementData.exports.data;
    const exportsToday = recentExports.filter(exp => 
      new Date(exp.created_at) >= today
    ).length;
    const exportsThisWeek = recentExports.filter(exp => 
      new Date(exp.created_at) >= thisWeek
    ).length;
    const exportsThisMonth = recentExports.filter(exp => 
      new Date(exp.created_at) >= thisMonth
    ).length;

    const daysInMonth = now.getDate();
    const averageExportsPerDay = daysInMonth > 0 ? exportsThisMonth / daysInMonth : 0;

    const loadTime = Date.now() - startTime;

    return {
      overview: {
        totalExports: statistics.totalExports,
        completedExports: statistics.completedExports,
        failedExports: statistics.failedExports,
        pendingExports: statistics.pendingExports,
        successRate: Math.round(successRate * 100) / 100,
      },
      formatBreakdown: statistics.exportsByFormat,
      recentActivity: {
        exportsToday,
        exportsThisWeek,
        exportsThisMonth,
        averageExportsPerDay: Math.round(averageExportsPerDay * 100) / 100,
      },
      performance: {
        averageProcessingTime: 45, // Mock data - would be calculated from actual processing times
        fastestExport: 15,
        slowestExport: 120,
      },
      metadata: {
        loadTime,
        timestamp: new Date().toISOString(),
      },
    };
  }
}
