"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CalculationItemsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationItemsController = void 0;
const common_1 = require("@nestjs/common");
const calculation_items_service_1 = require("./calculation-items.service");
const add_package_line_item_dto_1 = require("./dto/add-package-line-item.dto");
const add_custom_line_item_dto_1 = require("./dto/add-custom-line-item.dto");
const update_line_item_dto_1 = require("./dto/update-line-item.dto");
const line_item_dto_1 = require("./dto/line-item.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const calculations_service_1 = require("../calculations/calculations.service");
const item_id_response_dto_1 = require("./dto/item-id-response.dto");
let CalculationItemsController = CalculationItemsController_1 = class CalculationItemsController {
    calculationItemsService;
    calculationsService;
    logger = new common_1.Logger(CalculationItemsController_1.name);
    constructor(calculationItemsService, calculationsService) {
        this.calculationItemsService = calculationItemsService;
        this.calculationsService = calculationsService;
    }
    async checkOwnership(calcId, user) {
        this.logger.debug(`Checking ownership for calcId: ${calcId}, userId: ${user.id}`);
        await this.calculationsService.checkCalculationOwnership(calcId, user.id);
        this.logger.debug(`Ownership confirmed for calcId: ${calcId}`);
    }
    async getCalculationItems(calcId, user) {
        this.logger.log(`User ${user.email} fetching line items for calculation ID: ${calcId}`);
        await this.checkOwnership(calcId, user);
        return this.calculationItemsService.getCalculationItems(calcId);
    }
    async getLineItemById(calcId, itemId, user) {
        this.logger.log(`User ${user.email} fetching line item ID: ${itemId} for calculation ID: ${calcId}`);
        await this.checkOwnership(calcId, user);
        return this.calculationItemsService.getLineItemById(calcId, itemId);
    }
    async addPackageLineItem(calcId, addDto, user) {
        this.logger.log(`User ${user.email} adding package ${addDto.packageId} to calc ${calcId}`);
        await this.checkOwnership(calcId, user);
        const itemId = await this.calculationItemsService.addPackageLineItem(calcId, addDto, user);
        return itemId;
    }
    async addCustomLineItem(calcId, addDto, user) {
        this.logger.log(`User ${user.email} adding custom item '${addDto.itemName}' to calc ${calcId}`);
        await this.checkOwnership(calcId, user);
        const itemId = await this.calculationItemsService.addCustomLineItem(calcId, addDto, user);
        return itemId;
    }
    async updateLineItem(calcId, itemId, updateDto, user) {
        this.logger.log(`User ${user.email} updating line item ${itemId} in calc ${calcId}`);
        await this.checkOwnership(calcId, user);
        return this.calculationItemsService.updateLineItem(calcId, itemId, updateDto, user);
    }
    async deletePackageLineItem(calcId, itemId, user) {
        this.logger.log(`User ${user.email} deleting package item ${itemId} from calc ${calcId}`);
        await this.checkOwnership(calcId, user);
        await this.calculationItemsService.deletePackageLineItem(calcId, itemId, user);
    }
    async deleteCustomLineItem(calcId, itemId, user) {
        this.logger.log(`User ${user.email} deleting custom item ${itemId} from calc ${calcId}`);
        await this.checkOwnership(calcId, user);
        await this.calculationItemsService.deleteCustomLineItem(calcId, itemId, user);
    }
};
exports.CalculationItemsController = CalculationItemsController;
__decorate([
    (0, common_1.Get)('items'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all line items for a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'List of line items', type: [line_item_dto_1.LineItemDto] }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "getCalculationItems", null);
__decorate([
    (0, common_1.Get)('items/:itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific line item by ID' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Line item details', type: line_item_dto_1.LineItemDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Line item not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "getLineItemById", null);
__decorate([
    (0, common_1.Post)('items/package'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a package line item to a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Package item added', type: item_id_response_dto_1.ItemIdResponse }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input or package/option not found.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_package_line_item_dto_1.AddPackageLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "addPackageLineItem", null);
__decorate([
    (0, common_1.Post)('items/custom'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a custom line item to a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Custom item added', type: item_id_response_dto_1.ItemIdResponse }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_custom_line_item_dto_1.AddCustomLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "addCustomLineItem", null);
__decorate([
    (0, common_1.Put)('items/:itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a line item (package or custom)' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Line item updated', type: line_item_dto_1.LineItemDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or item not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_line_item_dto_1.UpdateLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "updateLineItem", null);
__decorate([
    (0, common_1.Delete)('items/package/:itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a package line item from a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Package item deleted.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or item not found or access denied.',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "deletePackageLineItem", null);
__decorate([
    (0, common_1.Delete)('items/custom/:itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a custom line item from a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Custom item deleted.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or item not found or access denied.',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CalculationItemsController.prototype, "deleteCustomLineItem", null);
exports.CalculationItemsController = CalculationItemsController = CalculationItemsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Calculation Items'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('calculations/:calcId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => calculations_service_1.CalculationsService))),
    __metadata("design:paramtypes", [calculation_items_service_1.CalculationItemsService,
        calculations_service_1.CalculationsService])
], CalculationItemsController);
//# sourceMappingURL=calculation-items.controller.js.map