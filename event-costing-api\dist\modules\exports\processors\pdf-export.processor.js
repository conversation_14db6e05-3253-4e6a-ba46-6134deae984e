"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PdfExportProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PdfExportProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const fs = require("fs");
const path = require("path");
const os = require("os");
const exports_service_1 = require("../exports.service");
const export_storage_service_1 = require("../services/export-storage.service");
const export_generation_service_1 = require("../services/export-generation.service");
const calculations_service_1 = require("../../calculations/calculations.service");
const export_status_enum_1 = require("../enums/export-status.enum");
let PdfExportProcessor = PdfExportProcessor_1 = class PdfExportProcessor extends bullmq_1.WorkerHost {
    exportsService;
    storageService;
    generationService;
    calculationsService;
    logger = new common_1.Logger(PdfExportProcessor_1.name);
    constructor(exportsService, storageService, generationService, calculationsService) {
        super();
        this.exportsService = exportsService;
        this.storageService = storageService;
        this.generationService = generationService;
        this.calculationsService = calculationsService;
    }
    async process(job) {
        const { exportHistoryId, calculationId, userId } = job.data;
        this.logger.log(`[PDF] Processing job ${job.id} for export history ${exportHistoryId}`);
        this.logger.log(`[PDF] Calculation: ${calculationId}, User: ${userId}`);
        let tempFilePath = undefined;
        let generatedFileName = '';
        let storagePath = undefined;
        try {
            this.logger.log(`[PDF] Updating status to PROCESSING for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.PROCESSING);
            this.logger.log(`[PDF] Fetching calculation data for ${calculationId}`);
            const calculationData = await this.calculationsService.findCalculationForExport(calculationId, userId);
            if (!calculationData) {
                throw new common_1.InternalServerErrorException(`[PDF] Calculation data not found for ID: ${calculationId}`);
            }
            this.logger.log(`[PDF] Calculation data fetched successfully for ${calculationId}`);
            this.logger.log(`[PDF] Transforming calculation data for ${calculationId}`);
            const transformedData = this.generationService.transformCalculationData(calculationData);
            this.logger.log(`[PDF] Data transformation completed for ${calculationId}`);
            this.logger.log(`[PDF] Generating PDF file for ${calculationId}`);
            const sanitizedCalcName = calculationData.name
                .replace(/[^a-z0-9]/gi, '_')
                .toLowerCase();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            generatedFileName = `${sanitizedCalcName}_${timestamp}.pdf`;
            tempFilePath = path.join(os.tmpdir(), generatedFileName);
            this.logger.log(`[PDF] Creating temporary file at: ${tempFilePath}`);
            await this.generationService.generatePdfToFile(transformedData, tempFilePath);
            this.logger.log(`[PDF] PDF file generated successfully at: ${tempFilePath}`);
            this.logger.log(`[PDF] Reading file buffer from temporary file`);
            const fileBuffer = fs.readFileSync(tempFilePath);
            this.logger.log(`[PDF] File buffer read successfully, size: ${fileBuffer.length} bytes`);
            this.logger.log(`[PDF] Uploading file to storage: ${generatedFileName}`);
            storagePath = await this.storageService.uploadExportFile(userId, generatedFileName, fileBuffer, 'application/pdf');
            this.logger.log(`[PDF] File uploaded successfully to: ${storagePath}`);
            this.logger.log(`[PDF] Updating status to COMPLETED for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.COMPLETED, {
                storagePath: storagePath,
                fileName: generatedFileName,
                fileSize: fileBuffer.length,
                mimeType: 'application/pdf',
            });
            this.logger.log(`[PDF] Job ${job.id} completed successfully for export history ${exportHistoryId}`);
        }
        catch (error) {
            this.logger.error(`[PDF] Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.FAILED, {
                error: error instanceof Error ? error.message : String(error),
                fileName: generatedFileName || undefined,
                storagePath: storagePath || undefined,
            });
            throw error;
        }
        finally {
            if (tempFilePath) {
                try {
                    fs.unlinkSync(tempFilePath);
                    this.logger.log(`[PDF] Temporary file deleted: ${tempFilePath}`);
                }
                catch (unlinkErr) {
                    this.logger.warn(`[PDF] Could not delete temporary file ${tempFilePath}: ${unlinkErr instanceof Error ? unlinkErr.message : String(unlinkErr)}`);
                }
            }
        }
    }
};
exports.PdfExportProcessor = PdfExportProcessor;
exports.PdfExportProcessor = PdfExportProcessor = PdfExportProcessor_1 = __decorate([
    (0, bullmq_1.Processor)('pdf-exports'),
    __metadata("design:paramtypes", [exports_service_1.ExportsService,
        export_storage_service_1.ExportStorageService,
        export_generation_service_1.ExportGenerationService,
        calculations_service_1.CalculationsService])
], PdfExportProcessor);
//# sourceMappingURL=pdf-export.processor.js.map