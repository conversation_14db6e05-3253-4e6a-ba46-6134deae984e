import { SupabaseService } from '../../../core/supabase/supabase.service';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';
export declare class CalculationVenueService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    addVenuesToCalculation(calculationId: string, venueIds: string[]): Promise<void>;
    updateCalculationVenues(calculationId: string, venueIds: string[]): Promise<void>;
    fetchCalculationVenues(calculationId: string): Promise<VenueReferenceDto[]>;
    removeAllVenuesFromCalculation(calculationId: string): Promise<void>;
    hasVenues(calculationId: string): Promise<boolean>;
    getVenueCount(calculationId: string): Promise<number>;
}
