import { CostItemsService } from './cost-items.service';
import { CreateCostItemDto } from './dto/create-cost-item.dto';
import { UpdateCostItemDto } from './dto/update-cost-item.dto';
import { CostItemDto } from './dto/cost-item.dto';
export declare class CostItemsController {
    private readonly costItemsService;
    private readonly logger;
    constructor(costItemsService: CostItemsService);
    create(createCostItemDto: CreateCostItemDto): Promise<CostItemDto>;
    findAll(): Promise<CostItemDto[]>;
    findOne(id: string): Promise<CostItemDto>;
    update(id: string, updateCostItemDto: UpdateCostItemDto): Promise<CostItemDto>;
    remove(id: string): Promise<void>;
}
