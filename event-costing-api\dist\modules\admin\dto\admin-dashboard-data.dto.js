"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDashboardDataDto = exports.AdminDashboardMetadataDto = exports.AdminDashboardFilterInfoDto = exports.RecentActivityDto = exports.UsersOverviewDto = exports.CalculationsOverviewDto = exports.TemplatesOverviewDto = exports.PackagesOverviewDto = exports.SystemOverviewDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const category_dto_1 = require("../../categories/dto/category.dto");
const city_dto_1 = require("../../cities/dto/city.dto");
const division_dto_1 = require("../../divisions/dto/division.dto");
const admin_dashboard_filters_dto_1 = require("./admin-dashboard-filters.dto");
class SystemOverviewDto {
    totalPackages;
    totalTemplates;
    totalCalculations;
    totalUsers;
    totalCategories;
    totalCities;
    totalDivisions;
}
exports.SystemOverviewDto = SystemOverviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of packages in the system',
        example: 150,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalPackages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of templates in the system',
        example: 25,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of calculations in the system',
        example: 500,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalCalculations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of users in the system',
        example: 15,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalUsers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of categories in the system',
        example: 12,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalCategories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of cities in the system',
        example: 8,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalCities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of divisions in the system',
        example: 5,
    }),
    __metadata("design:type", Number)
], SystemOverviewDto.prototype, "totalDivisions", void 0);
class PackagesOverviewDto {
    totalCount;
    activeCount;
    deletedCount;
}
exports.PackagesOverviewDto = PackagesOverviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of packages',
        example: 150,
    }),
    __metadata("design:type", Number)
], PackagesOverviewDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of active packages',
        example: 140,
    }),
    __metadata("design:type", Number)
], PackagesOverviewDto.prototype, "activeCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of deleted packages',
        example: 10,
    }),
    __metadata("design:type", Number)
], PackagesOverviewDto.prototype, "deletedCount", void 0);
class TemplatesOverviewDto {
    totalCount;
    activeCount;
    publicCount;
    privateCount;
}
exports.TemplatesOverviewDto = TemplatesOverviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of templates',
        example: 25,
    }),
    __metadata("design:type", Number)
], TemplatesOverviewDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of active templates',
        example: 20,
    }),
    __metadata("design:type", Number)
], TemplatesOverviewDto.prototype, "activeCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of public templates',
        example: 15,
    }),
    __metadata("design:type", Number)
], TemplatesOverviewDto.prototype, "publicCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of private templates',
        example: 10,
    }),
    __metadata("design:type", Number)
], TemplatesOverviewDto.prototype, "privateCount", void 0);
class CalculationsOverviewDto {
    totalCount;
    draftCount;
    finalizedCount;
}
exports.CalculationsOverviewDto = CalculationsOverviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of calculations',
        example: 500,
    }),
    __metadata("design:type", Number)
], CalculationsOverviewDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of draft calculations',
        example: 350,
    }),
    __metadata("design:type", Number)
], CalculationsOverviewDto.prototype, "draftCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of finalized calculations',
        example: 150,
    }),
    __metadata("design:type", Number)
], CalculationsOverviewDto.prototype, "finalizedCount", void 0);
class UsersOverviewDto {
    totalCount;
    activeCount;
}
exports.UsersOverviewDto = UsersOverviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of users',
        example: 15,
    }),
    __metadata("design:type", Number)
], UsersOverviewDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of active users',
        example: 12,
    }),
    __metadata("design:type", Number)
], UsersOverviewDto.prototype, "activeCount", void 0);
class RecentActivityDto {
    type;
    id;
    name;
    action;
    timestamp;
}
exports.RecentActivityDto = RecentActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of activity',
        example: 'package',
        enum: ['package', 'template', 'calculation', 'user'],
    }),
    __metadata("design:type", String)
], RecentActivityDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the item',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], RecentActivityDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the item',
        example: 'Sound System Basic',
    }),
    __metadata("design:type", String)
], RecentActivityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Action performed',
        example: 'created',
        enum: ['created', 'updated', 'deleted'],
    }),
    __metadata("design:type", String)
], RecentActivityDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the activity',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], RecentActivityDto.prototype, "timestamp", void 0);
class AdminDashboardFilterInfoDto {
    applied;
}
exports.AdminDashboardFilterInfoDto = AdminDashboardFilterInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Applied filters',
        type: admin_dashboard_filters_dto_1.AdminDashboardFiltersDto,
    }),
    __metadata("design:type", admin_dashboard_filters_dto_1.AdminDashboardFiltersDto)
], AdminDashboardFilterInfoDto.prototype, "applied", void 0);
class AdminDashboardMetadataDto {
    loadTime;
    cacheVersion;
    userId;
    errors;
    timestamp;
    dataPoints;
}
exports.AdminDashboardMetadataDto = AdminDashboardMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time taken to load the data in milliseconds',
        example: 450,
    }),
    __metadata("design:type", Number)
], AdminDashboardMetadataDto.prototype, "loadTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache version for the response',
        example: '1.0',
    }),
    __metadata("design:type", String)
], AdminDashboardMetadataDto.prototype, "cacheVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who requested the data',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], AdminDashboardMetadataDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Any errors encountered during data loading',
        type: [String],
        example: [],
    }),
    __metadata("design:type", Array)
], AdminDashboardMetadataDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the data was loaded',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], AdminDashboardMetadataDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data points summary',
        type: Object,
        example: {
            categoriesCount: 12,
            citiesCount: 8,
            divisionsCount: 5,
            packagesCount: 150,
            templatesCount: 25,
            calculationsCount: 500,
            usersCount: 15,
            recentActivityCount: 20,
        },
    }),
    __metadata("design:type", Object)
], AdminDashboardMetadataDto.prototype, "dataPoints", void 0);
class AdminDashboardDataDto {
    overview;
    categories;
    cities;
    divisions;
    packages;
    templates;
    calculations;
    users;
    recentActivity;
    filters;
    metadata;
}
exports.AdminDashboardDataDto = AdminDashboardDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'System overview statistics',
        type: SystemOverviewDto,
    }),
    __metadata("design:type", SystemOverviewDto)
], AdminDashboardDataDto.prototype, "overview", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available categories',
        type: [category_dto_1.CategoryDto],
    }),
    __metadata("design:type", Array)
], AdminDashboardDataDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available cities',
        type: [city_dto_1.CityDto],
    }),
    __metadata("design:type", Array)
], AdminDashboardDataDto.prototype, "cities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available divisions',
        type: [division_dto_1.DivisionDto],
    }),
    __metadata("design:type", Array)
], AdminDashboardDataDto.prototype, "divisions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Packages overview statistics',
        type: PackagesOverviewDto,
    }),
    __metadata("design:type", PackagesOverviewDto)
], AdminDashboardDataDto.prototype, "packages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Templates overview statistics',
        type: TemplatesOverviewDto,
    }),
    __metadata("design:type", TemplatesOverviewDto)
], AdminDashboardDataDto.prototype, "templates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculations overview statistics',
        type: CalculationsOverviewDto,
    }),
    __metadata("design:type", CalculationsOverviewDto)
], AdminDashboardDataDto.prototype, "calculations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Users overview statistics',
        type: UsersOverviewDto,
    }),
    __metadata("design:type", UsersOverviewDto)
], AdminDashboardDataDto.prototype, "users", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recent activity items',
        type: [RecentActivityDto],
    }),
    __metadata("design:type", Array)
], AdminDashboardDataDto.prototype, "recentActivity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter information',
        type: AdminDashboardFilterInfoDto,
    }),
    __metadata("design:type", AdminDashboardFilterInfoDto)
], AdminDashboardDataDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata about the response',
        type: AdminDashboardMetadataDto,
    }),
    __metadata("design:type", AdminDashboardMetadataDto)
], AdminDashboardDataDto.prototype, "metadata", void 0);
//# sourceMappingURL=admin-dashboard-data.dto.js.map