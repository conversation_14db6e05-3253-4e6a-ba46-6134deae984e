import { CalculationItemsService } from './calculation-items.service';
import { AddPackageLineItemDto } from './dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from './dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { LineItemDto } from './dto/line-item.dto';
import { User } from '@supabase/supabase-js';
import { CalculationsService } from '../calculations/calculations.service';
import { ItemIdResponse } from './dto/item-id-response.dto';
export declare class CalculationItemsController {
    private readonly calculationItemsService;
    private readonly calculationsService;
    private readonly logger;
    constructor(calculationItemsService: CalculationItemsService, calculationsService: CalculationsService);
    private checkOwnership;
    getCalculationItems(calcId: string, user: User): Promise<LineItemDto[]>;
    getLineItemById(calcId: string, itemId: string, user: User): Promise<LineItemDto>;
    addPackageLineItem(calcId: string, addDto: AddPackageLineItemDto, user: User): Promise<ItemIdResponse>;
    addCustomLineItem(calcId: string, addDto: AddCustomLineItemDto, user: User): Promise<ItemIdResponse>;
    updateLineItem(calcId: string, itemId: string, updateDto: UpdateLineItemDto, user: User): Promise<LineItemDto>;
    deletePackageLineItem(calcId: string, itemId: string, user: User): Promise<void>;
    deleteCustomLineItem(calcId: string, itemId: string, user: User): Promise<void>;
}
