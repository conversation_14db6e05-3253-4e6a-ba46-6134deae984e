"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CalculationTransformationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationTransformationService = void 0;
const common_1 = require("@nestjs/common");
let CalculationTransformationService = CalculationTransformationService_1 = class CalculationTransformationService {
    logger = new common_1.Logger(CalculationTransformationService_1.name);
    async mapRawToDetailDto(raw, venues = []) {
        if (!raw || !raw.id) {
            throw new common_1.InternalServerErrorException('Invalid raw calculation data for mapping.');
        }
        const currencyDto = raw.currency
            ? { id: raw.currency.id, code: raw.currency.code }
            : null;
        const cityDto = raw.city_id && raw.cities?.name
            ? { id: raw.city_id, name: raw.cities.name }
            : null;
        const clientDto = raw.clients
            ? { id: raw.clients.id, client_name: raw.clients.client_name }
            : null;
        const eventDto = raw.events
            ? { id: raw.events.id, event_name: raw.events.event_name }
            : null;
        const lineItemsDto = this.mapLineItems(raw.calculation_line_items ?? []);
        const customItemsDto = this.mapCustomItems(raw.calculation_custom_items ?? []);
        const dto = {
            id: raw.id,
            name: raw.name,
            currency: currencyDto,
            city: cityDto,
            venues: venues,
            event_start_date: raw.event_start_date,
            event_end_date: raw.event_end_date,
            attendees: raw.attendees,
            event_type_id: raw.event_type_id,
            notes: raw.notes,
            version_notes: raw.version_notes,
            status: raw.status,
            created_at: raw.created_at,
            updated_at: raw.updated_at,
            created_by: raw.created_by,
            client: clientDto,
            event: eventDto,
            line_items: lineItemsDto,
            custom_items: customItemsDto,
            subtotal: raw.subtotal ?? 0,
            taxes: raw.taxes ?? [],
            discount: raw.discount ?? null,
            total: raw.total ?? 0,
            total_cost: raw.total_cost ?? 0,
            estimated_profit: raw.estimated_profit ?? 0,
        };
        return dto;
    }
    mapLineItems(rawLineItems) {
        return rawLineItems.map((item) => ({
            id: item.id,
            package_id: item.package_id,
            item_name_snapshot: item.item_name_snapshot,
            option_summary_snapshot: item.option_summary_snapshot,
            item_quantity: item.item_quantity,
            duration_days: item.duration_days,
            unit_base_price: item.unit_base_price,
            options_total_adjustment: item.options_total_adjustment,
            calculated_line_total: item.calculated_line_total,
            notes: item.notes,
            unit_base_cost_snapshot: item.unit_base_cost_snapshot,
            options_total_cost_snapshot: item.options_total_cost_snapshot,
            calculated_line_cost: item.calculated_line_cost,
            options: this.mapLineItemOptions(item.calculation_line_item_options ?? []),
        }));
    }
    mapLineItemOptions(rawOptions) {
        return rawOptions.map((opt) => ({
            id: opt.option_id,
            option_name_snapshot: opt.package_options?.option_name ?? 'N/A',
            price_adjustment_snapshot: opt.price_adjustment_snapshot,
        }));
    }
    mapCustomItems(rawCustomItems) {
        return rawCustomItems.map((item) => ({
            id: item.id,
            item_name: item.item_name,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            unit_cost: item.unit_cost,
        }));
    }
    mapCalculationSummary(calculation) {
        const venues = calculation.venues?.map((v) => ({
            id: v.venue.id,
            name: v.venue.name,
        })) || [];
        return {
            id: calculation.id,
            name: calculation.name,
            total: calculation.total || 0,
            currency: calculation.currency,
            attendees: calculation.attendees,
            event_type_id: calculation.event_type_id,
            city: calculation.city,
            venues: venues,
        };
    }
    transformVenueData(venues) {
        return venues.map(venue => ({
            id: venue.id,
            name: venue.name,
            address: venue.address,
            city_id: venue.city_id,
            city_name: (venue.cities && venue.cities[0]?.name) || null,
        }));
    }
    validateRawData(raw) {
        if (!raw) {
            this.logger.error('Raw calculation data is null or undefined');
            return false;
        }
        if (!raw.id) {
            this.logger.error('Raw calculation data missing required ID field');
            return false;
        }
        if (!raw.name) {
            this.logger.error('Raw calculation data missing required name field');
            return false;
        }
        return true;
    }
    sanitizeRawData(raw) {
        return {
            ...raw,
            subtotal: raw.subtotal ?? 0,
            total: raw.total ?? 0,
            total_cost: raw.total_cost ?? 0,
            estimated_profit: raw.estimated_profit ?? 0,
            taxes: raw.taxes ?? [],
            discount: raw.discount ?? null,
            calculation_line_items: raw.calculation_line_items ?? [],
            calculation_custom_items: raw.calculation_custom_items ?? [],
        };
    }
    transformForExport(calculation) {
        return {
            basic_info: {
                id: calculation.id,
                name: calculation.name,
                status: calculation.status,
                created_at: calculation.created_at,
                updated_at: calculation.updated_at,
            },
            financial: {
                subtotal: calculation.subtotal,
                total: calculation.total,
                total_cost: calculation.total_cost,
                estimated_profit: calculation.estimated_profit,
                currency: calculation.currency,
            },
            event_details: {
                event_start_date: calculation.event_start_date,
                event_end_date: calculation.event_end_date,
                attendees: calculation.attendees,
                event_type_id: calculation.event_type_id,
                city: calculation.city,
                venues: calculation.venues,
            },
            items: {
                line_items: calculation.line_items,
                custom_items: calculation.custom_items,
            },
            adjustments: {
                taxes: calculation.taxes,
                discount: calculation.discount,
            },
            relationships: {
                client: calculation.client,
                event: calculation.event,
            },
        };
    }
};
exports.CalculationTransformationService = CalculationTransformationService;
exports.CalculationTransformationService = CalculationTransformationService = CalculationTransformationService_1 = __decorate([
    (0, common_1.Injectable)()
], CalculationTransformationService);
//# sourceMappingURL=calculation-transformation.service.js.map