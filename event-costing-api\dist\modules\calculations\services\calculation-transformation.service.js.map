{"version": 3, "file": "calculation-transformation.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/calculations/services/calculation-transformation.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAIwB;AA0BjB,IAAM,gCAAgC,wCAAtC,MAAM,gCAAgC;IAC1B,MAAM,GAAG,IAAI,eAAM,CAAC,kCAAgC,CAAC,IAAI,CAAC,CAAC;IAK5E,KAAK,CAAC,iBAAiB,CACrB,GAA0B,EAC1B,SAA8B,EAAE;QAEhC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;YACpB,MAAM,IAAI,qCAA4B,CACpC,2CAA2C,CAC5C,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAA8B,GAAG,CAAC,QAAQ;YACzD,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;YAClD,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,OAAO,GACX,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI;YAC7B,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;YAC5C,CAAC,CAAC,IAAI,CAAC;QAEX,MAAM,SAAS,GAA4B,GAAG,CAAC,OAAO;YACpD,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE;YAC9D,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,QAAQ,GAA2B,GAAG,CAAC,MAAM;YACjD,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE;YAC1D,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,YAAY,GAA6B,IAAI,CAAC,YAAY,CAC9D,GAAG,CAAC,sBAAsB,IAAI,EAAE,CACjC,CAAC;QAEF,MAAM,cAAc,GAA+B,IAAI,CAAC,cAAc,CACpE,GAAG,CAAC,wBAAwB,IAAI,EAAE,CACnC,CAAC;QAEF,MAAM,GAAG,GAAyB;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,QAAQ,EAAE,WAAW;YACrB,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,MAAM;YACd,gBAAgB,EAAE,GAAG,CAAC,gBAAgB;YACtC,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,MAAM,EAAE,SAAS;YACjB,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,YAAY;YACxB,YAAY,EAAE,cAAc;YAC5B,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,CAAC;YAC3B,KAAK,EAAG,GAAG,CAAC,KAA4B,IAAI,EAAE;YAC9C,QAAQ,EAAG,GAAG,CAAC,QAA8B,IAAI,IAAI;YACrD,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC;YACrB,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC;YAC/B,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,IAAI,CAAC;SAC5C,CAAC;QAEF,OAAO,GAAG,CAAC;IACb,CAAC;IAKO,YAAY,CAClB,YAAsC;QAEtC,OAAO,YAAY,CAAC,GAAG,CACrB,CAAC,IAA4B,EAA0B,EAAE,CAAC,CAAC;YACzD,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,2BAA2B,EAAE,IAAI,CAAC,2BAA2B;YAC7D,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAC9B,IAAI,CAAC,6BAA6B,IAAI,EAAE,CACzC;SACF,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CACxB,UAA0C;QAE1C,OAAO,UAAU,CAAC,GAAG,CACnB,CAAC,GAAiC,EAAgC,EAAE,CAAC,CAAC;YACpE,EAAE,EAAE,GAAG,CAAC,SAAS;YACjB,oBAAoB,EAAE,GAAG,CAAC,eAAe,EAAE,WAAW,IAAI,KAAK;YAC/D,yBAAyB,EAAE,GAAG,CAAC,yBAAyB;SACzD,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,cAAc,CACpB,cAA0C;QAE1C,OAAO,cAAc,CAAC,GAAG,CACvB,CAAC,IAA8B,EAA4B,EAAE,CAAC,CAAC;YAC7D,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CACH,CAAC;IACJ,CAAC;IAKD,qBAAqB,CAAC,WAAgB;QAEpC,MAAM,MAAM,GACV,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;YACnC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;YACd,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI;SACnB,CAAC,CAAC,IAAI,EAAE,CAAC;QAEZ,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC;YAC7B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,MAAM,EAAE,MAAM;SACf,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,MAAa;QAC9B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1B,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,SAAS,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;SAC3D,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,eAAe,CAAC,GAAQ;QACtB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,eAAe,CAAC,GAAQ;QACtB,OAAO;YACL,GAAG,GAAG;YAEN,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,CAAC;YAC3B,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC;YACrB,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC;YAC/B,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,IAAI,CAAC;YAC3C,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;YACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,IAAI;YAC9B,sBAAsB,EAAE,GAAG,CAAC,sBAAsB,IAAI,EAAE;YACxD,wBAAwB,EAAE,GAAG,CAAC,wBAAwB,IAAI,EAAE;SAC7D,CAAC;IACJ,CAAC;IAKD,kBAAkB,CAAC,WAAiC;QAClD,OAAO;YACL,UAAU,EAAE;gBACV,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,UAAU,EAAE,WAAW,CAAC,UAAU;aACnC;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,QAAQ,EAAE,WAAW,CAAC,QAAQ;aAC/B;YACD,aAAa,EAAE;gBACb,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,aAAa,EAAE,WAAW,CAAC,aAAa;gBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;aAC3B;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,YAAY,EAAE,WAAW,CAAC,YAAY;aACvC;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ;aAC/B;YACD,aAAa,EAAE;gBACb,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;aACzB;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA1PY,4EAAgC;2CAAhC,gCAAgC;IAD5C,IAAA,mBAAU,GAAE;GACA,gCAAgC,CA0P5C"}