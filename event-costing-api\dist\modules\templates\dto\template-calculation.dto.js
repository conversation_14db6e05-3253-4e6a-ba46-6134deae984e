"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateCalculationSummaryDto = exports.TemplateCalculationResultDto = exports.CalculationBreakdownDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CalculationBreakdownDto {
    packageId;
    packageName;
    quantity;
    unitPrice;
    totalPrice;
    currency;
    unitCost;
    totalCost;
}
exports.CalculationBreakdownDto = CalculationBreakdownDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package ID',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], CalculationBreakdownDto.prototype, "packageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package name',
        example: 'Catering Service',
    }),
    __metadata("design:type", String)
], CalculationBreakdownDto.prototype, "packageName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantity calculated based on attendees and package basis',
        example: 100,
    }),
    __metadata("design:type", Number)
], CalculationBreakdownDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unit price for this package',
        example: 50000,
    }),
    __metadata("design:type", Number)
], CalculationBreakdownDto.prototype, "unitPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total price for this package (quantity * unitPrice)',
        example: 5000000,
    }),
    __metadata("design:type", Number)
], CalculationBreakdownDto.prototype, "totalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'IDR',
    }),
    __metadata("design:type", String)
], CalculationBreakdownDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unit cost for this package (for profit calculation)',
        example: 35000,
    }),
    __metadata("design:type", Number)
], CalculationBreakdownDto.prototype, "unitCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Total cost for this package (quantity * unitCost)',
        example: 3500000,
    }),
    __metadata("design:type", Number)
], CalculationBreakdownDto.prototype, "totalCost", void 0);
class TemplateCalculationResultDto {
    packagesTotal;
    customItemsTotal;
    grandTotal;
    breakdown;
    currency;
    hasValidPrices;
    missingPrices;
    totalCost;
    estimatedProfit;
}
exports.TemplateCalculationResultDto = TemplateCalculationResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total value of all packages',
        example: 15000000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationResultDto.prototype, "packagesTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total value of custom items (currently always 0)',
        example: 0,
    }),
    __metadata("design:type", Number)
], TemplateCalculationResultDto.prototype, "customItemsTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Grand total (packages + custom items)',
        example: 15000000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationResultDto.prototype, "grandTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detailed breakdown of each package calculation',
        type: [CalculationBreakdownDto],
    }),
    __metadata("design:type", Array)
], TemplateCalculationResultDto.prototype, "breakdown", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code for the calculation',
        example: 'IDR',
    }),
    __metadata("design:type", String)
], TemplateCalculationResultDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether all packages have valid prices',
        example: true,
    }),
    __metadata("design:type", Boolean)
], TemplateCalculationResultDto.prototype, "hasValidPrices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of packages or issues with missing prices',
        example: [],
        type: [String],
    }),
    __metadata("design:type", Array)
], TemplateCalculationResultDto.prototype, "missingPrices", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Total cost of all packages (for profit calculation)',
        example: 10500000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationResultDto.prototype, "totalCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Estimated profit (grandTotal - totalCost)',
        example: 4500000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationResultDto.prototype, "estimatedProfit", void 0);
class TemplateCalculationSummaryDto {
    totalPackages;
    totalValue;
    currency;
    hasValidPrices;
    missingPricesCount;
    averagePackageValue;
    totalCost;
    profitMarginPercentage;
}
exports.TemplateCalculationSummaryDto = TemplateCalculationSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of packages in the template',
        example: 5,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "totalPackages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total value of the template',
        example: 15000000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "totalValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'IDR',
    }),
    __metadata("design:type", String)
], TemplateCalculationSummaryDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether all packages have valid prices',
        example: true,
    }),
    __metadata("design:type", Boolean)
], TemplateCalculationSummaryDto.prototype, "hasValidPrices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of packages with missing prices',
        example: 0,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "missingPricesCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average value per package',
        example: 3000000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "averagePackageValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Total cost (for profit calculation)',
        example: 10500000,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "totalCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Estimated profit margin percentage',
        example: 30,
    }),
    __metadata("design:type", Number)
], TemplateCalculationSummaryDto.prototype, "profitMarginPercentage", void 0);
//# sourceMappingURL=template-calculation.dto.js.map