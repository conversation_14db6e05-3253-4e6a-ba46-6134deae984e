import { PackageDto } from '../dto/package.dto';
export declare class PackageTransformerUtil {
    static transformPackagesWithRelations(packages: any[], categoriesMap: Map<string, string>, divisionsMap: Map<string, string>, pricesMap: Map<string, any>, citiesMap: Map<string, string[]>): PackageDto[];
    static transformSinglePackageWithRelations(pkg: any, categoryName?: string, divisionName?: string, priceInfo?: any, cities?: string[]): PackageDto;
    static extractUniqueIds(packages: any[], field: string): string[];
    static groupPackagesByField(packages: any[], field: string): Map<string, any[]>;
    static filterPackages(packages: any[], criteria: {
        isDeleted?: boolean;
        categoryId?: string;
        divisionId?: string;
        hasName?: string;
    }): any[];
    static sortPackages(packages: any[], field: string, order?: 'asc' | 'desc'): any[];
    static paginatePackages(packages: any[], offset?: number, limit?: number): {
        data: any[];
        count: number;
        offset: number;
        limit: number;
        hasMore: boolean;
    };
    static validatePackageStructure(pkg: any): {
        isValid: boolean;
        errors: string[];
    };
    static cleanPackageData(pkg: any): any;
}
