declare enum PackageQuantityBasis {
    PER_EVENT = "PER_EVENT",
    PER_DAY = "PER_DAY",
    PER_ATTENDEE = "PER_ATTENDEE",
    PER_ITEM = "PER_ITEM",
    PER_ITEM_PER_DAY = "PER_ITEM_PER_DAY",
    PER_ATTENDEE_PER_DAY = "PER_ATTENDEE_PER_DAY"
}
export declare class CreatePackageDto {
    name: string;
    description?: string;
    category_id?: string;
    division_id?: string;
    variation_group_code?: string;
    quantity_basis: PackageQuantityBasis;
    is_deleted?: boolean;
    city_ids?: string[];
    enable_venues?: boolean;
    venue_ids?: string[];
    price?: number;
    unit_base_cost?: number;
    currency_id?: string;
}
export {};
