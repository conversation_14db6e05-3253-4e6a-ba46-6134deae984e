"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageDependenciesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageDependenciesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let PackageDependenciesService = PackageDependenciesService_1 = class PackageDependenciesService {
    supabaseService;
    logger = new common_1.Logger(PackageDependenciesService_1.name);
    TABLE_NAME = 'package_dependencies';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    handlePostgrestError(error, context) {
        this.logger.error(`Postgrest error in PackageDependenciesService (${context}): ${error.message}`, error.details);
        const FK_VIOLATION = '23503';
        const UNIQUE_VIOLATION = '23505';
        const CHECK_VIOLATION = '23514';
        if (error.code === FK_VIOLATION) {
            if (error.details?.includes('package_id')) {
                throw new common_1.NotFoundException(`The package specified (${context === 'create' ? 'package_id' : 'dependent_package_id'}) does not exist. Details: ${error.details}`);
            }
            else if (error.details?.includes('dependent_package_id')) {
                throw new common_1.NotFoundException(`The dependent package specified (${context === 'create' ? 'dependent_package_id' : 'package_id'}) does not exist. Details: ${error.details}`);
            }
            else {
                throw new common_1.NotFoundException(`A related package was not found. Details: ${error.details}`);
            }
        }
        else if (error.code === UNIQUE_VIOLATION) {
            throw new common_1.ConflictException(`This dependency relationship already exists. Details: ${error.details}`);
        }
        else if (error.code === CHECK_VIOLATION) {
            if (error.message.includes('package_cannot_depend_on_itself')) {
                throw new common_1.BadRequestException('A package cannot depend on itself.');
            }
        }
        throw new common_1.InternalServerErrorException(`An unexpected database error occurred (${context}). Details: ${error.message}`);
    }
    async create(packageId, createDto) {
        this.logger.log(`Attempting to create dependency from package ${packageId} to ${createDto.dependent_package_id} of type ${createDto.dependency_type}`);
        if (packageId === createDto.dependent_package_id) {
            this.logger.warn(`Attempt to create self-dependency for package ${packageId} blocked.`);
            throw new common_1.BadRequestException('A package cannot depend on itself.');
        }
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .insert([
            {
                package_id: packageId,
                dependent_package_id: createDto.dependent_package_id,
                dependency_type: createDto.dependency_type,
                description: createDto.description,
            },
        ])
            .select('*')
            .single();
        if (error) {
            this.handlePostgrestError(error, 'create');
        }
        if (!data) {
            this.logger.error('Failed to create package dependency, insert operation returned no data.');
            throw new common_1.InternalServerErrorException('Failed to create package dependency due to an unexpected error.');
        }
        this.logger.log(`Successfully created dependency ${data.id} for package ${packageId}`);
        return data;
    }
    async findAllByPackage(packageId) {
        this.logger.log(`Finding all dependencies for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select(`
        *,
        dependent_package:packages!dependent_package_id(
          name
        )
      `)
            .eq('package_id', packageId)
            .returns();
        if (error) {
            this.handlePostgrestError(error, 'findAllByPackage');
        }
        this.logger.log(`Found ${data?.length ?? 0} dependencies for package ${packageId}`);
        const mappedData = data?.map(item => ({
            ...item,
            dependent_package: item.dependent_package ? {
                name: item.dependent_package.name
            } : null
        })) ?? [];
        return mappedData;
    }
    async remove(dependencyId) {
        this.logger.log(`Attempting to remove dependency with ID ${dependencyId}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.TABLE_NAME)
            .delete({ count: 'exact' })
            .eq('id', dependencyId);
        if (error) {
            this.handlePostgrestError(error, 'remove');
        }
        if (count === 0) {
            this.logger.warn(`Dependency with ID ${dependencyId} not found for deletion.`);
            throw new common_1.NotFoundException(`Dependency with ID ${dependencyId} not found.`);
        }
        if (count === null || count > 1) {
            this.logger.error(`Unexpected number of rows deleted (${count}) for dependency ID ${dependencyId}. Potential data integrity issue?`);
            throw new common_1.InternalServerErrorException('An unexpected error occurred during dependency deletion.');
        }
        this.logger.log(`Dependency with ID ${dependencyId} removed successfully.`);
    }
};
exports.PackageDependenciesService = PackageDependenciesService;
exports.PackageDependenciesService = PackageDependenciesService = PackageDependenciesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageDependenciesService);
//# sourceMappingURL=package-dependencies.service.js.map