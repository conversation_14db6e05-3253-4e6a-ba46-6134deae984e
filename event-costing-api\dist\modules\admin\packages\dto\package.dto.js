"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
var PackageQuantityBasis;
(function (PackageQuantityBasis) {
    PackageQuantityBasis["PER_EVENT"] = "PER_EVENT";
    PackageQuantityBasis["PER_DAY"] = "PER_DAY";
    PackageQuantityBasis["PER_ATTENDEE"] = "PER_ATTENDEE";
    PackageQuantityBasis["PER_ITEM"] = "PER_ITEM";
    PackageQuantityBasis["PER_ITEM_PER_DAY"] = "PER_ITEM_PER_DAY";
    PackageQuantityBasis["PER_ATTENDEE_PER_DAY"] = "PER_ATTENDEE_PER_DAY";
})(PackageQuantityBasis || (PackageQuantityBasis = {}));
let PackageDto = class PackageDto {
    id;
    name;
    description;
    category_id;
    division_id;
    variation_group_code;
    seq_number;
    quantity_basis;
    created_at;
    updated_at;
    created_by;
    updated_by;
    is_deleted;
    categoryName;
    divisionName;
    cityNames;
    venueNames;
    unitBaseCost;
    price;
    currencySymbol;
    hasPricing;
    hasOptions;
    hasDependencies;
};
exports.PackageDto = PackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the package',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the package variation',
        example: 'Standard Catering',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Detailed description of the package' }),
    __metadata("design:type", Object)
], PackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category UUID',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    __metadata("design:type", Object)
], PackageDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Division UUID',
        example: 'e47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    __metadata("design:type", Object)
], PackageDto.prototype, "division_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Code to group variations',
        example: 'CATER_STD_V1',
    }),
    __metadata("design:type", Object)
], PackageDto.prototype, "variation_group_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique sequence number (potentially for ordering)',
        example: 101,
    }),
    __metadata("design:type", Number)
], PackageDto.prototype, "seq_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Basis for quantity calculation',
        enum: PackageQuantityBasis,
        example: PackageQuantityBasis.PER_ATTENDEE,
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the package was created' }),
    __metadata("design:type", String)
], PackageDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the package was last updated' }),
    __metadata("design:type", String)
], PackageDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID who created the package' }),
    __metadata("design:type", String)
], PackageDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User ID who last updated the package',
        nullable: true,
    }),
    __metadata("design:type", Object)
], PackageDto.prototype, "updated_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], PackageDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category name',
        example: 'Catering',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Division name',
        example: 'Food & Beverage',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "divisionName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of city names where this package is available',
        type: [String],
        example: ['Jakarta', 'Bandung'],
    }),
    __metadata("design:type", Array)
], PackageDto.prototype, "cityNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue names where this package is available',
        type: [String],
        example: ['Grand Ballroom', 'Conference Hall'],
    }),
    __metadata("design:type", Array)
], PackageDto.prototype, "venueNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Base unit cost as string for display',
        example: '750000',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "unitBaseCost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price as string for display',
        example: '1500000',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency symbol',
        example: 'Rp',
    }),
    __metadata("design:type", String)
], PackageDto.prototype, "currencySymbol", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Flag indicating if the package has pricing information',
        example: true,
    }),
    __metadata("design:type", Boolean)
], PackageDto.prototype, "hasPricing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Flag indicating if the package has options',
        example: true,
    }),
    __metadata("design:type", Boolean)
], PackageDto.prototype, "hasOptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Flag indicating if the package has dependencies',
        example: false,
    }),
    __metadata("design:type", Boolean)
], PackageDto.prototype, "hasDependencies", void 0);
exports.PackageDto = PackageDto = __decorate([
    (0, swagger_1.ApiExtraModels)()
], PackageDto);
//# sourceMappingURL=package.dto.js.map