"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ExportStorageService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportStorageService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let ExportStorageService = ExportStorageService_1 = class ExportStorageService {
    supabaseService;
    logger = new common_1.Logger(ExportStorageService_1.name);
    BUCKET_NAME = 'calculation-exports';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async uploadExportFile(userId, fileName, fileBuffer, mimeType) {
        const storagePath = `exports/${userId}/${fileName}`;
        this.logger.log(`Attempting to upload file to ${storagePath}`);
        const supabase = this.supabaseService.getClient();
        const { error: uploadError } = await supabase.storage
            .from(this.BUCKET_NAME)
            .upload(storagePath, fileBuffer, {
            upsert: true,
            contentType: mimeType,
        });
        if (uploadError) {
            this.logger.error(`Failed to upload file to Supabase (${storagePath}): ${uploadError.message}`, uploadError.stack);
            throw new common_1.InternalServerErrorException('Failed to upload exported file.');
        }
        this.logger.log(`File uploaded successfully to ${storagePath}`);
        return storagePath;
    }
    async getSignedUrl(storagePath) {
        if (!storagePath) {
            return null;
        }
        this.logger.debug(`Generating signed URL for path: ${storagePath}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: urlData, error: urlError } = await supabase.storage
                .from(this.BUCKET_NAME)
                .createSignedUrl(storagePath, 60 * 60);
            if (urlError) {
                this.logger.error(`Failed to create signed URL for ${storagePath}: ${urlError.message}`);
                return null;
            }
            return urlData?.signedUrl ?? null;
        }
        catch (error) {
            this.logger.error(`Unexpected error generating signed URL for ${storagePath}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            return null;
        }
    }
};
exports.ExportStorageService = ExportStorageService;
exports.ExportStorageService = ExportStorageService = ExportStorageService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], ExportStorageService);
//# sourceMappingURL=export-storage.service.js.map