export declare enum SortOrder {
    ASC = "asc",
    DESC = "desc"
}
export declare enum TemplateSortBy {
    NAME = "name",
    CREATED_AT = "created_at",
    UPDATED_AT = "updated_at",
    EVENT_TYPE = "event_type",
    ATTENDEES = "attendees",
    STATUS = "is_active",
    PUBLIC = "is_public"
}
export declare enum TemplateStatus {
    ALL = "all",
    ACTIVE = "active",
    INACTIVE = "inactive"
}
export declare enum TemplateVisibility {
    ALL = "all",
    PUBLIC = "public",
    PRIVATE = "private"
}
export declare class TemplateFiltersDto {
    search?: string;
    eventType?: string;
    categoryId?: string;
    cityId?: string;
    venueIds?: string[];
    minAttendees?: number;
    maxAttendees?: number;
    status?: TemplateStatus;
    visibility?: TemplateVisibility;
    createdBy?: string;
    myTemplatesOnly?: boolean;
    hasPackagesFromCategory?: string;
    hasCalculations?: boolean;
    createdAfter?: string;
    createdBefore?: string;
    page?: number;
    pageSize?: number;
    sortBy?: TemplateSortBy;
    sortOrder?: SortOrder;
    includePackages?: boolean;
    includeCalculations?: boolean;
    includeStatistics?: boolean;
    tags?: string[];
    excludeId?: string;
}
