"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AdminUserDto {
    id;
    email;
    username;
    full_name;
    role_name;
    created_at;
    last_sign_in_at;
    profile_picture_url;
    status;
}
exports.AdminUserDto = AdminUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    __metadata("design:type", String)
], AdminUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], AdminUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username',
        example: 'johndoe',
        required: false,
    }),
    __metadata("design:type", Object)
], AdminUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Full name',
        example: 'John Doe',
        required: false,
    }),
    __metadata("design:type", Object)
], AdminUserDto.prototype, "full_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role name',
        example: 'admin',
        required: false,
    }),
    __metadata("design:type", Object)
], AdminUserDto.prototype, "role_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User creation date',
        example: '2023-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", String)
], AdminUserDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last sign in date',
        example: '2023-01-01T00:00:00.000Z',
        required: false,
    }),
    __metadata("design:type", Object)
], AdminUserDto.prototype, "last_sign_in_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Profile picture URL',
        example: 'https://example.com/avatar.png',
        required: false,
    }),
    __metadata("design:type", Object)
], AdminUserDto.prototype, "profile_picture_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User status',
        example: 'ACTIVE',
        enum: ['ACTIVE', 'INACTIVE'],
    }),
    __metadata("design:type", String)
], AdminUserDto.prototype, "status", void 0);
//# sourceMappingURL=admin-user.dto.js.map