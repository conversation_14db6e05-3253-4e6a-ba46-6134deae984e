"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DivisionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DivisionsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let DivisionsService = DivisionsService_1 = class DivisionsService {
    supabaseService;
    logger = new common_1.Logger(DivisionsService_1.name);
    tableName = 'divisions';
    selectFields = 'id, code, name, description, is_active, created_at, updated_at';
    uniqueConstraint = 'uk_division_code';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll(active) {
        this.logger.debug('Finding all divisions');
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from(this.tableName)
            .select(this.selectFields)
            .order('name');
        if (active !== undefined) {
            query = query.eq('is_active', active);
        }
        const { data, error } = await query;
        if (error) {
            this.logger.error(`Failed to fetch divisions: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch divisions');
        }
        return data;
    }
    async findOneById(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(this.selectFields)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Division with ID ${id} not found.`);
        }
        return data;
    }
    async createDivision(createDto) {
        this.logger.debug(`Creating division: ${createDto.code} - ${createDto.name}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .insert({
            code: createDto.code,
            name: createDto.name,
            description: createDto.description,
            is_active: createDto.is_active,
        })
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === '23505' &&
                error.message.includes(this.uniqueConstraint)) {
                this.logger.warn(`Attempted to create duplicate division code: ${createDto.code}`);
                throw new common_1.ConflictException(`A division with the code "${createDto.code}" already exists.`);
            }
            this.logger.error(`Failed to create division: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not create division.');
        }
        if (!data) {
            this.logger.error('Division insert succeeded but returned no data.');
            throw new common_1.InternalServerErrorException('Failed to retrieve newly created division.');
        }
        this.logger.log(`Successfully created division ID: ${data.id}, Code: ${data.code}`);
        return data;
    }
    async updateDivision(id, updateDto) {
        this.logger.debug(`Updating division ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {};
        if (updateDto.name !== undefined) {
            updateData.name = updateDto.name;
        }
        if (updateDto.description !== undefined) {
            updateData.description = updateDto.description;
        }
        if (updateDto.is_active !== undefined) {
            updateData.is_active = updateDto.is_active;
        }
        if (Object.keys(updateData).length === 0) {
            return this.findOneById(id);
        }
        const { data, error } = await supabase
            .from(this.tableName)
            .update(updateData)
            .eq('id', id)
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                this.logger.warn(`Division not found for update: ID ${id}`);
                throw new common_1.NotFoundException(`Division with ID ${id} not found.`);
            }
            this.logger.error(`Failed to update division ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update division.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Division with ID ${id} not found.`);
        }
        this.logger.log(`Successfully updated division ID: ${id}`);
        return data;
    }
    async deleteDivision(id) {
        this.logger.debug(`Deleting division ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.tableName)
            .delete()
            .eq('id', id);
        if (error) {
            this.logger.error(`Failed to delete division ${id}: ${error.message}`, error.stack);
            if (error.code === '23503') {
                this.logger.warn(`Attempted to delete division ${id} which is still referenced.`);
                throw new common_1.ConflictException(`Cannot delete division because it is referenced by other records.`);
            }
            throw new common_1.InternalServerErrorException('Could not delete division.');
        }
        if (count === 0) {
            this.logger.warn(`Division not found for deletion: ID ${id}`);
            throw new common_1.NotFoundException(`Division with ID ${id} not found.`);
        }
        this.logger.log(`Successfully deleted division ID: ${id}`);
    }
};
exports.DivisionsService = DivisionsService;
exports.DivisionsService = DivisionsService = DivisionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], DivisionsService);
//# sourceMappingURL=divisions.service.js.map