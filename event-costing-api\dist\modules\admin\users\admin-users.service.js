"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdminUsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUsersService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let AdminUsersService = AdminUsersService_1 = class AdminUsersService {
    supabaseService;
    logger = new common_1.Logger(AdminUsersService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        this.logger.log('Getting all users with profiles');
        const supabase = this.supabaseService.getClient();
        try {
            const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
            if (authError) {
                this.logger.error(`Error fetching auth users: ${authError.message}`, authError.stack);
                throw authError;
            }
            if (!authUsers || !authUsers.users || authUsers.users.length === 0) {
                return [];
            }
            const userIds = authUsers.users.map(user => user.id);
            const { data: profiles, error: profilesError } = await supabase
                .from('profiles')
                .select(`
          id,
          username,
          full_name,
          profile_picture_url,
          role_id
        `)
                .in('id', userIds);
            const { data: roles, error: rolesError } = await supabase
                .from('roles')
                .select('*');
            if (rolesError) {
                this.logger.error(`Error fetching roles: ${rolesError.message}`, rolesError.stack);
                throw rolesError;
            }
            const rolesMap = new Map();
            roles?.forEach(role => {
                rolesMap.set(role.id, role);
            });
            if (profilesError) {
                this.logger.error(`Error fetching profiles: ${profilesError.message}`, profilesError.stack);
                throw profilesError;
            }
            const profilesMap = new Map();
            profiles?.forEach(profile => {
                profilesMap.set(profile.id, profile);
            });
            return authUsers.users.map(user => {
                const profile = profilesMap.get(user.id);
                const isBanned = (user.user_metadata && user.user_metadata.banned) ||
                    (user.app_metadata && user.app_metadata.banned) ||
                    false;
                return {
                    id: user.id,
                    email: user.email || '',
                    username: profile?.username || null,
                    full_name: profile?.full_name || null,
                    role_name: profile?.role_id
                        ? rolesMap.get(profile.role_id)?.role_name || null
                        : null,
                    created_at: user.created_at,
                    last_sign_in_at: user.last_sign_in_at || null,
                    profile_picture_url: profile?.profile_picture_url || null,
                    status: isBanned ? 'INACTIVE' : 'ACTIVE',
                };
            });
        }
        catch (error) {
            this.logger.error(`Error in findAll: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOne(id) {
        this.logger.log(`Getting user with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: userData, error: userError } = await supabase.auth.admin.getUserById(id);
            if (userError) {
                this.logger.error(`Error fetching auth user: ${userError.message}`, userError.stack);
                throw userError;
            }
            if (!userData || !userData.user) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            const { data: profile, error: profileError } = await supabase
                .from('profiles')
                .select(`
          username,
          full_name,
          profile_picture_url,
          role_id
        `)
                .eq('id', id)
                .single();
            let roleName = null;
            if (profile?.role_id) {
                const { data: role, error: roleError } = await supabase
                    .from('roles')
                    .select('role_name')
                    .eq('id', profile.role_id)
                    .single();
                if (roleError && roleError.code !== 'PGRST116') {
                    this.logger.error(`Error fetching role: ${roleError.message}`, roleError.stack);
                }
                else if (role) {
                    roleName = role.role_name;
                }
            }
            if (profileError && profileError.code !== 'PGRST116') {
                this.logger.error(`Error fetching profile: ${profileError.message}`, profileError.stack);
                throw profileError;
            }
            const isBanned = (userData.user.user_metadata && userData.user.user_metadata.banned) ||
                (userData.user.app_metadata && userData.user.app_metadata.banned) ||
                false;
            return {
                id: userData.user.id,
                email: userData.user.email || '',
                username: profile?.username || null,
                full_name: profile?.full_name || null,
                role_name: roleName,
                created_at: userData.user.created_at,
                last_sign_in_at: userData.user.last_sign_in_at || null,
                profile_picture_url: profile?.profile_picture_url || null,
                status: isBanned ? 'INACTIVE' : 'ACTIVE',
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Error in findOne: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateRole(id, updateUserRoleDto) {
        this.logger.log(`Updating role for user ${id} to role ID ${updateUserRoleDto.roleId}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from('profiles')
            .update({ role_id: updateUserRoleDto.roleId })
            .eq('id', id);
        if (error) {
            this.logger.error(`Error updating user role: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateStatus(id, updateUserStatusDto) {
        this.logger.log(`Updating status for user ${id} to ${updateUserStatusDto.status}`);
        const supabase = this.supabaseService.getClient();
        try {
            const isBanned = updateUserStatusDto.status === 'INACTIVE';
            const { error } = await supabase.auth.admin.updateUserById(id, {
                user_metadata: { banned: isBanned },
            });
            if (error) {
                this.logger.error(`Error updating user status: ${error.message}`, error.stack);
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`Error in updateStatus: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getRoles() {
        this.logger.log('Getting all roles');
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('roles')
            .select('*')
            .order('id');
        if (error) {
            this.logger.error(`Error fetching roles: ${error.message}`, error.stack);
            throw error;
        }
        return data;
    }
    async createUser(createUserDto) {
        this.logger.log(`Creating new user with email: ${createUserDto.email}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: authData, error: authError } = await supabase.auth.admin.createUser({
                email: createUserDto.email,
                password: createUserDto.password || undefined,
                email_confirm: true,
                user_metadata: {
                    full_name: createUserDto.full_name,
                    username: createUserDto.username,
                },
            });
            if (authError) {
                this.logger.error(`Error creating user in auth: ${authError.message}`, authError.stack);
                throw authError;
            }
            if (!authData.user) {
                throw new common_1.BadRequestException('Failed to create user');
            }
            const { error: profileError } = await supabase
                .from('profiles')
                .update({ role_id: createUserDto.role_id })
                .eq('id', authData.user.id);
            if (profileError) {
                this.logger.error(`Error updating user role: ${profileError.message}`, profileError.stack);
                throw profileError;
            }
            return this.findOne(authData.user.id);
        }
        catch (error) {
            if (error.code === '23505') {
                throw new common_1.ConflictException('User with this email already exists');
            }
            throw error;
        }
    }
    async updateUser(id, updateUserDto) {
        this.logger.log(`Updating user with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        try {
            const user = await this.findOne(id);
            if (updateUserDto.email || updateUserDto.password) {
                const { error: authError } = await supabase.auth.admin.updateUserById(id, {
                    email: updateUserDto.email,
                    password: updateUserDto.password,
                });
                if (authError) {
                    this.logger.error(`Error updating user in auth: ${authError.message}`, authError.stack);
                    throw authError;
                }
            }
            const profileData = {};
            if (updateUserDto.full_name) {
                profileData.full_name = updateUserDto.full_name;
            }
            if (updateUserDto.username) {
                profileData.username = updateUserDto.username;
            }
            if (updateUserDto.role_id) {
                profileData.role_id = updateUserDto.role_id;
            }
            if (Object.keys(profileData).length > 0) {
                const { error: profileError } = await supabase
                    .from('profiles')
                    .update(profileData)
                    .eq('id', id);
                if (profileError) {
                    this.logger.error(`Error updating user profile: ${profileError.message}`, profileError.stack);
                    throw profileError;
                }
            }
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            if (error.code === '23505') {
                throw new common_1.ConflictException('Username or email already exists');
            }
            throw error;
        }
    }
};
exports.AdminUsersService = AdminUsersService;
exports.AdminUsersService = AdminUsersService = AdminUsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], AdminUsersService);
//# sourceMappingURL=admin-users.service.js.map