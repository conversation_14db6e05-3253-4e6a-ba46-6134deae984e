"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var XlsxExportProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.XlsxExportProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const exports_service_1 = require("../exports.service");
const export_storage_service_1 = require("../services/export-storage.service");
const export_generation_service_1 = require("../services/export-generation.service");
const calculations_service_1 = require("../../calculations/calculations.service");
const export_status_enum_1 = require("../enums/export-status.enum");
let XlsxExportProcessor = XlsxExportProcessor_1 = class XlsxExportProcessor extends bullmq_1.WorkerHost {
    exportsService;
    storageService;
    generationService;
    calculationsService;
    logger = new common_1.Logger(XlsxExportProcessor_1.name);
    constructor(exportsService, storageService, generationService, calculationsService) {
        super();
        this.exportsService = exportsService;
        this.storageService = storageService;
        this.generationService = generationService;
        this.calculationsService = calculationsService;
    }
    async process(job) {
        const { exportHistoryId, calculationId, userId } = job.data;
        this.logger.log(`[XLSX] Processing job ${job.id} for export history ${exportHistoryId}`);
        this.logger.log(`[XLSX] Calculation: ${calculationId}, User: ${userId}`);
        let generatedFileName = '';
        let storagePath = undefined;
        try {
            this.logger.log(`[XLSX] Updating status to PROCESSING for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.PROCESSING);
            this.logger.log(`[XLSX] Fetching calculation data for ${calculationId}`);
            const calculationData = await this.calculationsService.findCalculationForExport(calculationId, userId);
            if (!calculationData) {
                throw new common_1.InternalServerErrorException(`[XLSX] Calculation data not found for ID: ${calculationId}`);
            }
            this.logger.log(`[XLSX] Calculation data fetched successfully for ${calculationId}`);
            this.logger.log(`[XLSX] Transforming calculation data for ${calculationId}`);
            const transformedData = this.generationService.transformCalculationData(calculationData);
            this.logger.log(`[XLSX] Data transformation completed for ${calculationId}`);
            this.logger.log(`[XLSX] Generating XLSX buffer for ${calculationId}`);
            const sanitizedCalcName = calculationData.name
                .replace(/[^a-z0-9]/gi, '_')
                .toLowerCase();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            generatedFileName = `${sanitizedCalcName}_${timestamp}.xlsx`;
            const fileBuffer = await this.generationService.generateXlsxBuffer(transformedData);
            if (!fileBuffer) {
                throw new common_1.InternalServerErrorException('[XLSX] File buffer not generated.');
            }
            this.logger.log(`[XLSX] XLSX buffer generated successfully, size: ${fileBuffer.length} bytes`);
            this.logger.log(`[XLSX] Uploading file to storage: ${generatedFileName}`);
            storagePath = await this.storageService.uploadExportFile(userId, generatedFileName, fileBuffer, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            this.logger.log(`[XLSX] File uploaded successfully to: ${storagePath}`);
            this.logger.log(`[XLSX] Updating status to COMPLETED for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.COMPLETED, {
                storagePath: storagePath,
                fileName: generatedFileName,
                fileSize: fileBuffer.length,
                mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            this.logger.log(`[XLSX] Job ${job.id} completed successfully for export history ${exportHistoryId}`);
        }
        catch (error) {
            this.logger.error(`[XLSX] Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.FAILED, {
                error: error instanceof Error ? error.message : String(error),
                fileName: generatedFileName || undefined,
                storagePath: storagePath || undefined,
            });
            throw error;
        }
    }
};
exports.XlsxExportProcessor = XlsxExportProcessor;
exports.XlsxExportProcessor = XlsxExportProcessor = XlsxExportProcessor_1 = __decorate([
    (0, bullmq_1.Processor)('xlsx-exports'),
    __metadata("design:paramtypes", [exports_service_1.ExportsService,
        export_storage_service_1.ExportStorageService,
        export_generation_service_1.ExportGenerationService,
        calculations_service_1.CalculationsService])
], XlsxExportProcessor);
//# sourceMappingURL=xlsx-export.processor.js.map