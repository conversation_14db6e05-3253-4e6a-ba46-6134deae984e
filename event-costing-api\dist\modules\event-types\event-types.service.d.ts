import { SupabaseService } from '../../core/supabase/supabase.service';
import { EventTypeDto, CreateEventTypeDto, UpdateEventTypeDto } from './dto/event-type.dto';
export declare class EventTypesService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<EventTypeDto[]>;
    findAllAdmin(): Promise<EventTypeDto[]>;
    findOne(id: string): Promise<EventTypeDto>;
    create(createEventTypeDto: CreateEventTypeDto): Promise<EventTypeDto>;
    update(id: string, updateEventTypeDto: UpdateEventTypeDto): Promise<EventTypeDto>;
    remove(id: string): Promise<void>;
    getEventTypeByCode(code: string): Promise<EventTypeDto | null>;
}
