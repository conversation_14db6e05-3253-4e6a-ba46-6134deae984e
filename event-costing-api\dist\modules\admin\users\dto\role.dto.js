"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class RoleDto {
    id;
    role_name;
    description;
    created_at;
}
exports.RoleDto = RoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role ID',
        example: 1,
    }),
    __metadata("design:type", Number)
], RoleDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role name',
        example: 'admin',
    }),
    __metadata("design:type", String)
], RoleDto.prototype, "role_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role description',
        example: 'Administrator with full access',
    }),
    __metadata("design:type", String)
], RoleDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role creation date',
        example: '2023-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", String)
], RoleDto.prototype, "created_at", void 0);
//# sourceMappingURL=role.dto.js.map