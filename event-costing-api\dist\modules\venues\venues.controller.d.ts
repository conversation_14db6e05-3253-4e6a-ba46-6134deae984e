import { VenuesService } from './venues.service';
import { VenueReferenceDto } from './dto/venue-reference.dto';
export declare class VenuesController {
    private readonly venuesService;
    private readonly logger;
    constructor(venuesService: VenuesService);
    getVenues(cityId?: string, active?: boolean, classification?: string, minCapacity?: number, maxCapacity?: number): Promise<VenueReferenceDto[]>;
    getVenueById(id: string): Promise<VenueReferenceDto>;
}
