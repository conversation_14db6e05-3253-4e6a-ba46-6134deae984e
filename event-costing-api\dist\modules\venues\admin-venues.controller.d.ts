import { VenuesService } from './venues.service';
import { AdminVenueDto, CreateVenueDto, ListVenuesQueryDto, PaginatedVenuesResponse, UpdateVenueDto } from './dto/admin-venue.dto';
export declare class AdminVenuesController {
    private readonly venuesService;
    private readonly logger;
    constructor(venuesService: VenuesService);
    findAll(queryDto: ListVenuesQueryDto): Promise<PaginatedVenuesResponse>;
    findOne(id: string): Promise<AdminVenueDto>;
    create(createVenueDto: CreateVenueDto): Promise<AdminVenueDto>;
    update(id: string, updateVenueDto: UpdateVenueDto): Promise<AdminVenueDto>;
    remove(id: string): Promise<void>;
    restore(id: string): Promise<void>;
}
