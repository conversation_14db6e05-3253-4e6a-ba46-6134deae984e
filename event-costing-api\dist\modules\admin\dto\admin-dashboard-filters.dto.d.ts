export declare enum DashboardTimePeriod {
    LAST_7_DAYS = "last_7_days",
    LAST_30_DAYS = "last_30_days",
    LAST_90_DAYS = "last_90_days",
    LAST_YEAR = "last_year",
    ALL_TIME = "all_time"
}
export declare enum DashboardViewMode {
    OVERVIEW = "overview",
    DETAILED = "detailed",
    ANALYTICS = "analytics"
}
export declare class AdminDashboardFiltersDto {
    timePeriod?: DashboardTimePeriod;
    activityDays?: number;
    viewMode?: DashboardViewMode;
    includeStatistics?: boolean;
    includeActivity?: boolean;
    includeHealth?: boolean;
    includePerformance?: boolean;
    categoryId?: string;
    cityId?: string;
    divisionId?: string;
    userId?: string;
    dateStart?: string;
    dateEnd?: string;
    refreshInterval?: number;
    includeCacheMetrics?: boolean;
    includeDatabaseMetrics?: boolean;
    includeApiMetrics?: boolean;
    activityLimit?: number;
    includeUserActivity?: boolean;
    includeAlerts?: boolean;
    includeTrends?: boolean;
}
