"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePackageDependencyDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePackageDependencyDto {
    dependent_package_id;
    dependency_type;
    description;
}
exports.CreatePackageDependencyDto = CreatePackageDependencyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The UUID of the package that this package depends on (or conflicts with).',
        format: 'uuid',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackageDependencyDto.prototype, "dependent_package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: "Type of dependency (e.g., 'REQUIRES', 'CONFLICTS', 'RECOMMENDS').",
        example: 'REQUIRES',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreatePackageDependencyDto.prototype, "dependency_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Optional description.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePackageDependencyDto.prototype, "description", void 0);
//# sourceMappingURL=create-package-dependency.dto.js.map