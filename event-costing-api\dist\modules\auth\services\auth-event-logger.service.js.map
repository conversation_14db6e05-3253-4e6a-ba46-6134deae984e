{"version": 3, "file": "auth-event-logger.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/auth/services/auth-event-logger.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,+BAAoC;AAKpC,IAAY,aAYX;AAZD,WAAY,aAAa;IACvB,gDAA+B,CAAA;IAC/B,gDAA+B,CAAA;IAC/B,gDAA+B,CAAA;IAC/B,kCAAiB,CAAA;IACjB,8CAA6B,CAAA;IAC7B,gDAA+B,CAAA;IAC/B,gEAA+C,CAAA;IAC/C,kEAAiD,CAAA;IACjD,kDAAiC,CAAA;IACjC,0DAAyC,CAAA;IACzC,4DAA2C,CAAA;AAC7C,CAAC,EAZW,aAAa,6BAAb,aAAa,QAYxB;AAKD,IAAY,iBAKX;AALD,WAAY,iBAAiB;IAC3B,kCAAa,CAAA;IACb,wCAAmB,CAAA;IACnB,oCAAe,CAAA;IACf,0CAAqB,CAAA;AACvB,CAAC,EALW,iBAAiB,iCAAjB,iBAAiB,QAK5B;AAoBM,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGJ;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAElE,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAU7D,QAAQ,CACN,SAAwB,EACxB,QAA2B,EAC3B,IAAmB,EACnB,aAAsB;QAGtB,MAAM,kBAAkB,GAAG,aAAa,IAAI,IAAA,SAAM,GAAE,CAAC;QAGrD,MAAM,KAAK,GAAG;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS;YACT,QAAQ;YACR,aAAa,EAAE,kBAAkB;YACjC,GAAG,IAAI;SACR,CAAC;QAGF,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,iBAAiB,CAAC,IAAI;gBACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACvE,MAAM;YACR,KAAK,iBAAiB,CAAC,OAAO;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACxE,MAAM;YACR,KAAK,iBAAiB,CAAC,KAAK,CAAC;YAC7B,KAAK,iBAAiB,CAAC,QAAQ;gBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,eAAe,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CACtD,CAAC;gBACF,MAAM;YACR;gBACE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3E,CAAC;QAID,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAEvB,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAQD,eAAe,CAAC,IAAmB,EAAE,aAAsB;QACzD,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,aAAa,EAC3B,iBAAiB,CAAC,IAAI,EACtB,IAAI,EACJ,aAAa,CACd,CAAC;IACJ,CAAC;IAQD,eAAe,CAAC,IAAmB,EAAE,aAAsB;QACzD,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,aAAa,EAC3B,iBAAiB,CAAC,IAAI,EACtB;YACE,GAAG,IAAI;YACP,MAAM,EAAE,SAAS;SAClB,EACD,aAAa,CACd,CAAC;IACJ,CAAC;IAQD,eAAe,CAAC,IAAmB,EAAE,aAAsB;QACzD,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,aAAa,EAC3B,iBAAiB,CAAC,OAAO,EACzB;YACE,GAAG,IAAI;YACP,MAAM,EAAE,SAAS;SAClB,EACD,aAAa,CACd,CAAC;IACJ,CAAC;IAQD,SAAS,CAAC,IAAmB,EAAE,aAAsB;QACnD,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,MAAM,EACpB,iBAAiB,CAAC,IAAI,EACtB,IAAI,EACJ,aAAa,CACd,CAAC;IACJ,CAAC;IAQD,eAAe,CAAC,IAAmB,EAAE,aAAsB;QACzD,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,aAAa,EAC3B,iBAAiB,CAAC,IAAI,EACtB;YACE,GAAG,IAAI;YACP,MAAM,EAAE,SAAS;SAClB,EACD,aAAa,CACd,CAAC;IACJ,CAAC;IAQD,sBAAsB,CAAC,IAAmB,EAAE,aAAsB;QAChE,OAAO,IAAI,CAAC,QAAQ,CAClB,aAAa,CAAC,qBAAqB,EACnC,iBAAiB,CAAC,OAAO,EACzB;YACE,GAAG,IAAI;YACP,MAAM,EAAE,SAAS;SAClB,EACD,aAAa,CACd,CAAC;IACJ,CAAC;IAOO,UAAU,CAAC,KAAU;QAI3B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,CAAC,KAAK,YAAY,EAAE,CAAC;QAElE,CAAC;IACH,CAAC;CACF,CAAA;AA3KY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAIiC,sBAAa;GAH9C,sBAAsB,CA2KlC"}