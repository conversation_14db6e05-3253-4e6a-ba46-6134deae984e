import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { AuthEventLoggerService } from './auth-event-logger.service';
export interface UserWithProfile extends User {
    profile?: {
        id: string;
        role_id: number | null;
        roles: {
            role_name: string;
        } | null;
    } | null;
}
export declare class JwtValidationService {
    private readonly supabaseService;
    private readonly configService;
    private readonly authEventLogger;
    private readonly logger;
    constructor(supabaseService: SupabaseService, configService: ConfigService, authEventLogger: AuthEventLoggerService);
    validateUserFromJwtPayload(payload: any): Promise<UserWithProfile>;
}
