{"version": 3, "file": "package-relations.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/admin/packages/services/package-relations.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAIwB;AACxB,iFAAqE;AAK9D,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGL;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnE,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAOjE,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,OAAiB;QAC/D,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE7C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,UAAU,OAAO,CAAC,MAAM,sBAAsB,SAAS,EAAE,CAC1D,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzC,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC,CAAC;QAEJ,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;aACxC,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,WAAW,CAAC,CAAC;QAEvB,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mCAAmC,SAAS,CAAC,OAAO,EAAE,CACvD,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,4CAA4C,SAAS,CAAC,OAAO,EAAE,CAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,YAAqB,EACrB,QAAmB;QAEnB,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEhE,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,UAAU,QAAQ,CAAC,MAAM,sBAAsB,SAAS,EAAE,CAC3D,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,UAAU,EAAE,SAAS;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;aACzC,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,YAAY,CAAC,CAAC;QAExB,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mCAAmC,UAAU,CAAC,OAAO,EAAE,CACxD,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,4CAA4C,UAAU,CAAC,OAAO,EAAE,CACjE,CAAC;QACJ,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,gBAAkC;QAElC,IACE,CAAC,gBAAgB,CAAC,KAAK,KAAK,SAAS;YACnC,gBAAgB,CAAC,cAAc,KAAK,SAAS,CAAC;YAChD,CAAC,gBAAgB,CAAC,WAAW,EAC7B,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,SAAS,EAAE,CAAC,CAAC;QAEpE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,SAAS,GAAG;YAChB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,CAAC;YAClC,cAAc,EAAE,gBAAgB,CAAC,cAAc,IAAI,CAAC;YACpD,WAAW,EAAE,4BAA4B;SAC1C,CAAC;QAEF,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;aACzC,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAEvB,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,UAAU,CAAC,OAAO,EAAE,CACvD,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,+CAA+C,UAAU,CAAC,OAAO,EAAE,CACpE,CAAC;QACJ,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,OAAkB;QAChE,IAAI,OAAO,KAAK,SAAS;YAAE,OAAO;QAElC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC1C,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,EAAE;aACR,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAE/B,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,WAAW,CAAC,OAAO,EAAE,CACpE,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,uCAAuC,WAAW,CAAC,OAAO,EAAE,CAC7D,CAAC;QACJ,CAAC;QAGD,MAAM,IAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAG/D,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACzC,UAAU,EAAE,SAAS;gBACrB,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC,CAAC;YAEJ,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;iBAC1C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,WAAW,CAAC,CAAC;YAEvB,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,WAAW,CAAC,OAAO,EAAE,CAChE,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,uCAAuC,WAAW,CAAC,OAAO,EAAE,CAC7D,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,YAAsB,EACtB,QAAmB;QAEnB,IAAI,YAAY,KAAK,SAAS;YAAE,OAAO;QAEvC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,IAAI,YAAY,EAAE,CAAC;YAEjB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;gBAGxE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;qBAC1C,IAAI,CAAC,gBAAgB,CAAC;qBACtB,MAAM,EAAE;qBACR,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAE/B,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+CAA+C,WAAW,CAAC,OAAO,EAAE,CACrE,CAAC;oBACF,MAAM,IAAI,qCAA4B,CACpC,wCAAwC,WAAW,CAAC,OAAO,EAAE,CAC9D,CAAC;gBACJ,CAAC;gBAGD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YAEN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0DAA0D,SAAS,EAAE,CAAC,CAAC;YAEvF,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;iBAC1C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,EAAE;iBACR,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAE/B,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,WAAW,CAAC,OAAO,EAAE,CAC5D,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,wCAAwC,WAAW,CAAC,OAAO,EAAE,CAC9D,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2DAA2D,SAAS,EAAE,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,QAAkB;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,YAAY,QAAQ,CAAC,MAAM,uCAAuC,SAAS,EAAE,CAC9E,CAAC;QAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,UAAU,EAAE,SAAS;YACrB,QAAQ,EAAE,OAAO;SAClB,CAAC,CAAC,CAAC;QAEJ,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC1C,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,YAAY,CAAC,CAAC;QAExB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,WAAW,CAAC,OAAO,EAAE,CAChE,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,wCAAwC,WAAW,CAAC,OAAO,EAAE,CAC9D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,QAAQ,CAAC,MAAM,mCAAmC,SAAS,EAAE,CAAC,CAAC;IACzG,CAAC;IAOD,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,gBAAkC;QAElC,IACE,CAAC,gBAAgB,CAAC,KAAK,KAAK,SAAS;YACnC,gBAAgB,CAAC,cAAc,KAAK,SAAS,CAAC;YAChD,CAAC,gBAAgB,CAAC,WAAW,EAC7B,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;aACnE,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,IAAI,CAAC;aACZ,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;aAC3B,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,WAAW,CAAC;aAC/C,WAAW,EAAE,CAAC;QAEjB,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,eAAe,CAAC,OAAO,EAAE,CAC5D,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,uCAAuC,eAAe,CAAC,OAAO,EAAE,CACjE,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG;YAChB,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,CAAC;YAClC,cAAc,EAAE,gBAAgB,CAAC,cAAc,IAAI,CAAC;YACpD,WAAW,EAAE,4BAA4B;SAC1C,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAElB,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;iBAC/C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,SAAS,CAAC;iBACjB,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;YAE9B,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,gBAAgB,CAAC,OAAO,EAAE,CACpD,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,uCAAuC,gBAAgB,CAAC,OAAO,EAAE,CAClE,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YAEN,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;iBAC/C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAEvB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,gBAAgB,CAAC,OAAO,EAAE,CACzD,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,oCAAoC,gBAAgB,CAAC,OAAO,EAAE,CAC/D,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,+BAA+B,CAC3C,SAAiB,EACjB,UAAoB;QAEpB,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAE5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mEAAmE,SAAS,EAAE,CAAC,CAAC;YAEhG,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;YAClD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;iBAC1C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,EAAE;iBACR,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAE/B,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,WAAW,CAAC,OAAO,EAAE,CAC5D,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,yCAAyC,WAAW,CAAC,OAAO,EAAE,CAC/D,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sDAAsD,SAAS,qBAAqB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7H,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC;;;;;;;OAOP,CAAC;aACD,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAE/B,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,UAAU,CAAC,OAAO,EAAE,CACnE,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,yCAAyC,UAAU,CAAC,OAAO,EAAE,CAC9D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;YACxE,OAAO;QACT,CAAC;QAGD,MAAM,eAAe,GAAG,aAAa;aAClC,MAAM,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC5D,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,eAAe,CAAC,MAAM,2CAA2C,SAAS,KAAK,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEzI,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;iBAC1C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,EAAE;iBACR,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;iBAC3B,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;YAEnC,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,WAAW,CAAC,OAAO,EAAE,CACpE,CAAC;gBACF,MAAM,IAAI,qCAA4B,CACpC,yCAAyC,WAAW,CAAC,OAAO,EAAE,CAC/D,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+DAA+D,SAAS,EAAE,CAAC,CAAC;QAC9F,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mDAAmD,SAAS,EAAE,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;CACF,CAAA;AA5aY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,uBAAuB,CA4anC"}