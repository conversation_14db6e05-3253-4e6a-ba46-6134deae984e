import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { ExportsService } from '../exports.service';
import { CalculationsService } from '../../calculations/calculations.service';
import { ExportManagementDataDto } from '../dto/export-management-data.dto';
import { ExportManagementFiltersDto } from '../dto/export-management-filters.dto';
import { ExportFormat } from '../enums/export-format.enum';
export declare class ExportManagementService {
    private readonly supabaseService;
    private readonly exportsService;
    private readonly calculationsService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, exportsService: ExportsService, calculationsService: CalculationsService);
    getExportManagementData(filters: ExportManagementFiltersDto | undefined, user: User): Promise<ExportManagementDataDto>;
    initiateBatchExports(requests: Array<{
        calculationId: string;
        format: ExportFormat;
        recipient?: string;
    }>, user: User): Promise<Array<{
        calculationId: string;
        exportId: string;
        status: string;
    }>>;
    private getUserExports;
    private getUserCalculations;
    private getExportStatistics;
    private getRecentExportActivity;
    private extractResult;
    private collectErrors;
}
