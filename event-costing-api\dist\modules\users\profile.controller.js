"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProfileController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const storage_service_1 = require("../../core/storage/storage.service");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const swagger_1 = require("@nestjs/swagger");
const profile_picture_response_dto_1 = require("./dto/profile-picture-response.dto");
const update_profile_dto_1 = require("./dto/update-profile.dto");
let ProfileController = ProfileController_1 = class ProfileController {
    storageService;
    supabaseService;
    logger = new common_1.Logger(ProfileController_1.name);
    BUCKET_NAME = 'profiles';
    constructor(storageService, supabaseService) {
        this.storageService = storageService;
        this.supabaseService = supabaseService;
    }
    async uploadProfilePicture(file, user) {
        this.logger.log(`Profile picture upload request received for user: ${user.id}`);
        if (file) {
            this.logger.log(`File details: {
        originalname: ${file.originalname},
        mimetype: ${file.mimetype},
        size: ${file.size} bytes,
        buffer: ${file.buffer ? 'Present' : 'Missing'}
      }`);
        }
        else {
            this.logger.error('No file received in the request');
            throw new common_1.BadRequestException('No file uploaded');
        }
        const allowedMimeTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            this.logger.warn(`Invalid file type: ${file.mimetype}`);
            throw new common_1.BadRequestException('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        }
        try {
            const fileExt = file.originalname.split('.').pop();
            const filePath = `profile-pictures/${user.id}-${Date.now()}.${fileExt}`;
            this.logger.log(`Generated file path: ${filePath}`);
            this.logger.log(`Ensuring bucket exists: ${this.BUCKET_NAME}`);
            this.logger.log(`Uploading file to storage: ${this.BUCKET_NAME}/${filePath}`);
            await this.storageService.uploadFile(this.BUCKET_NAME, filePath, file.buffer, file.mimetype);
            this.logger.log(`File uploaded successfully to storage`);
            this.logger.log(`Generating public URL for file`);
            const publicUrl = this.storageService.getPublicUrl(this.BUCKET_NAME, filePath);
            this.logger.log(`Generated public URL: ${publicUrl}`);
            this.logger.log(`Updating user profile with new image URL`);
            const supabase = this.supabaseService.getClient();
            const { error: updateError } = await supabase
                .from('profiles')
                .update({ profile_picture_url: publicUrl })
                .eq('id', user.id);
            if (updateError) {
                this.logger.error(`Failed to update profile with new image URL: ${updateError.message}`, updateError.stack);
                throw new common_1.BadRequestException('Failed to update profile with new image URL');
            }
            this.logger.log(`User profile updated successfully with new image URL`);
            this.logger.log(`Profile picture upload completed successfully`);
            return {
                success: true,
                message: 'Profile picture uploaded successfully',
                profilePictureUrl: publicUrl,
            };
        }
        catch (error) {
            this.logger.error(`Error uploading profile picture: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`Failed to upload profile picture: ${error.message}`);
        }
    }
    async updateProfile(updateProfileDto, user) {
        try {
            const transformedData = {
                ...updateProfileDto,
                ...(updateProfileDto.company && {
                    company_name: updateProfileDto.company,
                }),
                ...(updateProfileDto.phone && { phone_number: updateProfileDto.phone }),
            };
            if ('company' in transformedData) {
                delete transformedData.company;
            }
            if ('phone' in transformedData) {
                delete transformedData.phone;
            }
            this.logger.log(`Updating profile for user ${user.id} with data:`, transformedData);
            const supabase = this.supabaseService.getClient();
            const { error } = await supabase
                .from('profiles')
                .update(transformedData)
                .eq('id', user.id);
            if (error) {
                this.logger.error(`Failed to update profile: ${error.message}`, error.stack);
                throw new common_1.BadRequestException(`Failed to update profile: ${error.message}`);
            }
            const { data: updatedProfile, error: fetchError } = await supabase
                .from('profiles')
                .select(`
          *,
          roles (role_name)
        `)
                .eq('id', user.id)
                .single();
            this.logger.log(`Fetched updated profile: ${JSON.stringify(updatedProfile)}`);
            if (fetchError) {
                this.logger.error(`Failed to verify profile update: ${fetchError.message}`, fetchError.stack);
                throw new common_1.BadRequestException(`Failed to verify profile update: ${fetchError.message}`);
            }
            const fieldsToCheck = Object.keys(transformedData);
            const missingUpdates = fieldsToCheck.filter(field => transformedData[field] !== undefined &&
                updatedProfile[field] !== transformedData[field]);
            if (missingUpdates.length > 0) {
                this.logger.error(`Profile update verification failed. The following fields were not updated: ${missingUpdates.join(', ')}`);
                throw new common_1.BadRequestException(`Profile update verification failed. Some fields were not updated correctly.`);
            }
            this.logger.log(`Profile updated successfully for user ${user.id}`);
            return {
                success: true,
                message: 'Profile updated successfully',
                updatedFields: fieldsToCheck,
                profile: updatedProfile,
            };
        }
        catch (error) {
            this.logger.error(`Error updating profile: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`Failed to update profile: ${error.message}`);
        }
    }
};
exports.ProfileController = ProfileController;
__decorate([
    (0, common_1.Post)('picture'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a profile picture' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Profile picture uploaded successfully',
        type: profile_picture_response_dto_1.ProfilePictureResponseDto,
    }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: {
            fileSize: 5 * 1024 * 1024,
        },
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ProfileController.prototype, "uploadProfilePicture", null);
__decorate([
    (0, common_1.Patch)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update user profile' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Profile updated successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_profile_dto_1.UpdateProfileDto, Object]),
    __metadata("design:returntype", Promise)
], ProfileController.prototype, "updateProfile", null);
exports.ProfileController = ProfileController = ProfileController_1 = __decorate([
    (0, common_1.Controller)('profile'),
    (0, swagger_1.ApiTags)('Profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [storage_service_1.StorageService,
        supabase_service_1.SupabaseService])
], ProfileController);
//# sourceMappingURL=profile.controller.js.map