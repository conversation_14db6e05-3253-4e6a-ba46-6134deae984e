import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageVenueDto } from './dto/package-venue.dto';
export declare class PackageVenuesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly PACKAGE_VENUES_TABLE;
    private readonly VENUES_TABLE;
    private readonly PACKAGES_TABLE;
    constructor(supabaseService: SupabaseService);
    addVenueToPackage(packageId: string, venueId: string): Promise<{
        id: string;
    }>;
    listVenuesForPackage(packageId: string): Promise<PackageVenueDto[]>;
    removeVenueFromPackage(packageId: string, venueId: string): Promise<void>;
}
