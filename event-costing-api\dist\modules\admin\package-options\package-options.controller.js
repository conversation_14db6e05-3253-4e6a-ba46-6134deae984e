"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOptionsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const package_options_service_1 = require("./package-options.service");
const create_package_option_dto_1 = require("./dto/create-package-option.dto");
const update_package_option_dto_1 = require("./dto/update-package-option.dto");
const package_option_dto_1 = require("./dto/package-option.dto");
const paginated_response_dto_1 = require("../../../shared/dtos/paginated-response.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
let PackageOptionsController = class PackageOptionsController {
    packageOptionsService;
    constructor(packageOptionsService) {
        this.packageOptionsService = packageOptionsService;
    }
    create(packageId, createDto) {
        return this.packageOptionsService.create(packageId, createDto);
    }
    findAll(packageId, queryDto) {
        return this.packageOptionsService.findAll(packageId, queryDto);
    }
    findOne(packageId, id) {
        return this.packageOptionsService.findOne(packageId, id);
    }
    update(packageId, id, updateDto) {
        return this.packageOptionsService.update(packageId, id, updateDto);
    }
    async remove(packageId, id) {
        await this.packageOptionsService.remove(packageId, id);
    }
};
exports.PackageOptionsController = PackageOptionsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new package option for a specific package',
    }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        description: 'Parent Package UUID',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The package option has been successfully created.',
        type: package_option_dto_1.PackageOptionDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Parent package not found.' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_package_option_dto_1.CreatePackageOptionDto]),
    __metadata("design:returntype", Promise)
], PackageOptionsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'List package options for a specific package',
    }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        description: 'Parent Package UUID',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({ type: package_options_service_1.PackageOptionListQueryDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of package options.',
        type: (paginated_response_dto_1.PaginatedResponseDto),
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Parent package not found.' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, package_options_service_1.PackageOptionListQueryDto]),
    __metadata("design:returntype", Promise)
], PackageOptionsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Retrieve a specific package option by ID within a package',
    }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the parent package' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The ID of the package option' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Detail', type: package_option_dto_1.PackageOptionDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Not Found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackageOptionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a package option' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        description: 'Parent Package UUID',
        type: String,
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Package Option UUID',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Updated', type: package_option_dto_1.PackageOptionDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Not Found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_package_option_dto_1.UpdatePackageOptionDto]),
    __metadata("design:returntype", Promise)
], PackageOptionsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Soft-delete a package option' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'ID of the parent package' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID of the package option to delete',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Package option soft-deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Option not found.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error.' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackageOptionsController.prototype, "remove", null);
exports.PackageOptionsController = PackageOptionsController = __decorate([
    (0, swagger_1.ApiTags)('Admin | Packages / Options'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/packages/:packageId/options'),
    __metadata("design:paramtypes", [package_options_service_1.PackageOptionsService])
], PackageOptionsController);
//# sourceMappingURL=package-options.controller.js.map