"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminTemplatesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminTemplatesController = void 0;
const common_1 = require("@nestjs/common");
const templates_service_1 = require("./templates.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const template_summary_dto_1 = require("./dto/template-summary.dto");
const create_template_from_calculation_dto_1 = require("./dto/create-template-from-calculation.dto");
const create_template_dto_1 = require("./dto/create-template.dto");
const template_calculation_dto_1 = require("./dto/template-calculation.dto");
const list_admin_templates_dto_1 = require("./dto/list-admin-templates.dto");
const update_template_dto_1 = require("./dto/update-template.dto");
const update_template_status_dto_1 = require("./dto/update-template-status.dto");
const swagger_1 = require("@nestjs/swagger");
let AdminTemplatesController = AdminTemplatesController_1 = class AdminTemplatesController {
    templatesService;
    logger = new common_1.Logger(AdminTemplatesController_1.name);
    constructor(templatesService) {
        this.templatesService = templatesService;
    }
    async createTemplate(createDto, user) {
        this.logger.log(`Admin user ${user.email} creating basic template '${createDto.name}'`);
        return this.templatesService.createTemplate(createDto, user);
    }
    async createTemplateFromCalculation(createDto, user) {
        this.logger.log(`Admin user ${user.email} creating template from calculation ${createDto.calculationId}`);
        return this.templatesService.createTemplateFromCalculation(createDto, user);
    }
    async findAllAdmin(queryDto) {
        this.logger.log(`Admin fetching templates list with query: ${JSON.stringify(queryDto)}`);
        return this.templatesService.findAllAdmin(queryDto);
    }
    async findOneAdmin(id) {
        this.logger.log(`Admin fetching template details for ID: ${id}`);
        const result = await this.templatesService.findOneAdmin(id);
        this.logger.log(`Template response for ID ${id}: ${JSON.stringify({
            id: result.id,
            name: result.name,
            venue_ids: result.venue_ids,
            has_venue_ids: !!result.venue_ids,
            venue_ids_length: result.venue_ids?.length || 0,
        })}`);
        return result;
    }
    async updateTemplate(id, updateDto) {
        this.logger.log(`Admin updating template ID: ${id}`);
        return this.templatesService.updateTemplate(id, updateDto);
    }
    async updateTemplateStatus(id, updateStatusDto) {
        this.logger.log(`Admin updating template status ID: ${id} to ${updateStatusDto.isActive ? 'active' : 'inactive'}`);
        return this.templatesService.updateTemplateStatus(id, updateStatusDto.isActive);
    }
    async deleteTemplate(id) {
        this.logger.log(`Admin deleting template ID: ${id}`);
        return this.templatesService.deleteTemplate(id);
    }
    async calculateTemplate(id) {
        this.logger.log(`Admin calculating template total for ID: ${id}`);
        return this.templatesService.calculateTemplateTotal(id);
    }
    async getTemplateCalculationSummary(id) {
        this.logger.log(`Admin getting template calculation summary for ID: ${id}`);
        return this.templatesService.getTemplateCalculationSummary(id);
    }
};
exports.AdminTemplatesController = AdminTemplatesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a basic template' }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Template created successfully.',
        type: template_summary_dto_1.TemplateSummaryDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Invalid input data.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_template_dto_1.CreateTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "createTemplate", null);
__decorate([
    (0, common_1.Post)('from-calculation'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a template from an existing calculation' }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Template created successfully.',
        type: template_summary_dto_1.TemplateSummaryDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Invalid input data.' }),
    (0, swagger_1.ApiNotFoundResponse)({
        description: 'Calculation not found or has no items.',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_template_from_calculation_dto_1.CreateTemplateFromCalculationDto, Object]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "createTemplateFromCalculation", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all templates (admin view)' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Paginated list of templates.',
        type: template_summary_dto_1.PaginatedAdminTemplatesResponse,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_admin_templates_dto_1.ListAdminTemplatesQueryDto]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "findAllAdmin", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get detailed template information (admin view)' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template details.',
        type: template_summary_dto_1.TemplateDetailDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "findOneAdmin", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a template' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template updated successfully.',
        type: template_summary_dto_1.TemplateDetailDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Invalid input data.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_template_dto_1.UpdateTemplateDto]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "updateTemplate", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update template active status' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template status updated successfully.',
        type: template_summary_dto_1.TemplateDetailDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Invalid input data.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_template_status_dto_1.UpdateTemplateStatusDto]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "updateTemplateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a template' }),
    (0, swagger_1.ApiNoContentResponse)({ description: 'Template deleted successfully.' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "deleteTemplate", null);
__decorate([
    (0, common_1.Get)(':id/calculate'),
    (0, swagger_1.ApiOperation)({ summary: 'Calculate template total value and breakdown' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template calculation result.',
        type: template_calculation_dto_1.TemplateCalculationResultDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "calculateTemplate", null);
__decorate([
    (0, common_1.Get)(':id/calculate/summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get template calculation summary' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template calculation summary.',
        type: template_calculation_dto_1.TemplateCalculationSummaryDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Template not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminTemplatesController.prototype, "getTemplateCalculationSummary", null);
exports.AdminTemplatesController = AdminTemplatesController = AdminTemplatesController_1 = __decorate([
    (0, common_1.Controller)('admin/templates'),
    (0, swagger_1.ApiTags)('Admin Templates'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => templates_service_1.TemplatesService))),
    __metadata("design:paramtypes", [templates_service_1.TemplatesService])
], AdminTemplatesController);
//# sourceMappingURL=admin-templates.controller.js.map