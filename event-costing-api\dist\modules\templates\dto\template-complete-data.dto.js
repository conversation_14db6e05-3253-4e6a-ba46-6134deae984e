"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateCompleteDataDto = exports.TemplateManagementMetadataDto = exports.TemplateFilterInfoDto = exports.TemplateAvailableFiltersDto = exports.TemplateStatsDto = exports.TemplatePackageDto = exports.EventTypeInfoDto = exports.PaginatedTemplatesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const category_dto_1 = require("../../categories/dto/category.dto");
const template_filters_dto_1 = require("./template-filters.dto");
class PaginatedTemplatesDto {
    data;
    totalCount;
    page;
    pageSize;
    totalPages;
}
exports.PaginatedTemplatesDto = PaginatedTemplatesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of templates',
        type: [Object],
    }),
    __metadata("design:type", Array)
], PaginatedTemplatesDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of templates',
        example: 25,
    }),
    __metadata("design:type", Number)
], PaginatedTemplatesDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedTemplatesDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedTemplatesDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 3,
    }),
    __metadata("design:type", Number)
], PaginatedTemplatesDto.prototype, "totalPages", void 0);
class EventTypeInfoDto {
    id;
    name;
    description;
}
exports.EventTypeInfoDto = EventTypeInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event type ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], EventTypeInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event type name',
        example: 'Corporate Event',
    }),
    __metadata("design:type", String)
], EventTypeInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event type description',
        example: 'Corporate meetings, conferences, and business events',
    }),
    __metadata("design:type", String)
], EventTypeInfoDto.prototype, "description", void 0);
class TemplatePackageDto {
    package_id;
    name;
    description;
    category_id;
    price;
    quantity_basis;
    is_available_in_city;
    is_available_in_venue;
}
exports.TemplatePackageDto = TemplatePackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], TemplatePackageDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package name',
        example: 'Sound System Basic',
    }),
    __metadata("design:type", String)
], TemplatePackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package description',
        example: 'Basic sound system for small events',
    }),
    __metadata("design:type", Object)
], TemplatePackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Category ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], TemplatePackageDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package price',
        example: 1500000,
    }),
    __metadata("design:type", Number)
], TemplatePackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantity basis',
        example: 'per_day',
    }),
    __metadata("design:type", String)
], TemplatePackageDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether package is available in city',
        example: true,
    }),
    __metadata("design:type", Boolean)
], TemplatePackageDto.prototype, "is_available_in_city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether package is available in venue',
        example: true,
    }),
    __metadata("design:type", Boolean)
], TemplatePackageDto.prototype, "is_available_in_venue", void 0);
class TemplateStatsDto {
    totalTemplates;
    activeTemplates;
    inactiveTemplates;
    publicTemplates;
    privateTemplates;
}
exports.TemplateStatsDto = TemplateStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of templates',
        example: 25,
    }),
    __metadata("design:type", Number)
], TemplateStatsDto.prototype, "totalTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of active templates',
        example: 20,
    }),
    __metadata("design:type", Number)
], TemplateStatsDto.prototype, "activeTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of inactive templates',
        example: 5,
    }),
    __metadata("design:type", Number)
], TemplateStatsDto.prototype, "inactiveTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of public templates',
        example: 15,
    }),
    __metadata("design:type", Number)
], TemplateStatsDto.prototype, "publicTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of private templates',
        example: 10,
    }),
    __metadata("design:type", Number)
], TemplateStatsDto.prototype, "privateTemplates", void 0);
class TemplateAvailableFiltersDto {
    categories;
    eventTypes;
}
exports.TemplateAvailableFiltersDto = TemplateAvailableFiltersDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available categories for filtering',
        type: [Object],
    }),
    __metadata("design:type", Array)
], TemplateAvailableFiltersDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available event types for filtering',
        type: [Object],
    }),
    __metadata("design:type", Array)
], TemplateAvailableFiltersDto.prototype, "eventTypes", void 0);
class TemplateFilterInfoDto {
    applied;
    available;
}
exports.TemplateFilterInfoDto = TemplateFilterInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Applied filters',
        type: template_filters_dto_1.TemplateFiltersDto,
    }),
    __metadata("design:type", template_filters_dto_1.TemplateFiltersDto)
], TemplateFilterInfoDto.prototype, "applied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available filter options',
        type: TemplateAvailableFiltersDto,
    }),
    __metadata("design:type", TemplateAvailableFiltersDto)
], TemplateFilterInfoDto.prototype, "available", void 0);
class TemplateManagementMetadataDto {
    loadTime;
    cacheVersion;
    userId;
    errors;
    timestamp;
    totalTemplates;
    appliedFilters;
}
exports.TemplateManagementMetadataDto = TemplateManagementMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time taken to load the data in milliseconds',
        example: 280,
    }),
    __metadata("design:type", Number)
], TemplateManagementMetadataDto.prototype, "loadTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache version for the response',
        example: '1.0',
    }),
    __metadata("design:type", String)
], TemplateManagementMetadataDto.prototype, "cacheVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who requested the data',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], TemplateManagementMetadataDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Any errors encountered during data loading',
        type: [String],
        example: [],
    }),
    __metadata("design:type", Array)
], TemplateManagementMetadataDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the data was loaded',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], TemplateManagementMetadataDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of templates',
        example: 25,
    }),
    __metadata("design:type", Number)
], TemplateManagementMetadataDto.prototype, "totalTemplates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of filters applied',
        example: 1,
    }),
    __metadata("design:type", Number)
], TemplateManagementMetadataDto.prototype, "appliedFilters", void 0);
class TemplateCompleteDataDto {
    templates;
    categories;
    eventTypes;
    packages;
    summary;
    filters;
    metadata;
}
exports.TemplateCompleteDataDto = TemplateCompleteDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Paginated template results',
        type: PaginatedTemplatesDto,
    }),
    __metadata("design:type", PaginatedTemplatesDto)
], TemplateCompleteDataDto.prototype, "templates", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available categories',
        type: [category_dto_1.CategoryDto],
    }),
    __metadata("design:type", Array)
], TemplateCompleteDataDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available event types',
        type: [EventTypeInfoDto],
    }),
    __metadata("design:type", Array)
], TemplateCompleteDataDto.prototype, "eventTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available packages for template creation',
        type: [TemplatePackageDto],
    }),
    __metadata("design:type", Array)
], TemplateCompleteDataDto.prototype, "packages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template summary statistics',
        type: TemplateStatsDto,
    }),
    __metadata("design:type", TemplateStatsDto)
], TemplateCompleteDataDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter information',
        type: TemplateFilterInfoDto,
    }),
    __metadata("design:type", TemplateFilterInfoDto)
], TemplateCompleteDataDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata about the response',
        type: TemplateManagementMetadataDto,
    }),
    __metadata("design:type", TemplateManagementMetadataDto)
], TemplateCompleteDataDto.prototype, "metadata", void 0);
//# sourceMappingURL=template-complete-data.dto.js.map