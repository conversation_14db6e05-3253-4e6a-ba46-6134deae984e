import { SupabaseService } from '../../../core/supabase/supabase.service';
import { UpdateCalculationStatusDto } from '../dto/update-calculation-status.dto';
import { CalculationStatus } from '../enums/calculation-status.enum';
export declare class CalculationStatusService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    updateStatus(id: string, updateStatusDto: UpdateCalculationStatusDto, userId: string): Promise<void>;
    getCurrentStatus(calculationId: string): Promise<string>;
    private isValidStatusTransition;
    getPossibleNextStatuses(calculationId: string): Promise<string[]>;
    bulkUpdateStatus(calculationIds: string[], newStatus: CalculationStatus, userId: string): Promise<{
        success: string[];
        failed: string[];
    }>;
    getCalculationsByStatus(userId: string, status: string, limit?: number, offset?: number): Promise<any[]>;
    getStatusStatistics(userId: string): Promise<Record<string, number>>;
    canModifyCalculation(status: string): boolean;
    canDeleteCalculation(status: string): boolean;
    autoTransitionStatus(calculationId: string): Promise<string | null>;
}
