"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageRelationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageRelationsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../../core/supabase/supabase.service");
let PackageRelationsService = PackageRelationsService_1 = class PackageRelationsService {
    supabaseService;
    logger = new common_1.Logger(PackageRelationsService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async createCityAssociations(packageId, cityIds) {
        if (!cityIds || cityIds.length === 0)
            return;
        this.logger.log(`Adding ${cityIds.length} cities to package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const cityInserts = cityIds.map(cityId => ({
            package_id: packageId,
            city_id: cityId,
        }));
        const { error: cityError } = await supabase
            .from('package_cities')
            .insert(cityInserts);
        if (cityError) {
            this.logger.error(`Error adding cities to package: ${cityError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to associate cities with package: ${cityError.message}`);
        }
    }
    async createVenueAssociations(packageId, enableVenues, venueIds) {
        if (!enableVenues || !venueIds || venueIds.length === 0)
            return;
        this.logger.log(`Adding ${venueIds.length} venues to package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const venueInserts = venueIds.map(venueId => ({
            package_id: packageId,
            venue_id: venueId,
        }));
        const { error: venueError } = await supabase
            .from('package_venues')
            .insert(venueInserts);
        if (venueError) {
            this.logger.error(`Error adding venues to package: ${venueError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to associate venues with package: ${venueError.message}`);
        }
    }
    async createPriceInformation(packageId, createPackageDto) {
        if ((createPackageDto.price === undefined &&
            createPackageDto.unit_base_cost === undefined) ||
            !createPackageDto.currency_id) {
            return;
        }
        this.logger.log(`Adding price information to package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const priceData = {
            package_id: packageId,
            currency_id: createPackageDto.currency_id,
            price: createPackageDto.price || 0,
            unit_base_cost: createPackageDto.unit_base_cost || 0,
            description: 'Added via package creation',
        };
        const { error: priceError } = await supabase
            .from('package_prices')
            .insert([priceData]);
        if (priceError) {
            this.logger.error(`Error adding price to package: ${priceError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to add price information to package: ${priceError.message}`);
        }
    }
    async updateCityAssociations(packageId, cityIds) {
        if (cityIds === undefined)
            return;
        this.logger.log(`Updating city associations for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { error: deleteError } = await supabase
            .from('package_cities')
            .delete()
            .eq('package_id', packageId);
        if (deleteError) {
            this.logger.error(`Error deleting existing city associations: ${deleteError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to update city associations: ${deleteError.message}`);
        }
        await this.cleanupInvalidVenueAssociations(packageId, cityIds);
        if (cityIds.length > 0) {
            const cityInserts = cityIds.map(cityId => ({
                package_id: packageId,
                city_id: cityId,
            }));
            const { error: insertError } = await supabase
                .from('package_cities')
                .insert(cityInserts);
            if (insertError) {
                this.logger.error(`Error inserting new city associations: ${insertError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to update city associations: ${insertError.message}`);
            }
        }
    }
    async updateVenueAssociations(packageId, enableVenues, venueIds) {
        if (enableVenues === undefined)
            return;
        const supabase = this.supabaseService.getClient();
        if (enableVenues) {
            if (venueIds !== undefined) {
                this.logger.log(`Updating venue associations for package ${packageId}`);
                const { error: deleteError } = await supabase
                    .from('package_venues')
                    .delete()
                    .eq('package_id', packageId);
                if (deleteError) {
                    this.logger.error(`Error removing existing venue associations: ${deleteError.message}`);
                    throw new common_1.InternalServerErrorException(`Failed to update venue associations: ${deleteError.message}`);
                }
                if (venueIds.length > 0) {
                    await this.handleVenueUpdates(packageId, venueIds);
                }
            }
        }
        else {
            this.logger.log(`Completely removing all venue associations for package ${packageId}`);
            const { error: deleteError } = await supabase
                .from('package_venues')
                .delete()
                .eq('package_id', packageId);
            if (deleteError) {
                this.logger.error(`Error removing venue associations: ${deleteError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to remove venue associations: ${deleteError.message}`);
            }
            this.logger.log(`Successfully removed all venue associations for package ${packageId}`);
        }
    }
    async handleVenueUpdates(packageId, venueIds) {
        const supabase = this.supabaseService.getClient();
        this.logger.log(`Creating ${venueIds.length} new venue associations for package ${packageId}`);
        const venueInserts = venueIds.map(venueId => ({
            package_id: packageId,
            venue_id: venueId,
        }));
        const { error: insertError } = await supabase
            .from('package_venues')
            .insert(venueInserts);
        if (insertError) {
            this.logger.error(`Error creating new venue associations: ${insertError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to create venue associations: ${insertError.message}`);
        }
        this.logger.log(`Successfully created ${venueIds.length} venue associations for package ${packageId}`);
    }
    async updatePriceInformation(packageId, updatePackageDto) {
        if ((updatePackageDto.price === undefined &&
            updatePackageDto.unit_base_cost === undefined) ||
            !updatePackageDto.currency_id) {
            return;
        }
        this.logger.log(`Updating price information for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data: existingPrice, error: priceCheckError } = await supabase
            .from('package_prices')
            .select('id')
            .eq('package_id', packageId)
            .eq('currency_id', updatePackageDto.currency_id)
            .maybeSingle();
        if (priceCheckError) {
            this.logger.error(`Error checking existing price: ${priceCheckError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to update price information: ${priceCheckError.message}`);
        }
        const priceData = {
            package_id: packageId,
            currency_id: updatePackageDto.currency_id,
            price: updatePackageDto.price || 0,
            unit_base_cost: updatePackageDto.unit_base_cost || 0,
            description: 'Updated via package update',
        };
        if (existingPrice) {
            const { error: updatePriceError } = await supabase
                .from('package_prices')
                .update(priceData)
                .eq('id', existingPrice.id);
            if (updatePriceError) {
                this.logger.error(`Error updating price: ${updatePriceError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to update price information: ${updatePriceError.message}`);
            }
        }
        else {
            const { error: insertPriceError } = await supabase
                .from('package_prices')
                .insert([priceData]);
            if (insertPriceError) {
                this.logger.error(`Error inserting new price: ${insertPriceError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to add price information: ${insertPriceError.message}`);
            }
        }
    }
    async cleanupInvalidVenueAssociations(packageId, newCityIds) {
        if (newCityIds.length === 0) {
            this.logger.log(`No cities selected, removing all venue associations for package ${packageId}`);
            const supabase = this.supabaseService.getClient();
            const { error: deleteError } = await supabase
                .from('package_venues')
                .delete()
                .eq('package_id', packageId);
            if (deleteError) {
                this.logger.error(`Error removing venue associations: ${deleteError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to cleanup venue associations: ${deleteError.message}`);
            }
            return;
        }
        this.logger.log(`Cleaning up invalid venue associations for package ${packageId} with new cities: ${newCityIds.join(', ')}`);
        const supabase = this.supabaseService.getClient();
        const { data: currentVenues, error: venueError } = await supabase
            .from('package_venues')
            .select(`
        id,
        venue_id,
        venues!inner (
          id,
          city_id
        )
      `)
            .eq('package_id', packageId);
        if (venueError) {
            this.logger.error(`Error fetching current venue associations: ${venueError.message}`);
            throw new common_1.InternalServerErrorException(`Failed to cleanup venue associations: ${venueError.message}`);
        }
        if (!currentVenues || currentVenues.length === 0) {
            this.logger.log(`No venue associations found for package ${packageId}`);
            return;
        }
        const invalidVenueIds = currentVenues
            .filter((pv) => !newCityIds.includes(pv.venues.city_id))
            .map((pv) => pv.venue_id);
        if (invalidVenueIds.length > 0) {
            this.logger.log(`Removing ${invalidVenueIds.length} invalid venue associations for package ${packageId}: ${invalidVenueIds.join(', ')}`);
            const { error: deleteError } = await supabase
                .from('package_venues')
                .delete()
                .eq('package_id', packageId)
                .in('venue_id', invalidVenueIds);
            if (deleteError) {
                this.logger.error(`Error removing invalid venue associations: ${deleteError.message}`);
                throw new common_1.InternalServerErrorException(`Failed to cleanup venue associations: ${deleteError.message}`);
            }
            this.logger.log(`Successfully removed invalid venue associations for package ${packageId}`);
        }
        else {
            this.logger.log(`No invalid venue associations found for package ${packageId}`);
        }
    }
};
exports.PackageRelationsService = PackageRelationsService;
exports.PackageRelationsService = PackageRelationsService = PackageRelationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageRelationsService);
//# sourceMappingURL=package-relations.service.js.map