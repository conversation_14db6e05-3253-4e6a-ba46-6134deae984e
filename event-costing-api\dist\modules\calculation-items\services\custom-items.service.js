"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CustomItemsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomItemsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let CustomItemsService = CustomItemsService_1 = class CustomItemsService {
    supabaseService;
    logger = new common_1.Logger(CustomItemsService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async getCustomItems(calculationId) {
        this.logger.log(`Fetching custom items for calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: customItems, error } = await supabase
            .from('calculation_custom_items')
            .select(`
        id,
        calculation_id,
        item_name,
        description,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_price,
        unit_cost,
        category_id,
        currency_id,
        city_id,
        created_at,
        updated_at
      `)
            .eq('calculation_id', calculationId)
            .order('created_at', { ascending: true });
        if (error) {
            this.logger.error(`Error fetching custom items: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to fetch custom items');
        }
        return customItems.map(item => this.transformToCustomItemDto(item));
    }
    async getCustomItemById(calculationId, itemId) {
        this.logger.log(`Fetching custom item ${itemId} for calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: customItem, error } = await supabase
            .from('calculation_custom_items')
            .select(`
        id,
        calculation_id,
        item_name,
        description,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_price,
        unit_cost,
        category_id,
        currency_id,
        city_id,
        created_at,
        updated_at
      `)
            .eq('id', itemId)
            .eq('calculation_id', calculationId)
            .single();
        if (error) {
            this.logger.error(`Error fetching custom item: ${error.message}`);
            throw new common_1.NotFoundException(`Custom item with ID ${itemId} not found`);
        }
        return this.transformToCustomItemDto(customItem);
    }
    async addCustomItem(calcId, addDto, user) {
        this.logger.log(`User ${user.id} adding custom item '${addDto.itemName}' to calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkCalculationOwnership(supabase, calcId, user.id);
        const { data: calcData, error: calcError } = await supabase
            .from('calculation_history')
            .select('currency_id')
            .eq('id', calcId)
            .single();
        if (calcError || !calcData) {
            throw new common_1.InternalServerErrorException('Failed to retrieve calculation currency for custom item.');
        }
        const insertPayload = {
            calculation_id: calcId,
            item_name: addDto.itemName,
            description: addDto.description,
            item_quantity: addDto.quantity,
            item_quantity_basis: addDto.itemQuantityBasis || 1,
            quantity_basis: addDto.quantityBasis || 'PER_DAY',
            unit_price: addDto.unitPrice,
            unit_cost: addDto.unitCost || 0,
            currency_id: calcData.currency_id,
            category_id: addDto.categoryId,
            city_id: addDto.cityId,
        };
        const { data, error: insertError } = await supabase
            .from('calculation_custom_items')
            .insert(insertPayload)
            .select('id')
            .single();
        if (insertError || !data) {
            this.logger.error(`Failed to add custom item to calculation ${calcId}: ${insertError?.message}`, insertError?.stack);
            throw new common_1.InternalServerErrorException('Could not add custom item.');
        }
        await this.recalcCalculationViaRpc(calcId);
        this.logger.log(`Custom item ${data.id} added to calculation ${calcId} and recalculation triggered`);
        return { id: data.id };
    }
    async updateCustomItem(calcId, itemId, updateDto, user) {
        this.logger.log(`Updating custom item ${itemId} in calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkCalculationOwnership(supabase, calcId, user.id);
        const updatePayload = {};
        if (updateDto.itemName !== undefined)
            updatePayload.item_name = updateDto.itemName;
        if (updateDto.description !== undefined)
            updatePayload.description = updateDto.description;
        if (updateDto.quantity !== undefined)
            updatePayload.item_quantity = updateDto.quantity;
        if (updateDto.unitPrice !== undefined)
            updatePayload.unit_price = updateDto.unitPrice;
        if (updateDto.unitCost !== undefined)
            updatePayload.unit_cost = updateDto.unitCost;
        if (updateDto.itemQuantityBasis !== undefined)
            updatePayload.item_quantity_basis = updateDto.itemQuantityBasis;
        if (updateDto.quantityBasis !== undefined)
            updatePayload.quantity_basis = updateDto.quantityBasis;
        if (updateDto.categoryId !== undefined)
            updatePayload.category_id = updateDto.categoryId;
        updatePayload.updated_at = new Date().toISOString();
        const { data, error } = await supabase
            .from('calculation_custom_items')
            .update(updatePayload)
            .eq('id', itemId)
            .eq('calculation_id', calcId)
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error updating custom item ${itemId}: ${error.message}`, error.details);
            throw new common_1.InternalServerErrorException('Failed to update custom item.');
        }
        await this.recalcCalculationViaRpc(calcId);
        this.logger.log(`Successfully updated custom item ${itemId} and triggered recalculation`);
        return this.transformToCustomItemDto(data);
    }
    async deleteCustomItem(calcId, itemId, user) {
        this.logger.log(`Deleting custom item ${itemId} from calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkCalculationOwnership(supabase, calcId, user.id);
        const { error } = await supabase
            .from('calculation_custom_items')
            .delete()
            .eq('id', itemId)
            .eq('calculation_id', calcId);
        if (error) {
            this.logger.error(`Error deleting custom item ${itemId}: ${error.message}`, error.details);
            throw new common_1.InternalServerErrorException('Failed to delete custom item.');
        }
        await this.recalcCalculationViaRpc(calcId);
        this.logger.log(`Successfully deleted custom item ${itemId} and triggered recalculation`);
    }
    transformToCustomItemDto(item) {
        return {
            id: item.id,
            calculation_id: item.calculation_id,
            item_name: item.item_name,
            description: item.description,
            item_quantity: item.item_quantity,
            item_quantity_basis: item.item_quantity_basis,
            quantity_basis: item.quantity_basis,
            unit_price: item.unit_price,
            unit_cost: item.unit_cost,
            calculated_total: item.item_quantity * item.unit_price,
            category_id: item.category_id,
            currency_id: item.currency_id,
            city_id: item.city_id,
            created_at: item.created_at,
            updated_at: item.updated_at,
        };
    }
    async checkCalculationOwnership(supabase, calculationId, userId) {
        const { data, error } = await supabase
            .from('calculation_history')
            .select('id')
            .eq('id', calculationId)
            .eq('created_by', userId)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error checking ownership for calc ${calculationId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Ownership check failed.');
        }
        if (!data) {
            this.logger.warn(`User ${userId} attempt to modify calc ${calculationId} without ownership.`);
            throw new common_1.ForbiddenException('Access denied to this calculation.');
        }
    }
    async recalcCalculationViaRpc(calcId) {
        this.logger.log(`Triggering recalculation RPC for calculation ${calcId}`);
        const supabase = this.supabaseService.getClient();
        const { error: rpcError } = await supabase.rpc('recalculate_calculation_totals', { p_calculation_id: calcId });
        if (rpcError) {
            this.logger.error(`RPC recalculate_calculation_totals failed for calc ${calcId}: ${rpcError.message}`, rpcError.details);
            throw new common_1.InternalServerErrorException(`Failed to trigger recalculation for calculation ${calcId}.`);
        }
        this.logger.log(`Successfully triggered recalculation via RPC for calc ID: ${calcId}`);
    }
};
exports.CustomItemsService = CustomItemsService;
exports.CustomItemsService = CustomItemsService = CustomItemsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CustomItemsService);
//# sourceMappingURL=custom-items.service.js.map