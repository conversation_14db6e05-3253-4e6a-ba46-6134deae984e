import { SupabaseService } from '../supabase/supabase.service';
export interface FileMetadata {
    name: string;
    size: number;
    mimetype: string;
    created_at: string;
    updated_at: string;
    last_accessed_at: string;
    publicUrl: string;
    isFolder?: boolean;
}
export declare class StorageService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    private initializeBuckets;
    ensureBucketExists(bucketName: string): Promise<void>;
    uploadFile(bucketName: string, filePath: string, fileBuffer: Buffer, contentType: string, options?: {
        upsert?: boolean;
    }): Promise<string>;
    getSignedUrl(bucketName: string, filePath: string | null | undefined, expiresIn?: number): Promise<string | null>;
    getPublicUrl(bucketName: string, filePath: string): string;
    deleteFile(bucketName: string, filePath: string): Promise<void>;
    getFileMetadata(bucketName: string, filePath: string): Promise<FileMetadata | null>;
    listFiles(bucketName: string, folderPath?: string): Promise<FileMetadata[]>;
}
