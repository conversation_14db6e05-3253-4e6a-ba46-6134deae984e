"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ExportManagementController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const export_management_service_1 = require("../services/export-management.service");
const export_management_data_dto_1 = require("../dto/export-management-data.dto");
const export_management_filters_dto_1 = require("../dto/export-management-filters.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../auth/decorators/get-current-user.decorator");
let ExportManagementController = ExportManagementController_1 = class ExportManagementController {
    exportManagementService;
    logger = new common_1.Logger(ExportManagementController_1.name);
    constructor(exportManagementService) {
        this.exportManagementService = exportManagementService;
    }
    async getManagementData(filters, user) {
        this.logger.log(`User ${user.email} requesting export management data`);
        if (typeof filters.exportIds === 'string') {
            filters.exportIds = filters.exportIds.split(',').filter(id => id.trim());
        }
        return this.exportManagementService.getExportManagementData(filters, user);
    }
    async initiateBatchExports(requests, user) {
        this.logger.log(`User ${user.email} initiating batch export for ${requests.length} items`);
        const results = await this.exportManagementService.initiateBatchExports(requests, user);
        const successful = results.filter(r => r.status === 'initiated').length;
        const failed = results.filter(r => r.status === 'failed').length;
        return {
            results,
            summary: {
                total: requests.length,
                successful,
                failed,
            },
            metadata: {
                timestamp: new Date().toISOString(),
                userId: user.id,
            },
        };
    }
    async getManagementSummary(user) {
        this.logger.log(`User ${user.email} requesting export management summary`);
        const startTime = Date.now();
        const managementData = await this.exportManagementService.getExportManagementData({}, user);
        const statistics = managementData.statistics;
        const successRate = statistics.totalExports > 0
            ? statistics.completedExports / statistics.totalExports
            : 0;
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const recentExports = managementData.exports.data;
        const exportsToday = recentExports.filter(exp => new Date(exp.created_at) >= today).length;
        const exportsThisWeek = recentExports.filter(exp => new Date(exp.created_at) >= thisWeek).length;
        const exportsThisMonth = recentExports.filter(exp => new Date(exp.created_at) >= thisMonth).length;
        const daysInMonth = now.getDate();
        const averageExportsPerDay = daysInMonth > 0 ? exportsThisMonth / daysInMonth : 0;
        const loadTime = Date.now() - startTime;
        return {
            overview: {
                totalExports: statistics.totalExports,
                completedExports: statistics.completedExports,
                failedExports: statistics.failedExports,
                pendingExports: statistics.pendingExports,
                successRate: Math.round(successRate * 100) / 100,
            },
            formatBreakdown: statistics.exportsByFormat,
            recentActivity: {
                exportsToday,
                exportsThisWeek,
                exportsThisMonth,
                averageExportsPerDay: Math.round(averageExportsPerDay * 100) / 100,
            },
            performance: {
                averageProcessingTime: 45,
                fastestExport: 15,
                slowestExport: 120,
            },
            metadata: {
                loadTime,
                timestamp: new Date().toISOString(),
            },
        };
    }
};
exports.ExportManagementController = ExportManagementController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get complete export management data',
        description: 'Consolidated endpoint that returns user exports, calculations, statistics, and recent activity in a single response. Replaces the need for multiple separate API calls for export management.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Complete export management data with metadata',
        type: export_management_data_dto_1.ExportManagementDataDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to load export management data.',
    }),
    (0, swagger_1.ApiQuery)({ name: 'calculationId', required: false, description: 'Filter by calculation ID' }),
    (0, swagger_1.ApiQuery)({ name: 'format', required: false, description: 'Filter by export format' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by export status' }),
    (0, swagger_1.ApiQuery)({ name: 'dateStart', required: false, description: 'Filter by date range - start date' }),
    (0, swagger_1.ApiQuery)({ name: 'dateEnd', required: false, description: 'Filter by date range - end date' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for calculation name' }),
    (0, swagger_1.ApiQuery)({ name: 'failedOnly', required: false, description: 'Include only failed exports' }),
    (0, swagger_1.ApiQuery)({ name: 'completedOnly', required: false, description: 'Include only completed exports' }),
    (0, swagger_1.ApiQuery)({ name: 'pendingOnly', required: false, description: 'Include only pending/processing exports' }),
    (0, swagger_1.ApiQuery)({ name: 'activityDays', required: false, description: 'Number of days for recent activity' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number for pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, description: 'Number of items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Sort by field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' }),
    (0, swagger_1.ApiQuery)({ name: 'includeStatistics', required: false, description: 'Include export statistics' }),
    (0, swagger_1.ApiQuery)({ name: 'includeActivity', required: false, description: 'Include recent activity' }),
    (0, swagger_1.ApiQuery)({ name: 'includeCalculations', required: false, description: 'Include calculation details' }),
    (0, swagger_1.ApiQuery)({ name: 'exportIds', required: false, description: 'Filter by specific export IDs (comma-separated)' }),
    (0, swagger_1.ApiQuery)({ name: 'minFileSize', required: false, description: 'Filter by minimum file size in bytes' }),
    (0, swagger_1.ApiQuery)({ name: 'maxFileSize', required: false, description: 'Filter by maximum file size in bytes' }),
    (0, swagger_1.ApiQuery)({ name: 'downloadableOnly', required: false, description: 'Include exports with download URLs only' }),
    (0, swagger_1.ApiQuery)({ name: 'withErrorsOnly', required: false, description: 'Include exports with errors only' }),
    (0, swagger_1.ApiQuery)({ name: 'groupByFormat', required: false, description: 'Group results by format' }),
    (0, swagger_1.ApiQuery)({ name: 'groupByStatus', required: false, description: 'Group results by status' }),
    (0, swagger_1.ApiQuery)({ name: 'includePerformanceMetrics', required: false, description: 'Include performance metrics' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [export_management_filters_dto_1.ExportManagementFiltersDto, Object]),
    __metadata("design:returntype", Promise)
], ExportManagementController.prototype, "getManagementData", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, common_1.HttpCode)(common_1.HttpStatus.ACCEPTED),
    (0, swagger_1.ApiOperation)({
        summary: 'Initiate multiple exports in batch',
        description: 'Creates multiple export jobs for different calculations or formats in a single request. Useful for bulk export operations.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Batch export initiation results',
        schema: {
            type: 'object',
            properties: {
                results: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            calculationId: { type: 'string', format: 'uuid' },
                            exportId: { type: 'string', format: 'uuid' },
                            status: { type: 'string', example: 'initiated' },
                        },
                    },
                },
                summary: {
                    type: 'object',
                    properties: {
                        total: { type: 'number', example: 5 },
                        successful: { type: 'number', example: 4 },
                        failed: { type: 'number', example: 1 },
                    },
                },
                metadata: {
                    type: 'object',
                    properties: {
                        timestamp: { type: 'string', format: 'date-time' },
                        userId: { type: 'string', format: 'uuid' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid batch export request.',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", Promise)
], ExportManagementController.prototype, "initiateBatchExports", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get export management summary statistics',
        description: 'Returns summary statistics about export management including counts by format, status, and recent activity metrics.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Export management summary statistics',
        schema: {
            type: 'object',
            properties: {
                overview: {
                    type: 'object',
                    properties: {
                        totalExports: { type: 'number', example: 50 },
                        completedExports: { type: 'number', example: 45 },
                        failedExports: { type: 'number', example: 3 },
                        pendingExports: { type: 'number', example: 2 },
                        successRate: { type: 'number', example: 0.9 },
                    },
                },
                formatBreakdown: {
                    type: 'object',
                    properties: {
                        pdf: { type: 'number', example: 30 },
                        xlsx: { type: 'number', example: 15 },
                        csv: { type: 'number', example: 5 },
                    },
                },
                recentActivity: {
                    type: 'object',
                    properties: {
                        exportsToday: { type: 'number', example: 5 },
                        exportsThisWeek: { type: 'number', example: 20 },
                        exportsThisMonth: { type: 'number', example: 45 },
                        averageExportsPerDay: { type: 'number', example: 1.5 },
                    },
                },
                performance: {
                    type: 'object',
                    properties: {
                        averageProcessingTime: { type: 'number', example: 45 },
                        fastestExport: { type: 'number', example: 15 },
                        slowestExport: { type: 'number', example: 120 },
                    },
                },
                metadata: {
                    type: 'object',
                    properties: {
                        loadTime: { type: 'number' },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    __param(0, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ExportManagementController.prototype, "getManagementSummary", null);
exports.ExportManagementController = ExportManagementController = ExportManagementController_1 = __decorate([
    (0, swagger_1.ApiTags)('Export Management'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('exports/management'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [export_management_service_1.ExportManagementService])
], ExportManagementController);
//# sourceMappingURL=export-management.controller.js.map