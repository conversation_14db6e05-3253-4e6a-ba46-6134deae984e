"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackagePricesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricesController = void 0;
const common_1 = require("@nestjs/common");
const package_prices_service_1 = require("./package-prices.service");
const swagger_1 = require("@nestjs/swagger");
const create_package_price_dto_1 = require("./dto/create-package-price.dto");
const update_package_price_dto_1 = require("./dto/update-package-price.dto");
const package_price_dto_1 = require("./dto/package-price.dto");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let PackagePricesController = PackagePricesController_1 = class PackagePricesController {
    packagePricesService;
    logger = new common_1.Logger(PackagePricesController_1.name);
    constructor(packagePricesService) {
        this.packagePricesService = packagePricesService;
    }
    create(packageId, createPackagePriceDto) {
        this.logger.log(`Received request to create price for package ${packageId}`);
        return this.packagePricesService.create(packageId, createPackagePriceDto);
    }
    findAllByPackage(packageId) {
        this.logger.log(`Received request to find all prices for package ${packageId}`);
        return this.packagePricesService.findAll(packageId);
    }
    findOne(packageId, packagePriceId) {
        this.logger.log(`Received request to find price ${packagePriceId} for package ${packageId}`);
        return this.packagePricesService.findOne(packageId, packagePriceId);
    }
    update(packageId, packagePriceId, updatePackagePriceDto) {
        this.logger.log(`Received request to update price ${packagePriceId} for package ${packageId}`);
        return this.packagePricesService.update(packageId, packagePriceId, updatePackagePriceDto);
    }
    remove(packageId, packagePriceId) {
        this.logger.log(`Received request to delete price ${packagePriceId} for package ${packageId}`);
        return this.packagePricesService.remove(packageId, packagePriceId);
    }
};
exports.PackagePricesController = PackagePricesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new price for a specific package' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the package' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Package price created successfully.',
        type: package_price_dto_1.PackagePriceDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request (e.g., validation error)',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict (e.g., duplicate price for currency/date)',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_package_price_dto_1.CreatePackagePriceDto]),
    __metadata("design:returntype", Promise)
], PackagePricesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prices for a specific package' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the package' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of package prices.',
        type: [package_price_dto_1.PackagePriceDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagePricesController.prototype, "findAllByPackage", null);
__decorate([
    (0, common_1.Get)(':packagePriceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific price record for a package' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the package' }),
    (0, swagger_1.ApiParam)({
        name: 'packagePriceId',
        description: 'The ID of the price record',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Package price details.',
        type: package_price_dto_1.PackagePriceDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Price not found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('packagePriceId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackagePricesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':packagePriceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific price record' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the package' }),
    (0, swagger_1.ApiParam)({
        name: 'packagePriceId',
        description: 'The ID of the price record to update',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Package price updated successfully.',
        type: package_price_dto_1.PackagePriceDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request (e.g., validation error)',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Price not found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('packagePriceId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_package_price_dto_1.UpdatePackagePriceDto]),
    __metadata("design:returntype", Promise)
], PackagePricesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':packagePriceId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a specific price record' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: 'The ID of the package' }),
    (0, swagger_1.ApiParam)({
        name: 'packagePriceId',
        description: 'The ID of the price record to delete',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Package price deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Price not found' }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('packagePriceId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackagePricesController.prototype, "remove", null);
exports.PackagePricesController = PackagePricesController = PackagePricesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Package Prices'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/packages/:packageId/prices'),
    __metadata("design:paramtypes", [package_prices_service_1.PackagePricesService])
], PackagePricesController);
//# sourceMappingURL=package-prices.controller.js.map