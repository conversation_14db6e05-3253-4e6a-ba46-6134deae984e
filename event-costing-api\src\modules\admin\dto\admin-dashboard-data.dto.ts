import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CategoryDto } from '../../categories/dto/category.dto';
import { CityDto } from '../../cities/dto/city.dto';
import { DivisionDto } from '../../divisions/dto/division.dto';
import { AdminDashboardFiltersDto } from './admin-dashboard-filters.dto';

/**
 * DTO for system overview statistics
 */
export class SystemOverviewDto {
  @ApiProperty({
    description: 'Total number of packages in the system',
    example: 150,
  })
  totalPackages: number;

  @ApiProperty({
    description: 'Total number of templates in the system',
    example: 25,
  })
  totalTemplates: number;

  @ApiProperty({
    description: 'Total number of calculations in the system',
    example: 500,
  })
  totalCalculations: number;

  @ApiProperty({
    description: 'Total number of users in the system',
    example: 15,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'Total number of categories in the system',
    example: 12,
  })
  totalCategories: number;

  @ApiProperty({
    description: 'Total number of cities in the system',
    example: 8,
  })
  totalCities: number;

  @ApiProperty({
    description: 'Total number of divisions in the system',
    example: 5,
  })
  totalDivisions: number;
}

/**
 * DTO for packages overview
 */
export class PackagesOverviewDto {
  @ApiProperty({
    description: 'Total number of packages',
    example: 150,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Number of active packages',
    example: 140,
  })
  activeCount: number;

  @ApiProperty({
    description: 'Number of deleted packages',
    example: 10,
  })
  deletedCount: number;
}

/**
 * DTO for templates overview
 */
export class TemplatesOverviewDto {
  @ApiProperty({
    description: 'Total number of templates',
    example: 25,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Number of active templates',
    example: 20,
  })
  activeCount: number;

  @ApiProperty({
    description: 'Number of public templates',
    example: 15,
  })
  publicCount: number;

  @ApiProperty({
    description: 'Number of private templates',
    example: 10,
  })
  privateCount: number;
}

/**
 * DTO for calculations overview
 */
export class CalculationsOverviewDto {
  @ApiProperty({
    description: 'Total number of calculations',
    example: 500,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Number of draft calculations',
    example: 350,
  })
  draftCount: number;

  @ApiProperty({
    description: 'Number of finalized calculations',
    example: 150,
  })
  finalizedCount: number;
}

/**
 * DTO for users overview
 */
export class UsersOverviewDto {
  @ApiProperty({
    description: 'Total number of users',
    example: 15,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Number of active users',
    example: 12,
  })
  activeCount: number;
}

/**
 * DTO for recent activity item
 */
export class RecentActivityDto {
  @ApiProperty({
    description: 'Type of activity',
    example: 'package',
    enum: ['package', 'template', 'calculation', 'user'],
  })
  type: string;

  @ApiProperty({
    description: 'ID of the item',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the item',
    example: 'Sound System Basic',
  })
  name: string;

  @ApiProperty({
    description: 'Action performed',
    example: 'created',
    enum: ['created', 'updated', 'deleted'],
  })
  action: string;

  @ApiProperty({
    description: 'Timestamp of the activity',
    type: String,
    format: 'date-time',
  })
  timestamp: string;
}

/**
 * DTO for filter information
 */
export class AdminDashboardFilterInfoDto {
  @ApiProperty({
    description: 'Applied filters',
    type: AdminDashboardFiltersDto,
  })
  applied: AdminDashboardFiltersDto;
}

/**
 * DTO for admin dashboard metadata
 */
export class AdminDashboardMetadataDto {
  @ApiProperty({
    description: 'Time taken to load the data in milliseconds',
    example: 450,
  })
  loadTime: number;

  @ApiProperty({
    description: 'Cache version for the response',
    example: '1.0',
  })
  cacheVersion: string;

  @ApiProperty({
    description: 'User ID who requested the data',
    type: String,
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Any errors encountered during data loading',
    type: [String],
    example: [],
  })
  errors?: string[];

  @ApiProperty({
    description: 'Timestamp when the data was loaded',
    type: String,
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Data points summary',
    type: Object,
    example: {
      categoriesCount: 12,
      citiesCount: 8,
      divisionsCount: 5,
      packagesCount: 150,
      templatesCount: 25,
      calculationsCount: 500,
      usersCount: 15,
      recentActivityCount: 20,
    },
  })
  dataPoints: {
    categoriesCount: number;
    citiesCount: number;
    divisionsCount: number;
    packagesCount: number;
    templatesCount: number;
    calculationsCount: number;
    usersCount: number;
    recentActivityCount: number;
  };
}

/**
 * Complete admin dashboard data response DTO
 * Consolidates all admin dashboard-related data in a single response
 */
export class AdminDashboardDataDto {
  @ApiProperty({
    description: 'System overview statistics',
    type: SystemOverviewDto,
  })
  overview: SystemOverviewDto;

  @ApiProperty({
    description: 'All available categories',
    type: [CategoryDto],
  })
  categories: CategoryDto[];

  @ApiProperty({
    description: 'All available cities',
    type: [CityDto],
  })
  cities: CityDto[];

  @ApiProperty({
    description: 'All available divisions',
    type: [DivisionDto],
  })
  divisions: DivisionDto[];

  @ApiProperty({
    description: 'Packages overview statistics',
    type: PackagesOverviewDto,
  })
  packages: PackagesOverviewDto;

  @ApiProperty({
    description: 'Templates overview statistics',
    type: TemplatesOverviewDto,
  })
  templates: TemplatesOverviewDto;

  @ApiProperty({
    description: 'Calculations overview statistics',
    type: CalculationsOverviewDto,
  })
  calculations: CalculationsOverviewDto;

  @ApiProperty({
    description: 'Users overview statistics',
    type: UsersOverviewDto,
  })
  users: UsersOverviewDto;

  @ApiProperty({
    description: 'Recent activity items',
    type: [RecentActivityDto],
  })
  recentActivity: RecentActivityDto[];

  @ApiProperty({
    description: 'Filter information',
    type: AdminDashboardFilterInfoDto,
  })
  filters: AdminDashboardFilterInfoDto;

  @ApiProperty({
    description: 'Metadata about the response',
    type: AdminDashboardMetadataDto,
  })
  metadata: AdminDashboardMetadataDto;
}
