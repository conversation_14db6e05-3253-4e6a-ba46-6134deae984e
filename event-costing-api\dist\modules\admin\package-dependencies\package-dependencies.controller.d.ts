import { PackageDependenciesService } from './package-dependencies.service';
import { CreatePackageDependencyDto } from './dto/create-package-dependency.dto';
import { PackageDependencyDto } from './dto/package-dependency.dto';
export declare class PackageDependenciesController {
    private readonly packageDependenciesService;
    private readonly logger;
    constructor(packageDependenciesService: PackageDependenciesService);
    create(packageId: string, createDto: CreatePackageDependencyDto): Promise<PackageDependencyDto>;
    findAllByPackage(packageId: string): Promise<PackageDependencyDto[]>;
    remove(packageId: string, dependencyId: string): Promise<void>;
}
