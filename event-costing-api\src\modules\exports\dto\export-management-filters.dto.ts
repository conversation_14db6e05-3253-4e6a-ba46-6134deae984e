import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsDateString, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ExportFormat } from '../enums/export-format.enum';
import { ExportStatus } from '../enums/export-status.enum';

/**
 * Sort order enum
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Sort by options enum for exports
 */
export enum ExportSortBy {
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
  FORMAT = 'format',
  STATUS = 'status',
  CALCULATION_NAME = 'calculation_name',
}

/**
 * DTO for export management filtering and pagination
 */
export class ExportManagementFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by calculation ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  calculationId?: string;

  @ApiPropertyOptional({
    description: 'Filter by export format',
    enum: ExportFormat,
    example: ExportFormat.PDF,
  })
  @IsOptional()
  @IsEnum(ExportFormat)
  format?: ExportFormat;

  @ApiPropertyOptional({
    description: 'Filter by export status',
    enum: ExportStatus,
    example: ExportStatus.COMPLETED,
  })
  @IsOptional()
  @IsEnum(ExportStatus)
  status?: ExportStatus;

  @ApiPropertyOptional({
    description: 'Filter by date range - start date',
    type: String,
    format: 'date-time',
    example: '2024-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  dateStart?: string;

  @ApiPropertyOptional({
    description: 'Filter by date range - end date',
    type: String,
    format: 'date-time',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  dateEnd?: string;

  @ApiPropertyOptional({
    description: 'Search term for calculation name',
    example: 'corporate event',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Include only failed exports',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  failedOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Include only completed exports',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  completedOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Include only pending/processing exports',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  pendingOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Number of days for recent activity',
    example: 7,
    default: 7,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(365)
  activityDays?: number;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number;

  @ApiPropertyOptional({
    description: 'Sort by field',
    enum: ExportSortBy,
    example: ExportSortBy.CREATED_AT,
    default: ExportSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ExportSortBy)
  sortBy?: ExportSortBy;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    example: SortOrder.DESC,
    default: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder;

  @ApiPropertyOptional({
    description: 'Include export statistics in response',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeStatistics?: boolean;

  @ApiPropertyOptional({
    description: 'Include recent activity in response',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeActivity?: boolean;

  @ApiPropertyOptional({
    description: 'Include calculation details in response',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCalculations?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by specific export IDs',
    type: [String],
    example: ['export-1', 'export-2'],
  })
  @IsOptional()
  @IsString({ each: true })
  exportIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by file size range - minimum size in bytes',
    example: 1024,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minFileSize?: number;

  @ApiPropertyOptional({
    description: 'Filter by file size range - maximum size in bytes',
    example: 10485760, // 10MB
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxFileSize?: number;

  @ApiPropertyOptional({
    description: 'Include exports with download URLs only',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  downloadableOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Include exports with errors only',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  withErrorsOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Group results by format',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  groupByFormat?: boolean;

  @ApiPropertyOptional({
    description: 'Group results by status',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  groupByStatus?: boolean;

  @ApiPropertyOptional({
    description: 'Include performance metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePerformanceMetrics?: boolean;
}
