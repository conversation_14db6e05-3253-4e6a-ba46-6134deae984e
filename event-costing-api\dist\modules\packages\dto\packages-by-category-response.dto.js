"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesByCategoryResponseDto = exports.CategoryWithPackagesDto = exports.CategoryPackageDto = exports.CategoryPackageOptionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
let CategoryPackageOptionDto = class CategoryPackageOptionDto {
    id;
    option_name;
    description;
    price_adjustment;
    cost_adjustment;
    is_default_for_package;
    is_required;
};
exports.CategoryPackageOptionDto = CategoryPackageOptionDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryPackageOptionDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryPackageOptionDto.prototype, "option_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], CategoryPackageOptionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], CategoryPackageOptionDto.prototype, "price_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], CategoryPackageOptionDto.prototype, "cost_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], CategoryPackageOptionDto.prototype, "is_default_for_package", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], CategoryPackageOptionDto.prototype, "is_required", void 0);
exports.CategoryPackageOptionDto = CategoryPackageOptionDto = __decorate([
    (0, swagger_1.ApiExtraModels)()
], CategoryPackageOptionDto);
let CategoryPackageDto = class CategoryPackageDto {
    id;
    name;
    description;
    quantity_basis;
    price;
    unit_base_cost;
    options;
};
exports.CategoryPackageDto = CategoryPackageDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryPackageDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryPackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], CategoryPackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryPackageDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], CategoryPackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], CategoryPackageDto.prototype, "unit_base_cost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, type: [CategoryPackageOptionDto] }),
    __metadata("design:type", Array)
], CategoryPackageDto.prototype, "options", void 0);
exports.CategoryPackageDto = CategoryPackageDto = __decorate([
    (0, swagger_1.ApiExtraModels)()
], CategoryPackageDto);
class CategoryWithPackagesDto {
    id;
    name;
    display_order;
    packages;
}
exports.CategoryWithPackagesDto = CategoryWithPackagesDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryWithPackagesDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CategoryWithPackagesDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], CategoryWithPackagesDto.prototype, "display_order", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [CategoryPackageDto] }),
    __metadata("design:type", Array)
], CategoryWithPackagesDto.prototype, "packages", void 0);
class PackagesByCategoryResponseDto {
    categories;
}
exports.PackagesByCategoryResponseDto = PackagesByCategoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [CategoryWithPackagesDto] }),
    __metadata("design:type", Array)
], PackagesByCategoryResponseDto.prototype, "categories", void 0);
//# sourceMappingURL=packages-by-category-response.dto.js.map