"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TemplateManagementController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const template_consolidated_service_1 = require("../services/template-consolidated.service");
const template_complete_data_dto_1 = require("../dto/template-complete-data.dto");
const template_filters_dto_1 = require("../dto/template-filters.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../auth/decorators/get-current-user.decorator");
let TemplateManagementController = TemplateManagementController_1 = class TemplateManagementController {
    templateConsolidatedService;
    logger = new common_1.Logger(TemplateManagementController_1.name);
    constructor(templateConsolidatedService) {
        this.templateConsolidatedService = templateConsolidatedService;
    }
    async getManagementData(filters, user) {
        this.logger.log(`User ${user.email} requesting template management data`);
        if (typeof filters.venueIds === 'string') {
            filters.venueIds = filters.venueIds.split(',').filter(id => id.trim());
        }
        if (typeof filters.tags === 'string') {
            filters.tags = filters.tags.split(',').filter(tag => tag.trim());
        }
        if (filters.myTemplatesOnly === undefined) {
            filters.myTemplatesOnly = true;
        }
        return this.templateConsolidatedService.getTemplateManagementData(filters, user);
    }
    async getTemplateDetailData(id, user) {
        this.logger.log(`User ${user.email} requesting template detail data for ID: ${id}`);
        return this.templateConsolidatedService.getTemplateDetailData(id, user);
    }
    async getManagementSummary(user) {
        this.logger.log(`User ${user.email} requesting template management summary`);
        const startTime = Date.now();
        const managementData = await this.templateConsolidatedService.getTemplateManagementData({}, user);
        const templates = managementData.templates.data;
        const totalTemplates = managementData.templates.totalCount;
        const templatesByEventType = managementData.eventTypes.map(eventType => ({
            eventType: eventType.name,
            count: templates.filter(template => template.event_type === eventType.name).length,
        }));
        const templatesByCategory = managementData.categories.map(category => ({
            categoryId: category.id,
            categoryName: category.name,
            count: 0,
        }));
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const templatesCreatedThisWeek = templates.filter(template => new Date(template.created_at) >= oneWeekAgo).length;
        const templatesCreatedThisMonth = templates.filter(template => new Date(template.created_at) >= oneMonthAgo).length;
        const loadTime = Date.now() - startTime;
        return {
            totalTemplates,
            activeTemplates: managementData.summary?.activeTemplates || 0,
            inactiveTemplates: managementData.summary?.inactiveTemplates || 0,
            publicTemplates: managementData.summary?.publicTemplates || 0,
            privateTemplates: managementData.summary?.privateTemplates || 0,
            templatesByEventType,
            templatesByCategory,
            recentActivity: {
                templatesCreatedThisWeek,
                templatesCreatedThisMonth,
                mostUsedTemplate: templates[0] || null,
            },
            metadata: {
                loadTime,
                timestamp: new Date().toISOString(),
            },
        };
    }
};
exports.TemplateManagementController = TemplateManagementController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get complete template management data',
        description: 'Consolidated endpoint that returns templates, categories, event types, packages, and statistics in a single response. Replaces the need for multiple separate API calls for template management.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Complete template management data with metadata',
        type: template_complete_data_dto_1.TemplateCompleteDataDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to load template management data.',
    }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for template name or description' }),
    (0, swagger_1.ApiQuery)({ name: 'eventType', required: false, description: 'Filter by event type' }),
    (0, swagger_1.ApiQuery)({ name: 'categoryId', required: false, description: 'Filter by category ID' }),
    (0, swagger_1.ApiQuery)({ name: 'cityId', required: false, description: 'Filter by city ID' }),
    (0, swagger_1.ApiQuery)({ name: 'venueIds', required: false, description: 'Filter by venue IDs (comma-separated)' }),
    (0, swagger_1.ApiQuery)({ name: 'minAttendees', required: false, description: 'Minimum attendees filter' }),
    (0, swagger_1.ApiQuery)({ name: 'maxAttendees', required: false, description: 'Maximum attendees filter' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by template status (all/active/inactive)' }),
    (0, swagger_1.ApiQuery)({ name: 'visibility', required: false, description: 'Filter by template visibility (all/public/private)' }),
    (0, swagger_1.ApiQuery)({ name: 'createdBy', required: false, description: 'Filter by template creator' }),
    (0, swagger_1.ApiQuery)({ name: 'myTemplatesOnly', required: false, description: 'Include only current user templates' }),
    (0, swagger_1.ApiQuery)({ name: 'hasPackagesFromCategory', required: false, description: 'Include templates with packages from category' }),
    (0, swagger_1.ApiQuery)({ name: 'hasCalculations', required: false, description: 'Include only templates with calculations' }),
    (0, swagger_1.ApiQuery)({ name: 'createdAfter', required: false, description: 'Filter by creation date (after)' }),
    (0, swagger_1.ApiQuery)({ name: 'createdBefore', required: false, description: 'Filter by creation date (before)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number for pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, description: 'Number of items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Sort by field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' }),
    (0, swagger_1.ApiQuery)({ name: 'includePackages', required: false, description: 'Include template packages' }),
    (0, swagger_1.ApiQuery)({ name: 'includeCalculations', required: false, description: 'Include template calculations' }),
    (0, swagger_1.ApiQuery)({ name: 'includeStatistics', required: false, description: 'Include template statistics' }),
    (0, swagger_1.ApiQuery)({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeId', required: false, description: 'Exclude template with specific ID' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [template_filters_dto_1.TemplateFiltersDto, Object]),
    __metadata("design:returntype", Promise)
], TemplateManagementController.prototype, "getManagementData", null);
__decorate([
    (0, common_1.Get)(':id/detail'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get complete template detail data',
        description: 'Consolidated endpoint that returns template details, packages, calculations, and categories in a single response for template editing/viewing.'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Complete template detail data with metadata',
        schema: {
            type: 'object',
            properties: {
                template: { type: 'object', description: 'Template details' },
                packages: { type: 'array', description: 'Template packages' },
                calculation: { type: 'object', description: 'Template calculation data' },
                categories: { type: 'array', description: 'Available categories' },
                metadata: {
                    type: 'object',
                    properties: {
                        loadTime: { type: 'number' },
                        cacheVersion: { type: 'string' },
                        userId: { type: 'string' },
                        errors: { type: 'array', items: { type: 'string' } },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Template not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to load template detail data.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TemplateManagementController.prototype, "getTemplateDetailData", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get template management summary statistics',
        description: 'Returns summary statistics about template management including counts by status, event type, and usage metrics.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Template management summary statistics',
        schema: {
            type: 'object',
            properties: {
                totalTemplates: { type: 'number', example: 25 },
                activeTemplates: { type: 'number', example: 20 },
                inactiveTemplates: { type: 'number', example: 5 },
                publicTemplates: { type: 'number', example: 15 },
                privateTemplates: { type: 'number', example: 10 },
                templatesByEventType: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            eventType: { type: 'string' },
                            count: { type: 'number' },
                        },
                    },
                },
                templatesByCategory: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            categoryId: { type: 'string' },
                            categoryName: { type: 'string' },
                            count: { type: 'number' },
                        },
                    },
                },
                recentActivity: {
                    type: 'object',
                    properties: {
                        templatesCreatedThisWeek: { type: 'number' },
                        templatesCreatedThisMonth: { type: 'number' },
                        mostUsedTemplate: { type: 'object' },
                    },
                },
                metadata: {
                    type: 'object',
                    properties: {
                        loadTime: { type: 'number' },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    __param(0, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TemplateManagementController.prototype, "getManagementSummary", null);
exports.TemplateManagementController = TemplateManagementController = TemplateManagementController_1 = __decorate([
    (0, swagger_1.ApiTags)('Template Management'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('templates/management'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [template_consolidated_service_1.TemplateConsolidatedService])
], TemplateManagementController);
//# sourceMappingURL=template-management.controller.js.map