"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackageCatalogController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCatalogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const package_catalog_service_1 = require("../services/package-catalog.service");
const package_catalog_dto_1 = require("../dto/package-catalog.dto");
const package_filters_dto_1 = require("../dto/package-filters.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../auth/decorators/get-current-user.decorator");
let PackageCatalogController = PackageCatalogController_1 = class PackageCatalogController {
    packageCatalogService;
    logger = new common_1.Logger(PackageCatalogController_1.name);
    constructor(packageCatalogService) {
        this.packageCatalogService = packageCatalogService;
    }
    async getCatalogData(filters, user) {
        this.logger.log(`User ${user.email} requesting package catalog data`);
        if (typeof filters.venueIds === 'string') {
            filters.venueIds = filters.venueIds.split(',').filter(id => id.trim());
        }
        if (typeof filters.tags === 'string') {
            filters.tags = filters.tags.split(',').filter(tag => tag.trim());
        }
        return this.packageCatalogService.getCatalogData(filters, user);
    }
    async getAdvancedCatalogData(filters, user) {
        this.logger.log(`User ${user.email} requesting advanced package catalog data`);
        if (typeof filters.venueIds === 'string') {
            filters.venueIds = filters.venueIds.split(',').filter(id => id.trim());
        }
        if (typeof filters.tags === 'string') {
            filters.tags = filters.tags.split(',').filter(tag => tag.trim());
        }
        return this.packageCatalogService.getAdvancedCatalog(filters, user);
    }
    async getCatalogSummary(user) {
        this.logger.log(`User ${user.email} requesting package catalog summary`);
        const startTime = Date.now();
        const catalogData = await this.packageCatalogService.getCatalogData({}, user);
        const packages = catalogData.packages.data;
        const totalPackages = catalogData.packages.totalCount;
        const totalCategories = catalogData.categories.length;
        const totalDivisions = catalogData.divisions.length;
        const prices = packages.map(pkg => pkg.price || 0).filter(price => price > 0);
        const averagePrice = prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
        const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;
        const packagesByCategory = catalogData.categories.map(category => ({
            categoryId: category.id,
            categoryName: category.name,
            count: packages.filter(pkg => pkg.category_id === category.id).length,
        }));
        const packagesByDivision = catalogData.divisions.map(division => ({
            divisionId: division.id,
            divisionName: division.name,
            count: packages.filter(pkg => pkg.division_id === division.id).length,
        }));
        const loadTime = Date.now() - startTime;
        return {
            totalPackages,
            totalCategories,
            totalDivisions,
            averagePrice,
            priceRange: {
                min: minPrice,
                max: maxPrice,
            },
            packagesByCategory,
            packagesByDivision,
            metadata: {
                loadTime,
                timestamp: new Date().toISOString(),
            },
        };
    }
};
exports.PackageCatalogController = PackageCatalogController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get complete package catalog data',
        description: 'Consolidated endpoint that returns packages, categories, cities, divisions, and currencies in a single response. Replaces the need for multiple separate API calls for package browsing.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Complete package catalog data with metadata',
        type: package_catalog_dto_1.PackageCatalogDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to load package catalog data.',
    }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for package name' }),
    (0, swagger_1.ApiQuery)({ name: 'categoryId', required: false, description: 'Filter by category ID' }),
    (0, swagger_1.ApiQuery)({ name: 'divisionId', required: false, description: 'Filter by division ID' }),
    (0, swagger_1.ApiQuery)({ name: 'cityId', required: false, description: 'Filter by city ID' }),
    (0, swagger_1.ApiQuery)({ name: 'venueIds', required: false, description: 'Filter by venue IDs (comma-separated)' }),
    (0, swagger_1.ApiQuery)({ name: 'currencyId', required: false, description: 'Filter by currency ID' }),
    (0, swagger_1.ApiQuery)({ name: 'minPrice', required: false, description: 'Minimum price filter' }),
    (0, swagger_1.ApiQuery)({ name: 'maxPrice', required: false, description: 'Maximum price filter' }),
    (0, swagger_1.ApiQuery)({ name: 'quantityBasis', required: false, description: 'Filter by quantity basis' }),
    (0, swagger_1.ApiQuery)({ name: 'showDeleted', required: false, description: 'Include deleted packages' }),
    (0, swagger_1.ApiQuery)({ name: 'hasOptions', required: false, description: 'Include packages with options' }),
    (0, swagger_1.ApiQuery)({ name: 'venueExclusive', required: false, description: 'Include venue-exclusive packages' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeId', required: false, description: 'Exclude package with specific ID' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number for pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, description: 'Number of items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Sort by field' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' }),
    (0, swagger_1.ApiQuery)({ name: 'includeOptions', required: false, description: 'Include package options' }),
    (0, swagger_1.ApiQuery)({ name: 'includeDependencies', required: false, description: 'Include package dependencies' }),
    (0, swagger_1.ApiQuery)({ name: 'includeAvailability', required: false, description: 'Include availability information' }),
    (0, swagger_1.ApiQuery)({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' }),
    (0, swagger_1.ApiQuery)({ name: 'isAvailable', required: false, description: 'Filter by availability status' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [package_filters_dto_1.PackageFiltersDto, Object]),
    __metadata("design:returntype", Promise)
], PackageCatalogController.prototype, "getCatalogData", null);
__decorate([
    (0, common_1.Get)('advanced'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get advanced package catalog data',
        description: 'Enhanced catalog endpoint that includes package options, dependencies, and availability information. Optimized for detailed package browsing.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Advanced package catalog data with enhanced information',
        type: package_catalog_dto_1.PackageCatalogDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to load advanced package catalog data.',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PackageCatalogController.prototype, "getAdvancedCatalogData", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get package catalog summary statistics',
        description: 'Returns summary statistics about the package catalog including counts by category, division, and price ranges.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Package catalog summary statistics',
        schema: {
            type: 'object',
            properties: {
                totalPackages: { type: 'number', example: 150 },
                totalCategories: { type: 'number', example: 12 },
                totalDivisions: { type: 'number', example: 5 },
                averagePrice: { type: 'number', example: 2500000 },
                priceRange: {
                    type: 'object',
                    properties: {
                        min: { type: 'number', example: 50000 },
                        max: { type: 'number', example: 10000000 },
                    },
                },
                packagesByCategory: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            categoryId: { type: 'string' },
                            categoryName: { type: 'string' },
                            count: { type: 'number' },
                        },
                    },
                },
                packagesByDivision: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            divisionId: { type: 'string' },
                            divisionName: { type: 'string' },
                            count: { type: 'number' },
                        },
                    },
                },
                metadata: {
                    type: 'object',
                    properties: {
                        loadTime: { type: 'number' },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    __param(0, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PackageCatalogController.prototype, "getCatalogSummary", null);
exports.PackageCatalogController = PackageCatalogController = PackageCatalogController_1 = __decorate([
    (0, swagger_1.ApiTags)('Package Catalog'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('packages/catalog'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [package_catalog_service_1.PackageCatalogService])
], PackageCatalogController);
//# sourceMappingURL=package-catalog.controller.js.map