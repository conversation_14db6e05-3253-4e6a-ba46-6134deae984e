import { SupabaseService } from 'src/core/supabase/supabase.service';
import { BatchUpdatePackagesDto } from '../dto/batch-update-packages.dto';
export declare class PackageBatchService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    batchUpdatePackages(batchUpdateDto: BatchUpdatePackagesDto): Promise<{
        updatedCount: number;
        errors: any[];
    }>;
    batchDeletePackages(packageIds: string[]): Promise<{
        deletedCount: number;
        errors: any[];
    }>;
    batchUpdatePackageStatus(packageIds: string[], isActive: boolean): Promise<{
        updatedCount: number;
        errors: any[];
    }>;
}
