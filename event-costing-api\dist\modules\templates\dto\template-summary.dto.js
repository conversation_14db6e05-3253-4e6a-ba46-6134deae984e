"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedAdminTemplatesResponse = exports.PaginatedTemplatesResponse = exports.EnhancedTemplateDetailDto = exports.TemplateDetailDto = exports.TemplateSummaryDto = exports.BaseTemplateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class BaseTemplateDto {
    id;
    name;
    description;
    event_type_id;
    city_id;
    currency_id;
    attendees;
    template_start_date;
    template_end_date;
    category_id;
    created_at;
    updated_at;
    created_by;
    is_public;
    is_deleted;
    taxes;
    discount;
}
exports.BaseTemplateDto = BaseTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, format: 'uuid' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "event_type_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, format: 'uuid' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, format: 'uuid' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], BaseTemplateDto.prototype, "attendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, type: Date }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], BaseTemplateDto.prototype, "template_start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, type: Date }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], BaseTemplateDto.prototype, "template_end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true, format: 'uuid' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], BaseTemplateDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], BaseTemplateDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ format: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BaseTemplateDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BaseTemplateDto.prototype, "is_public", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BaseTemplateDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        nullable: true,
        description: 'Tax configuration from source calculation',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BaseTemplateDto.prototype, "taxes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        nullable: true,
        description: 'Discount configuration from source calculation',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BaseTemplateDto.prototype, "discount", void 0);
class TemplateSummaryDto extends BaseTemplateDto {
    venue_ids;
}
exports.TemplateSummaryDto = TemplateSummaryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs associated with the template',
        type: [String],
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true }),
    __metadata("design:type", Array)
], TemplateSummaryDto.prototype, "venue_ids", void 0);
class PackageSelectionItemDto {
    package_id;
    option_ids;
    item_quantity;
    duration_days;
}
__decorate([
    (0, swagger_1.ApiProperty)({ format: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PackageSelectionItemDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], format: 'uuid' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true }),
    __metadata("design:type", Array)
], PackageSelectionItemDto.prototype, "option_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Object)
], PackageSelectionItemDto.prototype, "item_quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Object)
], PackageSelectionItemDto.prototype, "duration_days", void 0);
class EnhancedPackageSelectionItemDto {
    package_id;
    package_name;
    option_ids;
    option_names;
    item_quantity;
    duration_days;
}
__decorate([
    (0, swagger_1.ApiProperty)({ format: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], EnhancedPackageSelectionItemDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Package name for display purposes' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EnhancedPackageSelectionItemDto.prototype, "package_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], format: 'uuid' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true }),
    __metadata("design:type", Array)
], EnhancedPackageSelectionItemDto.prototype, "option_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Option names corresponding to option_ids for display purposes',
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], EnhancedPackageSelectionItemDto.prototype, "option_names", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Object)
], EnhancedPackageSelectionItemDto.prototype, "item_quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Object)
], EnhancedPackageSelectionItemDto.prototype, "duration_days", void 0);
class TemplateCustomItemDto {
    item_name;
    description;
    item_quantity;
    unit_price;
    unit_cost;
    currency_id;
    category_id;
    city_id;
    item_quantity_basis;
    quantity_basis;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Custom item name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "item_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Custom item description',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Item quantity' }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], TemplateCustomItemDto.prototype, "item_quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unit price' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TemplateCustomItemDto.prototype, "unit_price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Unit cost', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TemplateCustomItemDto.prototype, "unit_cost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency ID',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category ID',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City ID',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Item quantity basis', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], TemplateCustomItemDto.prototype, "item_quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Quantity basis', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateCustomItemDto.prototype, "quantity_basis", void 0);
class TemplateDetailDto extends BaseTemplateDto {
    package_selections;
    venue_ids;
    custom_items;
}
exports.TemplateDetailDto = TemplateDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The blueprint of package and option selections (IDs, quantities).',
        type: [PackageSelectionItemDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PackageSelectionItemDto),
    __metadata("design:type", Array)
], TemplateDetailDto.prototype, "package_selections", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs associated with the template',
        type: [String],
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true }),
    __metadata("design:type", Array)
], TemplateDetailDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of custom items associated with the template',
        type: [TemplateCustomItemDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TemplateCustomItemDto),
    __metadata("design:type", Array)
], TemplateDetailDto.prototype, "custom_items", void 0);
class EnhancedTemplateDetailDto extends BaseTemplateDto {
    package_selections;
    venue_ids;
    custom_items;
}
exports.EnhancedTemplateDetailDto = EnhancedTemplateDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The blueprint of package and option selections with names included for display.',
        type: [EnhancedPackageSelectionItemDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => EnhancedPackageSelectionItemDto),
    __metadata("design:type", Array)
], EnhancedTemplateDetailDto.prototype, "package_selections", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs associated with the template',
        type: [String],
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('all', { each: true }),
    __metadata("design:type", Array)
], EnhancedTemplateDetailDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of custom items associated with the template',
        type: [TemplateCustomItemDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TemplateCustomItemDto),
    __metadata("design:type", Array)
], EnhancedTemplateDetailDto.prototype, "custom_items", void 0);
class PaginatedTemplatesResponse {
    data;
    count;
}
exports.PaginatedTemplatesResponse = PaginatedTemplatesResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [TemplateSummaryDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TemplateSummaryDto),
    __metadata("design:type", Array)
], PaginatedTemplatesResponse.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 100 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PaginatedTemplatesResponse.prototype, "count", void 0);
class PaginatedAdminTemplatesResponse {
    data;
    count;
}
exports.PaginatedAdminTemplatesResponse = PaginatedAdminTemplatesResponse;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [TemplateDetailDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TemplateDetailDto),
    __metadata("design:type", Array)
], PaginatedAdminTemplatesResponse.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 50 }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], PaginatedAdminTemplatesResponse.prototype, "count", void 0);
//# sourceMappingURL=template-summary.dto.js.map