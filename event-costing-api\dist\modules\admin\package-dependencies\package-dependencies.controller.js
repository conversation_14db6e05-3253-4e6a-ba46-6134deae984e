"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackageDependenciesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageDependenciesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
const package_dependencies_service_1 = require("./package-dependencies.service");
const create_package_dependency_dto_1 = require("./dto/create-package-dependency.dto");
const package_dependency_dto_1 = require("./dto/package-dependency.dto");
let PackageDependenciesController = PackageDependenciesController_1 = class PackageDependenciesController {
    packageDependenciesService;
    logger = new common_1.Logger(PackageDependenciesController_1.name);
    constructor(packageDependenciesService) {
        this.packageDependenciesService = packageDependenciesService;
    }
    async create(packageId, createDto) {
        this.logger.log(`Received request to create dependency for package ${packageId}`);
        if (packageId === createDto.dependent_package_id) {
            throw new common_1.BadRequestException('A package cannot depend on itself.');
        }
        return await this.packageDependenciesService.create(packageId, createDto);
    }
    async findAllByPackage(packageId) {
        this.logger.log(`Received request to find all dependencies for package ${packageId}`);
        return await this.packageDependenciesService.findAllByPackage(packageId);
    }
    async remove(packageId, dependencyId) {
        this.logger.log(`Received request to delete dependency ${dependencyId} for package ${packageId}`);
        await this.packageDependenciesService.remove(dependencyId);
    }
};
exports.PackageDependenciesController = PackageDependenciesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Add a dependency relationship for a package' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Package dependency created successfully.',
        type: package_dependency_dto_1.PackageDependencyDto,
    }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict (e.g., dependency already exists)',
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Bad Request (e.g., validation error or self-dependency)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Package or dependent package not found',
    }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'The ID of the package to add a dependency to',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_package_dependency_dto_1.CreatePackageDependencyDto]),
    __metadata("design:returntype", Promise)
], PackageDependenciesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all dependencies for a specific package' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of package dependencies.',
        type: [package_dependency_dto_1.PackageDependencyDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'The ID of the package whose dependencies to list',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageDependenciesController.prototype, "findAllByPackage", null);
__decorate([
    (0, common_1.Delete)(':dependencyId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Remove a specific dependency relationship' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Package dependency deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Dependency record not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'The ID of the package owning the dependency',
    }),
    (0, swagger_1.ApiParam)({
        name: 'dependencyId',
        type: 'string',
        format: 'uuid',
        description: 'The ID of the dependency record to delete',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('dependencyId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackageDependenciesController.prototype, "remove", null);
exports.PackageDependenciesController = PackageDependenciesController = PackageDependenciesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin/Package Dependencies'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/packages/:packageId/dependencies'),
    __metadata("design:paramtypes", [package_dependencies_service_1.PackageDependenciesService])
], PackageDependenciesController);
//# sourceMappingURL=package-dependencies.controller.js.map