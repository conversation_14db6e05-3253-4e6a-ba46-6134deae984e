import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { PackagesService } from '../packages.service';
import { CategoriesService } from '../../categories/categories.service';
import { CitiesService } from '../../cities/cities.service';
import { PackageCatalogDto } from '../dto/package-catalog.dto';
import { PackageFiltersDto } from '../dto/package-filters.dto';
export declare class PackageCatalogService {
    private readonly supabaseService;
    private readonly packagesService;
    private readonly categoriesService;
    private readonly citiesService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, packagesService: PackagesService, categoriesService: CategoriesService, citiesService: CitiesService);
    getCatalogData(filters: PackageFiltersDto | undefined, user: User): Promise<PackageCatalogDto>;
    private getPackages;
    private getCategories;
    private getCities;
    private getDivisions;
    private getCurrencies;
    private extractResult;
    private collectErrors;
    getAdvancedCatalog(filters: PackageFiltersDto & {
        includeOptions?: boolean;
        includeDependencies?: boolean;
        includeAvailability?: boolean;
    }, user: User): Promise<PackageCatalogDto>;
    private getPackageOptions;
    private getPackageDependencies;
    private getPackageAvailability;
}
