{"version": 3, "file": "template-calculation.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/templates/services/template-calculation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAKwB;AACxB,8EAAqE;AAMrE,wEAAoE;AAoC7D,IAAM,0BAA0B,kCAAhC,MAAM,0BAA0B;IAGR;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,4BAA0B,CAAC,IAAI,CAAC,CAAC;IAEtE,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAKjE,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;aAC5D,IAAI,CAAC,sCAAiB,CAAC,UAAU,CAAC;aAClC,MAAM,CAAC,yCAAyC,CAAC;aACjD,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;aACpB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,MAAM,EAAE,CAAC;QAEZ,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,UAAU,KAAK,aAAa,CAAC,OAAO,EAAE,EACjE,aAAa,CAAC,KAAK,CACpB,CAAC;YACF,MAAM,IAAI,qCAA4B,CAAC,mCAAmC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,UAAU,aAAa,CAAC,CAAC;QAC3E,CAAC;QAGD,MAAM,MAAM,GAAiC;YAC3C,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,CAAC;YACnB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,KAAK;YACf,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,eAAe,EAAE,CAAC;SACnB,CAAC;QAGF,MAAM,iBAAiB,GAAuB,QAAQ,CAAC,kBAAkB,IAAI,EAAE,CAAC;QAChF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAE5E,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;aAC5D,GAAG,CAAC,0BAA0B,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC;QAEhE,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAC;YAC7D,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACvD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;QAGvD,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU,CAAmB,CAAC;YAE/F,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC/D,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,SAAS,CAAC,UAAU,YAAY,CAAC,CAAC;gBACvE,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC9B,SAAS;YACX,CAAC;YAGD,MAAM,aAAa,GAAmB,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC;gBAChF,CAAC,CAAC,cAAc,CAAC,cAAc;gBAC/B,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,WAAW,GAAgB;gBAC/B,GAAG,cAAc;gBACjB,cAAc,EAAE,aAAa;aAC9B,CAAC;YAGF,IAAI,SAAS,GAA6B,SAAS,CAAC;YAEpD,IAAI,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAExE,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAClD,KAAK,CAAC,UAAU,EAAE,IAAI,KAAK,KAAK,CACjC,CAAC;gBAGF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC9D,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC9B,SAAS;YACX,CAAC;YAGD,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,QAAQ,CAAC,SAAS,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;gBACrD,QAAQ,WAAW,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC;oBACjD,KAAK,YAAY,CAAC;oBAClB,KAAK,cAAc;wBACjB,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;wBAC9B,MAAM;oBACR,KAAK,eAAe;wBAClB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;wBAC9C,MAAM;oBACR,KAAK,gBAAgB;wBACnB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;wBAC/C,MAAM;oBACR,KAAK,WAAW,CAAC;oBACjB,KAAK,OAAO,CAAC;oBACb;wBACE,QAAQ,GAAG,CAAC,CAAC;wBACb,MAAM;gBACV,CAAC;YACH,CAAC;YAED,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;YAClC,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;YACxC,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,IAAI,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,QAAQ,GAAG,QAAQ,CAAC;YAGtC,MAAM,aAAa,GAA4B;gBAC7C,SAAS,EAAE,WAAW,CAAC,EAAE;gBACzB,WAAW,EAAE,WAAW,CAAC,IAAI;gBAC7B,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,QAAQ,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,IAAI,KAAK;gBAC7C,QAAQ;gBACR,SAAS;aACV,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAGrC,MAAM,CAAC,aAAa,IAAI,UAAU,CAAC;YACnC,MAAM,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;YAGvD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,UAAU,EAAE,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC1E,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,UAAU,EAAE,IAAI,IAAI,KAAK,CAAC;YACxD,CAAC;QACH,CAAC;QAGD,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACnE,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,SAAS,CAAC;YAC7D,UAAU;YACV,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,kBAAkB,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM;SAChD,CAAC,EAAE,CAAC,CAAC;QAEN,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAE7D,MAAM,OAAO,GAAkC;YAC7C,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM;YACtC,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,kBAAkB,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM;YAC/C,mBAAmB,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;gBAC9C,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM;gBAChD,CAAC,CAAC,CAAC;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,sBAAsB,EAAE,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS;gBACnF,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;gBAChE,CAAC,CAAC,CAAC;SACN,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAA;AAnNY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,0BAA0B,CAmNtC"}