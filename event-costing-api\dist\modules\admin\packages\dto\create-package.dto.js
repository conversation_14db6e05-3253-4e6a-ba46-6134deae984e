"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePackageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var PackageQuantityBasis;
(function (PackageQuantityBasis) {
    PackageQuantityBasis["PER_EVENT"] = "PER_EVENT";
    PackageQuantityBasis["PER_DAY"] = "PER_DAY";
    PackageQuantityBasis["PER_ATTENDEE"] = "PER_ATTENDEE";
    PackageQuantityBasis["PER_ITEM"] = "PER_ITEM";
    PackageQuantityBasis["PER_ITEM_PER_DAY"] = "PER_ITEM_PER_DAY";
    PackageQuantityBasis["PER_ATTENDEE_PER_DAY"] = "PER_ATTENDEE_PER_DAY";
})(PackageQuantityBasis || (PackageQuantityBasis = {}));
class CreatePackageDto {
    name;
    description;
    category_id;
    division_id;
    variation_group_code;
    quantity_basis;
    is_deleted;
    city_ids;
    enable_venues;
    venue_ids;
    price;
    unit_base_cost;
    currency_id;
}
exports.CreatePackageDto = CreatePackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the package',
        example: 'Standard Wedding Photography',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Detailed description of the package',
        example: 'Includes 8 hours coverage, 2 photographers...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category ID for the package',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Division ID for the package',
        example: 'e47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "division_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Variation group code for related package variations',
        example: 'WEDDING_PHOTO_V1',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "variation_group_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Defines how quantity and price interact for calculation',
        enum: PackageQuantityBasis,
        example: PackageQuantityBasis.PER_ATTENDEE,
    }),
    (0, class_validator_1.IsEnum)(PackageQuantityBasis),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether the package is deleted/inactive',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePackageDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of city IDs where this package is available',
        example: [
            'f47ac10b-58cc-4372-a567-0e02b2c3d479',
            'e47ac10b-58cc-4372-a567-0e02b2c3d479',
        ],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(undefined, { each: true }),
    __metadata("design:type", Array)
], CreatePackageDto.prototype, "city_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to enable venue-specific availability',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePackageDto.prototype, "enable_venues", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs where this package is available (if enable_venues is true)',
        example: [
            'f47ac10b-58cc-4372-a567-0e02b2c3d479',
            'e47ac10b-58cc-4372-a567-0e02b2c3d479',
        ],
        type: [String],
    }),
    (0, class_validator_1.ValidateIf)(o => o.enable_venues === true),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(undefined, { each: true }),
    __metadata("design:type", Array)
], CreatePackageDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Base price of the package',
        example: 1500000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreatePackageDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Base cost of the package (internal)',
        example: 1000000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreatePackageDto.prototype, "unit_base_cost", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency ID for the price',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_validator_1.ValidateIf)(o => o.price !== undefined || o.unit_base_cost !== undefined),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackageDto.prototype, "currency_id", void 0);
//# sourceMappingURL=create-package.dto.js.map