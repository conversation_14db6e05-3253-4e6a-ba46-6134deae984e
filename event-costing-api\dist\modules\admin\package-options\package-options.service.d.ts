import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackageOptionDto } from './dto/create-package-option.dto.js';
import { PackageOptionDto } from './dto/package-option.dto.js';
export declare class PackageOptionListQueryDto {
    limit?: number;
    offset?: number;
}
declare const UpdatePackageOptionDto_base: import("@nestjs/common").Type<Partial<CreatePackageOptionDto>>;
export declare class UpdatePackageOptionDto extends UpdatePackageOptionDto_base {
}
export declare class PackageOptionsService {
    private readonly supabaseService;
    private readonly logger;
    private TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    private _checkPackageExists;
    private _checkOptionUniqueness;
    create(packageId: string, createDto: CreatePackageOptionDto): Promise<PackageOptionDto>;
    findAll(packageId: string, queryDto: PackageOptionListQueryDto): Promise<{
        data: PackageOptionDto[];
        count: number;
        limit: number;
        offset: number;
    }>;
    findOne(packageId: string, optionId: string): Promise<PackageOptionDto>;
    update(packageId: string, optionId: string, updateDto: UpdatePackageOptionDto): Promise<PackageOptionDto>;
    remove(packageId: string, optionId: string): Promise<void>;
}
export {};
