"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportStatusResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const export_status_enum_1 = require("./../enums/export-status.enum");
const export_format_enum_1 = require("../enums/export-format.enum");
class ExportStatusResponseDto {
    exportId;
    status;
    format;
    message;
    createdAt;
    updatedAt;
    fileName;
    downloadUrl;
    errorMessage;
    completedAt;
}
exports.ExportStatusResponseDto = ExportStatusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique identifier of the export record.',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "exportId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The current status of the export job.',
        enum: export_status_enum_1.ExportStatus,
        example: export_status_enum_1.ExportStatus.COMPLETED,
    }),
    (0, class_validator_1.IsEnum)(export_status_enum_1.ExportStatus),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The export format type.',
        enum: export_format_enum_1.ExportFormat,
        example: export_format_enum_1.ExportFormat.PDF,
    }),
    (0, class_validator_1.IsEnum)(export_format_enum_1.ExportFormat),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A message providing more details about the status (e.g., error message if failed).',
        example: 'Export completed successfully.',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The timestamp when the export request was created.',
    }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ExportStatusResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The timestamp when the export job last changed status.',
        required: false,
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], ExportStatusResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the generated file (available when status is COMPLETED).',
        example: 'export_calculation_name_2023-10-27T10:30:00Z.xlsx',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "fileName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A temporary signed URL to download the generated file (available when status is COMPLETED).',
        example: 'https://your-supabase-url/storage/v1/object/sign/exports/user-id/file.xlsx?token=...',
        required: false,
    }),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "downloadUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message if the export failed',
        example: 'Failed to connect to storage.',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ExportStatusResponseDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The timestamp when the export job was completed.',
        required: false,
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], ExportStatusResponseDto.prototype, "completedAt", void 0);
//# sourceMappingURL=export-status-response.dto.js.map