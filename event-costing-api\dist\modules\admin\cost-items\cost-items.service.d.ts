import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateCostItemDto } from './dto/create-cost-item.dto.js';
import { UpdateCostItemDto } from './dto/update-cost-item.dto.js';
import { CostItemDto } from './dto/cost-item.dto.js';
export declare class CostItemsService {
    private readonly supabaseService;
    private readonly logger;
    private readonly TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    create(createDto: CreateCostItemDto): Promise<CostItemDto>;
    findAll(): Promise<CostItemDto[]>;
    findOne(id: string): Promise<CostItemDto>;
    update(id: string, updateDto: UpdateCostItemDto): Promise<CostItemDto>;
    remove(id: string): Promise<void>;
}
