import { ExportFormat } from '../enums/export-format.enum';
import { ExportStatus } from '../enums/export-status.enum';
export declare enum SortOrder {
    ASC = "asc",
    DESC = "desc"
}
export declare enum ExportSortBy {
    CREATED_AT = "created_at",
    UPDATED_AT = "updated_at",
    FORMAT = "format",
    STATUS = "status",
    CALCULATION_NAME = "calculation_name"
}
export declare class ExportManagementFiltersDto {
    calculationId?: string;
    format?: ExportFormat;
    status?: ExportStatus;
    dateStart?: string;
    dateEnd?: string;
    search?: string;
    failedOnly?: boolean;
    completedOnly?: boolean;
    pendingOnly?: boolean;
    activityDays?: number;
    page?: number;
    pageSize?: number;
    sortBy?: ExportSortBy;
    sortOrder?: SortOrder;
    includeStatistics?: boolean;
    includeActivity?: boolean;
    includeCalculations?: boolean;
    exportIds?: string[];
    minFileSize?: number;
    maxFileSize?: number;
    downloadableOnly?: boolean;
    withErrorsOnly?: boolean;
    groupByFormat?: boolean;
    groupByStatus?: boolean;
    includePerformanceMetrics?: boolean;
}
