import { useState, useEffect, useCallback, useMemo } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Tax, Discount } from "../../utils/calculationUtils";
import { updateCalculation } from "../../../../services/calculations";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { debug } from "@/lib/debugUtils";
import {
  useRenderTracker,
  useObjectReferenceTracker,
} from "@/lib/renderTracker";

/**
 * Custom hook for managing taxes and discounts in calculations
 * PERFORMANCE OPTIMIZED: Fixed object recreation and stabilized dependencies
 *
 * This hook centralizes all tax and discount functionality in one place,
 * reducing complexity and improving performance.
 *
 * @param calculationId - The ID of the calculation
 * @param initialTaxes - Initial taxes from the calculation
 * @param initialDiscount - Initial discount from the calculation
 * @returns Object with taxes, discount, and functions to manage them
 */
export function useTaxesAndDiscounts(
  calculationId: string,
  initialTaxes: Tax[] = [],
  initialDiscount?: Discount | null
) {
  const queryClient = useQueryClient();

  // PERFORMANCE OPTIMIZATION: Memoize default discount to prevent recreation
  const defaultDiscount = useMemo(
    () => ({
      name: "Discount",
      type: "fixed" as const,
      value: 0,
    }),
    []
  );

  // State for taxes and discount
  const [taxes, setTaxes] = useState<Tax[]>(initialTaxes);
  const [discount, setDiscount] = useState<Discount>(
    initialDiscount || defaultDiscount
  );

  // DEBUG: Track renders and object references
  useRenderTracker(
    "useTaxesAndDiscounts",
    {
      calculationId,
      taxesLength: taxes.length,
      discountValue: discount.value,
      hasInitialTaxes: !!initialTaxes?.length,
      hasInitialDiscount: !!initialDiscount,
    },
    { logLevel: "detailed" }
  );

  useObjectReferenceTracker("taxes", taxes);
  useObjectReferenceTracker("discount", discount);

  // PERFORMANCE OPTIMIZATION: Update state when initialTaxes or initialDiscount change
  // Use JSON.stringify for deep comparison to prevent unnecessary updates
  useEffect(() => {
    if (Array.isArray(initialTaxes)) {
      setTaxes((prevTaxes) => {
        // Only update if actually different
        if (JSON.stringify(prevTaxes) !== JSON.stringify(initialTaxes)) {
          return initialTaxes;
        }
        return prevTaxes;
      });
    } else {
      setTaxes([]);
    }
  }, [initialTaxes]);

  useEffect(() => {
    if (initialDiscount) {
      setDiscount((prevDiscount) => {
        // Only update if actually different
        if (JSON.stringify(prevDiscount) !== JSON.stringify(initialDiscount)) {
          return initialDiscount;
        }
        return prevDiscount;
      });
    } else {
      setDiscount((prevDiscount) => {
        // Only update if not already default
        if (JSON.stringify(prevDiscount) !== JSON.stringify(defaultDiscount)) {
          return defaultDiscount;
        }
        return prevDiscount;
      });
    }
  }, [initialDiscount, defaultDiscount]);

  // Mutation for updating taxes and discount
  const updateMutation = useMutation({
    mutationFn: (data: {
      status?: "draft" | "completed" | "canceled";
      taxes: Tax[];
      discount: Discount;
    }) => updateCalculation(calculationId, data),
    onSuccess: () => {
      // Invalidate the calculation query to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.calculation(calculationId),
      });
    },
    onError: (error) => {
      console.error("Error updating taxes and discount:", error);
      toast.error("Failed to save taxes and discount");
    },
  });

  // Add a new tax
  const addTax = useCallback(
    (newTax: Tax) => {
      const updatedTaxes = [...taxes, newTax];
      setTaxes(updatedTaxes);
      return updatedTaxes;
    },
    [taxes]
  );

  // Remove a tax
  const removeTax = useCallback(
    (taxId: string) => {
      const updatedTaxes = taxes.filter((tax) => tax.id !== taxId);
      setTaxes(updatedTaxes);
      return updatedTaxes;
    },
    [taxes]
  );

  // Update discount
  const updateDiscount = useCallback((newDiscount: Discount) => {
    setDiscount(newDiscount);
    return newDiscount;
  }, []);

  // Save taxes and discount to the database
  const saveTaxesAndDiscount = useCallback(
    async (status?: "draft" | "completed" | "canceled") => {
      try {
        const updateData = {
          ...(status ? { status } : {}),
          taxes,
          discount,
        };

        debug("Saving taxes and discount:", updateData);

        await updateMutation.mutateAsync(updateData);
        return true;
      } catch (error) {
        debug("Error saving taxes and discount:", error);
        return false;
      }
    },
    [taxes, discount, updateMutation]
  );

  // PERFORMANCE OPTIMIZATION: Memoize return object with minimal dependencies
  return useMemo(
    () => ({
      taxes,
      discount,
      addTax,
      removeTax,
      updateDiscount,
      saveTaxesAndDiscount,
      isUpdating: updateMutation.isPending,
    }),
    [
      // Only depend on primitive values and stable references
      taxes.length, // Only depend on length, not array reference
      JSON.stringify(taxes), // Deep comparison for taxes array
      discount.value, // Only depend on value, not object reference
      discount.type, // Include type for completeness
      discount.name, // Include name for completeness
      addTax,
      removeTax,
      updateDiscount,
      saveTaxesAndDiscount,
      updateMutation.isPending,
    ]
  );
}
