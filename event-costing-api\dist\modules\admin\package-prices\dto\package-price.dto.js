"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePriceDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PackagePriceDto {
    id;
    package_id;
    currency_id;
    price;
    unit_base_cost;
    description;
    created_at;
    updated_at;
}
exports.PackagePriceDto = PackagePriceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Price record UUID', format: 'uuid' }),
    __metadata("design:type", String)
], PackagePriceDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated package UUID', format: 'uuid' }),
    __metadata("design:type", String)
], PackagePriceDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated currency UUID', format: 'uuid' }),
    __metadata("design:type", String)
], PackagePriceDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base unit price', type: Number }),
    __metadata("design:type", Number)
], PackagePriceDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Base unit cost', type: Number }),
    __metadata("design:type", Number)
], PackagePriceDto.prototype, "unit_base_cost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Price description', nullable: true }),
    __metadata("design:type", Object)
], PackagePriceDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", String)
], PackagePriceDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", String)
], PackagePriceDto.prototype, "updated_at", void 0);
//# sourceMappingURL=package-price.dto.js.map