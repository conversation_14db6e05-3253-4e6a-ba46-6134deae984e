"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationVenueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationVenueService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let CalculationVenueService = CalculationVenueService_1 = class CalculationVenueService {
    supabaseService;
    logger = new common_1.Logger(CalculationVenueService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async addVenuesToCalculation(calculationId, venueIds) {
        if (!venueIds || venueIds.length === 0) {
            return;
        }
        this.logger.log(`Adding venues to calculation ${calculationId}: ${venueIds.join(', ')}`);
        const supabase = this.supabaseService.getClient();
        const venueEntries = venueIds.map(venueId => ({
            calculation_id: calculationId,
            venue_id: venueId,
        }));
        const { error: venueError } = await supabase
            .from('calculation_venues')
            .insert(venueEntries);
        if (venueError) {
            this.logger.error(`Failed to add venues to calculation ${calculationId}: ${venueError.message}`, venueError.stack);
        }
    }
    async updateCalculationVenues(calculationId, venueIds) {
        this.logger.log(`Updating venues for calculation ${calculationId}: ${venueIds.join(', ')}`);
        const supabase = this.supabaseService.getClient();
        try {
            await supabase
                .from('calculation_venues')
                .delete()
                .eq('calculation_id', calculationId);
            if (venueIds && venueIds.length > 0) {
                const venueEntries = venueIds.map(venueId => ({
                    calculation_id: calculationId,
                    venue_id: venueId,
                }));
                const { error: venueError } = await supabase
                    .from('calculation_venues')
                    .insert(venueEntries);
                if (venueError) {
                    this.logger.error(`Failed to update venues for calculation ${calculationId}: ${venueError.message}`, venueError.stack);
                }
            }
        }
        catch (venueError) {
            this.logger.error(`Error handling venue updates for calculation ${calculationId}: ${venueError}`);
        }
    }
    async fetchCalculationVenues(calculationId) {
        this.logger.log(`Fetching venues for calculation ID: ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: venueRelations, error: relationsError } = await supabase
            .from('calculation_venues')
            .select('venue_id')
            .eq('calculation_id', calculationId);
        if (relationsError) {
            this.logger.error(`Error fetching venue relations for calculation ${calculationId}: ${relationsError.message}`);
            return [];
        }
        if (!venueRelations || venueRelations.length === 0) {
            return [];
        }
        const venueIds = venueRelations.map(relation => relation.venue_id);
        const { data: venues, error: venuesError } = await supabase
            .from('venues')
            .select(`
        id,
        name,
        address,
        city_id,
        cities (name)
      `)
            .in('id', venueIds)
            .eq('is_deleted', false);
        if (venuesError) {
            this.logger.error(`Error fetching venues for calculation ${calculationId}: ${venuesError.message}`);
            return [];
        }
        return venues.map(venue => ({
            id: venue.id,
            name: venue.name,
            address: venue.address,
            city_id: venue.city_id,
            city_name: (venue.cities && venue.cities[0]?.name) || null,
        }));
    }
    async removeAllVenuesFromCalculation(calculationId) {
        this.logger.log(`Removing all venues from calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from('calculation_venues')
            .delete()
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Failed to remove venues from calculation ${calculationId}: ${error.message}`, error.stack);
        }
    }
    async hasVenues(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { count, error } = await supabase
            .from('calculation_venues')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Error checking venues for calculation ${calculationId}: ${error.message}`);
            return false;
        }
        return (count ?? 0) > 0;
    }
    async getVenueCount(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { count, error } = await supabase
            .from('calculation_venues')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Error counting venues for calculation ${calculationId}: ${error.message}`);
            return 0;
        }
        return count ?? 0;
    }
};
exports.CalculationVenueService = CalculationVenueService;
exports.CalculationVenueService = CalculationVenueService = CalculationVenueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationVenueService);
//# sourceMappingURL=calculation-venue.service.js.map