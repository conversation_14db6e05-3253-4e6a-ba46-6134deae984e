/**
 * Consolidated Export Management Service
 * 
 * This service implements the consolidated endpoint pattern for export management operations.
 * It replaces the need for multiple separate API calls with a single consolidated endpoint.
 */

import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { showError } from '@/lib/notifications';

/**
 * Export format type
 */
export type ExportFormat = 'pdf' | 'xlsx' | 'csv';

/**
 * Export status type
 */
export type ExportStatus = 'pending' | 'processing' | 'completed' | 'failed';

/**
 * Types for the consolidated export management response
 */
export interface ExportManagementData {
  exports: {
    data: Array<{
      id: string;
      calculation_id: string;
      format: ExportFormat;
      status: ExportStatus;
      file_url?: string;
      download_url?: string;
      error_message?: string;
      created_at: string;
      updated_at: string;
      calculations?: any;
    }>;
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  calculations: Array<{
    id: string;
    name: string;
    status: string;
    created_at: string;
  }>;
  statistics: {
    totalExports: number;
    completedExports: number;
    failedExports: number;
    pendingExports: number;
    exportsByFormat: Record<string, number>;
    exportsByStatus: Record<string, number>;
  };
  recentActivity: Array<{
    id: string;
    calculationId: string;
    calculationName: string;
    format: ExportFormat;
    status: ExportStatus;
    timestamp: string;
  }>;
  supportedFormats: ExportFormat[];
  filters: {
    applied: ExportManagementFilters;
    available: {
      formats: ExportFormat[];
      statuses: ExportStatus[];
      calculations: Array<{ id: string; name: string }>;
    };
  };
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalExports: number;
    appliedFilters: number;
  };
}

/**
 * Export management summary data
 */
export interface ExportManagementSummary {
  overview: {
    totalExports: number;
    completedExports: number;
    failedExports: number;
    pendingExports: number;
    successRate: number;
  };
  formatBreakdown: Record<string, number>;
  recentActivity: {
    exportsToday: number;
    exportsThisWeek: number;
    exportsThisMonth: number;
    averageExportsPerDay: number;
  };
  performance: {
    averageProcessingTime: number;
    fastestExport: number;
    slowestExport: number;
  };
  metadata: {
    loadTime: number;
    timestamp: string;
  };
}

/**
 * Batch export request
 */
export interface BatchExportRequest {
  calculationId: string;
  format: ExportFormat;
  recipient?: string;
}

/**
 * Batch export response
 */
export interface BatchExportResponse {
  results: Array<{
    calculationId: string;
    exportId: string;
    status: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
  metadata: {
    timestamp: string;
    userId: string;
  };
}

/**
 * Export management filters interface
 */
export interface ExportManagementFilters {
  calculationId?: string;
  format?: ExportFormat;
  status?: ExportStatus;
  dateStart?: string;
  dateEnd?: string;
  search?: string;
  failedOnly?: boolean;
  completedOnly?: boolean;
  pendingOnly?: boolean;
  activityDays?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includeStatistics?: boolean;
  includeActivity?: boolean;
  includeCalculations?: boolean;
  exportIds?: string[];
  minFileSize?: number;
  maxFileSize?: number;
  downloadableOnly?: boolean;
  withErrorsOnly?: boolean;
  groupByFormat?: boolean;
  groupByStatus?: boolean;
  includePerformanceMetrics?: boolean;
}

/**
 * Fetch complete export management data in a single API call
 * This replaces the need for multiple separate API calls:
 * 1. GET /exports/calculation/{id} (user exports)
 * 2. GET /calculations (available calculations)
 * 3. Export statistics calculation
 * 4. Recent activity data
 * 
 * @param filters - Export management filters
 * @returns Complete export management data with metadata
 */
export const getExportManagementData = async (
  filters: ExportManagementFilters = {},
): Promise<ExportManagementData> => {
  try {
    console.log(`[API] Fetching export management data with filters:`, filters);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Map frontend filters to backend parameters
    if (filters.calculationId) queryParams.append('calculationId', filters.calculationId);
    if (filters.format) queryParams.append('format', filters.format);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.dateStart) queryParams.append('dateStart', filters.dateStart);
    if (filters.dateEnd) queryParams.append('dateEnd', filters.dateEnd);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.failedOnly !== undefined) {
      queryParams.append('failedOnly', filters.failedOnly.toString());
    }
    if (filters.completedOnly !== undefined) {
      queryParams.append('completedOnly', filters.completedOnly.toString());
    }
    if (filters.pendingOnly !== undefined) {
      queryParams.append('pendingOnly', filters.pendingOnly.toString());
    }
    if (filters.activityDays) queryParams.append('activityDays', filters.activityDays.toString());
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);
    if (filters.includeStatistics !== undefined) {
      queryParams.append('includeStatistics', filters.includeStatistics.toString());
    }
    if (filters.includeActivity !== undefined) {
      queryParams.append('includeActivity', filters.includeActivity.toString());
    }
    if (filters.includeCalculations !== undefined) {
      queryParams.append('includeCalculations', filters.includeCalculations.toString());
    }
    if (filters.exportIds && filters.exportIds.length > 0) {
      queryParams.append('exportIds', filters.exportIds.join(','));
    }
    if (filters.minFileSize) queryParams.append('minFileSize', filters.minFileSize.toString());
    if (filters.maxFileSize) queryParams.append('maxFileSize', filters.maxFileSize.toString());
    if (filters.downloadableOnly !== undefined) {
      queryParams.append('downloadableOnly', filters.downloadableOnly.toString());
    }
    if (filters.withErrorsOnly !== undefined) {
      queryParams.append('withErrorsOnly', filters.withErrorsOnly.toString());
    }
    if (filters.groupByFormat !== undefined) {
      queryParams.append('groupByFormat', filters.groupByFormat.toString());
    }
    if (filters.groupByStatus !== undefined) {
      queryParams.append('groupByStatus', filters.groupByStatus.toString());
    }
    if (filters.includePerformanceMetrics !== undefined) {
      queryParams.append('includePerformanceMetrics', filters.includePerformanceMetrics.toString());
    }

    // Make single API request to get all data
    const url = `${API_ENDPOINTS.EXPORTS.MANAGEMENT.GET_ALL}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    
    const response = await authClient.get(url);
    const data = response.data as ExportManagementData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched export management data in ${loadTime}ms:`, {
      exports: data.exports.totalCount,
      calculations: data.calculations.length,
      statistics: data.statistics,
      recentActivity: data.recentActivity.length,
      errors: data.metadata.errors?.length || 0,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching export management data:`, error);
    showError('Failed to load export management data', {
      description: 'There was an error loading the export management data. Please try again.',
    });
    throw error;
  }
};

/**
 * Initiate multiple exports in batch
 * Allows creating multiple exports for different calculations or formats
 * 
 * @param requests - Array of batch export requests
 * @returns Batch export response with results and summary
 */
export const initiateBatchExports = async (
  requests: BatchExportRequest[],
): Promise<BatchExportResponse> => {
  try {
    console.log(`[API] Initiating batch export for ${requests.length} items`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to batch endpoint
    const response = await authClient.post(API_ENDPOINTS.EXPORTS.MANAGEMENT.BATCH_INITIATE, requests);
    const data = response.data as BatchExportResponse;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Initiated batch export in ${loadTime}ms:`, {
      total: data.summary.total,
      successful: data.summary.successful,
      failed: data.summary.failed,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error initiating batch export:`, error);
    showError('Failed to initiate batch export', {
      description: 'There was an error initiating the batch export. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch export management summary statistics
 * Provides overview metrics for export management
 * 
 * @returns Export management summary data
 */
export const getExportManagementSummary = async (): Promise<ExportManagementSummary> => {
  try {
    console.log(`[API] Fetching export management summary`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to summary endpoint
    const response = await authClient.get(API_ENDPOINTS.EXPORTS.MANAGEMENT.GET_SUMMARY);
    const data = response.data as ExportManagementSummary;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched export management summary in ${loadTime}ms:`, {
      overview: data.overview,
      formatBreakdown: data.formatBreakdown,
      recentActivity: data.recentActivity,
      performance: data.performance,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching export management summary:`, error);
    showError('Failed to load export management summary', {
      description: 'There was an error loading the export management summary. Please try again.',
    });
    throw error;
  }
};
