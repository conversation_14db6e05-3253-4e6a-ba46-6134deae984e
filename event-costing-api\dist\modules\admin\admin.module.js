"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const admin_users_module_1 = require("./users/admin-users.module");
const admin_dashboard_service_1 = require("./services/admin-dashboard.service");
const admin_dashboard_controller_1 = require("./controllers/admin-dashboard.controller");
const auth_module_1 = require("../auth/auth.module");
const categories_module_1 = require("../categories/categories.module");
const cities_module_1 = require("../cities/cities.module");
const divisions_module_1 = require("../divisions/divisions.module");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            admin_users_module_1.AdminUsersModule,
            auth_module_1.AuthModule,
            categories_module_1.CategoriesModule,
            cities_module_1.CitiesModule,
            divisions_module_1.DivisionsModule,
        ],
        controllers: [
            admin_dashboard_controller_1.AdminDashboardController,
        ],
        providers: [
            admin_dashboard_service_1.AdminDashboardService,
        ],
        exports: [
            admin_users_module_1.AdminUsersModule,
            admin_dashboard_service_1.AdminDashboardService,
        ],
    })
], AdminModule);
//# sourceMappingURL=admin.module.js.map