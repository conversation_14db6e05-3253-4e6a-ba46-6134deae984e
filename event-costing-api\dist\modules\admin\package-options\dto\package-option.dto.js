"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOptionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
let PackageOptionDto = class PackageOptionDto {
    id;
    applicable_package_id;
    option_code;
    option_name;
    description;
    price_adjustment;
    cost_adjustment;
    currency_id;
    example;
    option_group;
    is_default_for_package;
    is_required;
    created_at;
    updated_at;
};
exports.PackageOptionDto = PackageOptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Option record UUID', format: 'uuid' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated package UUID',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "applicable_package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Option code (unique within package/currency)' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "option_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Display name' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "option_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description', nullable: true }),
    __metadata("design:type", Object)
], PackageOptionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Price adjustment', type: Number }),
    __metadata("design:type", Number)
], PackageOptionDto.prototype, "price_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Cost adjustment', type: Number }),
    __metadata("design:type", Number)
], PackageOptionDto.prototype, "cost_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency ID', format: 'uuid' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Option group name', nullable: true }),
    __metadata("design:type", Object)
], PackageOptionDto.prototype, "option_group", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is default option?', type: Boolean }),
    __metadata("design:type", Boolean)
], PackageOptionDto.prototype, "is_default_for_package", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is required option?', type: Boolean }),
    __metadata("design:type", Boolean)
], PackageOptionDto.prototype, "is_required", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", String)
], PackageOptionDto.prototype, "updated_at", void 0);
exports.PackageOptionDto = PackageOptionDto = __decorate([
    (0, swagger_1.ApiExtraModels)()
], PackageOptionDto);
//# sourceMappingURL=package-option.dto.js.map