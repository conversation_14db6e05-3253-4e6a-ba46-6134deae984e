"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CalculationLineItemsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationLineItemsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const calculation_items_service_1 = require("../../calculation-items/calculation-items.service");
const calculation_crud_service_1 = require("../services/calculation-crud.service");
const add_package_line_item_dto_1 = require("../../calculation-items/dto/add-package-line-item.dto");
const add_custom_line_item_dto_1 = require("../../calculation-items/dto/add-custom-line-item.dto");
const update_line_item_dto_1 = require("../../calculation-items/dto/update-line-item.dto");
const line_item_dto_1 = require("../../calculation-items/dto/line-item.dto");
const item_id_response_dto_1 = require("../../calculation-items/dto/item-id-response.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../auth/decorators/get-current-user.decorator");
let CalculationLineItemsController = CalculationLineItemsController_1 = class CalculationLineItemsController {
    calculationItemsService;
    calculationCrudService;
    logger = new common_1.Logger(CalculationLineItemsController_1.name);
    constructor(calculationItemsService, calculationCrudService) {
        this.calculationItemsService = calculationItemsService;
        this.calculationCrudService = calculationCrudService;
    }
    async validateCalculationOwnership(calculationId, user) {
        this.logger.debug(`Validating ownership for calculation: ${calculationId}, user: ${user.id}`);
        try {
            await this.calculationCrudService.findCalculationRawById(calculationId, user);
            this.logger.debug(`Ownership validated for calculation: ${calculationId}`);
        }
        catch (error) {
            this.logger.warn(`Ownership validation failed for calculation: ${calculationId}, user: ${user.id}`);
            throw error;
        }
    }
    async addPackageLineItem(calculationId, addPackageDto, user) {
        this.logger.log(`User ${user.email} adding package line item to calculation ${calculationId}`);
        await this.validateCalculationOwnership(calculationId, user);
        const result = await this.calculationItemsService.addPackageLineItem(calculationId, addPackageDto, user);
        await this.calculationCrudService.triggerRecalculation(calculationId);
        this.logger.log(`Package line item added successfully to calculation ${calculationId}`);
        return result;
    }
    async addCustomLineItem(calculationId, addCustomDto, user) {
        this.logger.log(`User ${user.email} adding custom line item to calculation ${calculationId}`);
        await this.validateCalculationOwnership(calculationId, user);
        const result = await this.calculationItemsService.addCustomLineItem(calculationId, addCustomDto, user);
        await this.calculationCrudService.triggerRecalculation(calculationId);
        this.logger.log(`Custom line item added successfully to calculation ${calculationId}`);
        return result;
    }
    async updateLineItem(calculationId, lineItemId, updateDto, user) {
        this.logger.log(`User ${user.email} updating line item ${lineItemId} in calculation ${calculationId}`);
        await this.validateCalculationOwnership(calculationId, user);
        const updatedItem = await this.calculationItemsService.updateLineItem(calculationId, lineItemId, updateDto, user);
        const shouldRecalculate = updateDto.quantity !== undefined ||
            updateDto.unitPrice !== undefined ||
            updateDto.itemQuantityBasis !== undefined;
        if (shouldRecalculate) {
            this.logger.debug(`Triggering recalculation for calculation ${calculationId} after line item update`);
            await this.calculationCrudService.triggerRecalculation(calculationId);
        }
        this.logger.log(`Line item ${lineItemId} updated successfully in calculation ${calculationId}`);
        return updatedItem;
    }
    async deleteLineItem(calculationId, lineItemId, user) {
        this.logger.log(`User ${user.email} deleting line item ${lineItemId} from calculation ${calculationId}`);
        await this.validateCalculationOwnership(calculationId, user);
        await this.calculationItemsService.getLineItemById(calculationId, lineItemId);
        await this.calculationItemsService.deleteLineItem(calculationId, lineItemId, user);
        await this.calculationCrudService.triggerRecalculation(calculationId);
        this.logger.log(`Line item ${lineItemId} deleted successfully from calculation ${calculationId}`);
    }
};
exports.CalculationLineItemsController = CalculationLineItemsController;
__decorate([
    (0, common_1.Post)('package'),
    (0, swagger_1.ApiOperation)({
        summary: 'Add a package line item to a calculation',
        description: 'Adds a package-based line item and triggers recalculation',
    }),
    (0, swagger_1.ApiParam)({ name: 'calculationId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Package line item added successfully',
        type: item_id_response_dto_1.ItemIdResponse,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid package data provided.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_package_line_item_dto_1.AddPackageLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationLineItemsController.prototype, "addPackageLineItem", null);
__decorate([
    (0, common_1.Post)('custom'),
    (0, swagger_1.ApiOperation)({
        summary: 'Add a custom line item to a calculation',
        description: 'Adds a custom line item and triggers recalculation',
    }),
    (0, swagger_1.ApiParam)({ name: 'calculationId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Custom line item added successfully',
        type: item_id_response_dto_1.ItemIdResponse,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid custom item data provided.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_custom_line_item_dto_1.AddCustomLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationLineItemsController.prototype, "addCustomLineItem", null);
__decorate([
    (0, common_1.Put)(':lineItemId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a line item',
        description: 'Updates a line item and triggers recalculation if quantity or price changed',
    }),
    (0, swagger_1.ApiParam)({ name: 'calculationId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'lineItemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Line item updated successfully',
        type: line_item_dto_1.LineItemDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or line item not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('lineItemId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_line_item_dto_1.UpdateLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationLineItemsController.prototype, "updateLineItem", null);
__decorate([
    (0, common_1.Delete)(':lineItemId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a line item',
        description: 'Deletes a line item and triggers recalculation',
    }),
    (0, swagger_1.ApiParam)({ name: 'calculationId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'lineItemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiNoContentResponse)({ description: 'Line item deleted successfully' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or line item not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('lineItemId', common_1.ParseUUIDPipe)),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CalculationLineItemsController.prototype, "deleteLineItem", null);
exports.CalculationLineItemsController = CalculationLineItemsController = CalculationLineItemsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Calculation Line Items'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('calculations/:calculationId/line-items'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => calculation_crud_service_1.CalculationCrudService))),
    __metadata("design:paramtypes", [calculation_items_service_1.CalculationItemsService,
        calculation_crud_service_1.CalculationCrudService])
], CalculationLineItemsController);
//# sourceMappingURL=calculation-line-items.controller.js.map