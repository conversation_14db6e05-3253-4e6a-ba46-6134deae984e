export declare enum SortOrder {
    ASC = "asc",
    DESC = "desc"
}
export declare enum PackageSortBy {
    NAME = "name",
    PRICE = "price",
    CATEGORY = "category",
    DIVISION = "division",
    CREATED_AT = "created_at",
    UPDATED_AT = "updated_at"
}
export declare class PackageFiltersDto {
    search?: string;
    categoryId?: string;
    divisionId?: string;
    cityId?: string;
    venueIds?: string[];
    currencyId?: string;
    minPrice?: number;
    maxPrice?: number;
    quantityBasis?: string;
    showDeleted?: boolean;
    hasOptions?: boolean;
    venueExclusive?: boolean;
    excludeId?: string;
    page?: number;
    pageSize?: number;
    sortBy?: PackageSortBy;
    sortOrder?: SortOrder;
    includeOptions?: boolean;
    includeDependencies?: boolean;
    includeAvailability?: boolean;
    tags?: string[];
    isAvailable?: boolean;
}
