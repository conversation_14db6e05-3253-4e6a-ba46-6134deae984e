import { SupabaseService } from '../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CreateCalculationFromTemplateDto } from './dto/create-calculation-from-template.dto';
import { CalculationItemsService } from '../calculation-items/calculation-items.service';
export declare class CalculationTemplateService {
    private readonly supabaseService;
    private readonly calculationItemsService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, calculationItemsService: CalculationItemsService);
    createFromTemplate(templateId: string, customization: CreateCalculationFromTemplateDto, user: User): Promise<string>;
}
