"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTemplateDto = exports.PackageSelectionDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class PackageSelectionDto {
    package_id;
    option_ids;
}
exports.PackageSelectionDto = PackageSelectionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Package ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PackageSelectionDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Selected option IDs for this package',
        example: ['456e7890-e89b-12d3-a456-************'],
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], PackageSelectionDto.prototype, "option_ids", void 0);
class CreateTemplateDto {
    name;
    description;
    event_type_id;
    attendees;
    template_start_date;
    template_end_date;
    is_public;
    city_id;
    category_id;
    currency_id;
    venue_ids;
    package_selections;
}
exports.CreateTemplateDto = CreateTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Template name',
        example: 'Corporate Event Template',
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(2, 100),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template description',
        example: 'A comprehensive template for corporate events',
        maxLength: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 1000),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event type ID (UUID reference to event_types table)',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "event_type_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of attendees',
        example: 100,
        minimum: 1,
        maximum: 100000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100000),
    __metadata("design:type", Number)
], CreateTemplateDto.prototype, "attendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template start datetime (ISO 8601 format)',
        example: '2024-06-01T00:00:00.000Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "template_start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template end datetime (ISO 8601 format)',
        example: '2024-06-03T23:59:59.999Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "template_end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the template is public',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateTemplateDto.prototype, "is_public", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City ID where the template is applicable',
        example: '789e0123-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Template category ID',
        example: '012e3456-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency ID for the template',
        example: '345e6789-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateTemplateDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Venue IDs associated with this template',
        example: ['567e8901-e89b-12d3-a456-************'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], CreateTemplateDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Package selections for the template',
        type: [PackageSelectionDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PackageSelectionDto),
    __metadata("design:type", Array)
], CreateTemplateDto.prototype, "package_selections", void 0);
//# sourceMappingURL=create-template.dto.js.map