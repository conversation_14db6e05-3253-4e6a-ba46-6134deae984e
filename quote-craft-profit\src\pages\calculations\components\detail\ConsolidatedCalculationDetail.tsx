/**
 * Example component demonstrating the consolidated calculation data approach
 *
 * This component shows how to use the new consolidated endpoint to replace
 * multiple API calls with a single, efficient data fetch.
 */

import React from "react";
import { useParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Clock, Database, Zap, AlertCircle, CheckCircle } from "lucide-react";
import {
  useConsolidatedCalculationData,
  useConsolidatedCalculationSelectors,
  isConsolidatedDataLoaded,
} from "../../hooks/data/useConsolidatedCalculationData";
import { formatCurrency } from "@/lib/utils";
import { useSafeUUID } from "@/lib/uuidValidation";

/**
 * Main component demonstrating consolidated data usage
 * RACE CONDITION FIX: Added UUID validation to prevent API calls with invalid IDs
 */
export const ConsolidatedCalculationDetail: React.FC = () => {
  const { id: calculationId } = useParams<{ id: string }>();

  // CRITICAL FIX: Use safe UUID validation to prevent race conditions
  const { isValid, validId, shouldLoadData } = useSafeUUID(calculationId);

  // Single hook call replaces 4 separate API calls
  // Only make API call if we have a valid UUID
  const { data, isLoading, isError, error, refetch } =
    useConsolidatedCalculationData(validId, {
      enabled: shouldLoadData, // Only enable query if ID is valid
      staleTime: 5 * 60 * 1000, // 5 minutes
      onSuccess: (data) => {
        console.log("Consolidated data loaded successfully:", data.metadata);
      },
      onError: (error) => {
        console.error("Failed to load consolidated data:", error);
      },
    });

  // CRITICAL FIX: Early return for invalid IDs to prevent errors
  if (!isValid) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Invalid calculation ID format. Please check the URL and try again.
        </AlertDescription>
      </Alert>
    );
  }

  // Use selectors for convenient data access
  const {
    calculation,
    lineItems,
    packagesByCategory,
    categories,
    totals,
    currency,
    location,
    performance,
  } = useConsolidatedCalculationSelectors(data);

  // Loading state
  if (isLoading) {
    return <ConsolidatedLoadingSkeleton />;
  }

  // Error state
  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load calculation data: {error?.message}
          <button
            onClick={() => refetch()}
            className="ml-2 underline hover:no-underline"
          >
            Try again
          </button>
        </AlertDescription>
      </Alert>
    );
  }

  // Data not loaded
  if (!isConsolidatedDataLoaded(data)) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>No calculation data available.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Performance Metrics Card */}
      {performance && <PerformanceMetricsCard performance={performance} />}

      {/* Calculation Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl">{calculation.name}</CardTitle>
            <Badge variant="outline">{calculation.status}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Currency</p>
              <p className="font-medium">{currency?.code || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">City</p>
              <p className="font-medium">{location.city?.name || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Attendees</p>
              <p className="font-medium">{calculation.attendees || "N/A"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Summary */}
      {totals && (
        <Card>
          <CardHeader>
            <CardTitle>Financial Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Subtotal</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(totals.subtotal, currency?.code)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(totals.total, currency?.code)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Cost</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(totals.totalCost, currency?.code)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Estimated Profit
                </p>
                <p className="text-lg font-semibold text-green-600">
                  {formatCurrency(totals.estimatedProfit, currency?.code)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Line Items */}
      <Card>
        <CardHeader>
          <CardTitle>Line Items ({lineItems.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {lineItems.length === 0 ? (
            <p className="text-muted-foreground">No line items added yet.</p>
          ) : (
            <div className="space-y-2">
              {lineItems.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">
                      Qty: {item.quantity} × {item.item_quantity_basis} days
                      {item.is_custom && (
                        <Badge variant="secondary" className="ml-2">
                          Custom
                        </Badge>
                      )}
                    </p>
                  </div>
                  <p className="font-semibold">
                    {formatCurrency(item.total_price, currency?.code)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Packages */}
      <Card>
        <CardHeader>
          <CardTitle>
            Available Packages ({packagesByCategory.length} categories)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {packagesByCategory.length === 0 ? (
            <p className="text-muted-foreground">No packages available.</p>
          ) : (
            <div className="space-y-4">
              {packagesByCategory.map((category) => (
                <div key={category.id}>
                  <h4 className="font-medium mb-2">{category.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {category.packages.map((pkg) => (
                      <div key={pkg.id} className="p-2 border rounded text-sm">
                        <p className="font-medium">{pkg.name}</p>
                        <p className="text-muted-foreground">
                          {formatCurrency(pkg.price, currency?.code)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Performance metrics display component
 */
const PerformanceMetricsCard: React.FC<{
  performance: NonNullable<
    ReturnType<typeof useConsolidatedCalculationSelectors>["performance"]
  >;
}> = ({ performance }) => {
  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Zap className="h-4 w-4 text-green-600" />
          Consolidated API Performance
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-green-600" />
            <div>
              <p className="text-muted-foreground">Load Time</p>
              <p className="font-semibold">{performance.loadTime}ms</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4 text-green-600" />
            <div>
              <p className="text-muted-foreground">API Calls</p>
              <p className="font-semibold">1 (vs 4)</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div>
              <p className="text-muted-foreground">Errors</p>
              <p className="font-semibold">{performance.errorCount}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="text-green-600 border-green-600"
            >
              v{performance.cacheVersion}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Loading skeleton for consolidated data
 */
const ConsolidatedLoadingSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-64" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i}>
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-6 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i}>
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div>
                  <Skeleton className="h-5 w-32 mb-2" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <Skeleton className="h-5 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
