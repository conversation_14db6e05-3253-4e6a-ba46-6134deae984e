"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportManagementFiltersDto = exports.ExportSortBy = exports.SortOrder = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const export_format_enum_1 = require("../enums/export-format.enum");
const export_status_enum_1 = require("../enums/export-status.enum");
var SortOrder;
(function (SortOrder) {
    SortOrder["ASC"] = "asc";
    SortOrder["DESC"] = "desc";
})(SortOrder || (exports.SortOrder = SortOrder = {}));
var ExportSortBy;
(function (ExportSortBy) {
    ExportSortBy["CREATED_AT"] = "created_at";
    ExportSortBy["UPDATED_AT"] = "updated_at";
    ExportSortBy["FORMAT"] = "format";
    ExportSortBy["STATUS"] = "status";
    ExportSortBy["CALCULATION_NAME"] = "calculation_name";
})(ExportSortBy || (exports.ExportSortBy = ExportSortBy = {}));
class ExportManagementFiltersDto {
    calculationId;
    format;
    status;
    dateStart;
    dateEnd;
    search;
    failedOnly;
    completedOnly;
    pendingOnly;
    activityDays;
    page;
    pageSize;
    sortBy;
    sortOrder;
    includeStatistics;
    includeActivity;
    includeCalculations;
    exportIds;
    minFileSize;
    maxFileSize;
    downloadableOnly;
    withErrorsOnly;
    groupByFormat;
    groupByStatus;
    includePerformanceMetrics;
}
exports.ExportManagementFiltersDto = ExportManagementFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by calculation ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "calculationId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by export format',
        enum: export_format_enum_1.ExportFormat,
        example: export_format_enum_1.ExportFormat.PDF,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(export_format_enum_1.ExportFormat),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by export status',
        enum: export_status_enum_1.ExportStatus,
        example: export_status_enum_1.ExportStatus.COMPLETED,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(export_status_enum_1.ExportStatus),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by date range - start date',
        type: String,
        format: 'date-time',
        example: '2024-01-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "dateStart", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by date range - end date',
        type: String,
        format: 'date-time',
        example: '2024-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "dateEnd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search term for calculation name',
        example: 'corporate event',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only failed exports',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "failedOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only completed exports',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "completedOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only pending/processing exports',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "pendingOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of days for recent activity',
        example: 7,
        default: 7,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], ExportManagementFiltersDto.prototype, "activityDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Page number for pagination',
        example: 1,
        default: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ExportManagementFiltersDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of items per page',
        example: 10,
        default: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ExportManagementFiltersDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort by field',
        enum: ExportSortBy,
        example: ExportSortBy.CREATED_AT,
        default: ExportSortBy.CREATED_AT,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ExportSortBy),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        enum: SortOrder,
        example: SortOrder.DESC,
        default: SortOrder.DESC,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], ExportManagementFiltersDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include export statistics in response',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "includeStatistics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include recent activity in response',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "includeActivity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include calculation details in response',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "includeCalculations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by specific export IDs',
        type: [String],
        example: ['export-1', 'export-2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ExportManagementFiltersDto.prototype, "exportIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by file size range - minimum size in bytes',
        example: 1024,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ExportManagementFiltersDto.prototype, "minFileSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by file size range - maximum size in bytes',
        example: 10485760,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ExportManagementFiltersDto.prototype, "maxFileSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include exports with download URLs only',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "downloadableOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include exports with errors only',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "withErrorsOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Group results by format',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "groupByFormat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Group results by status',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "groupByStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include performance metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ExportManagementFiltersDto.prototype, "includePerformanceMetrics", void 0);
//# sourceMappingURL=export-management-filters.dto.js.map