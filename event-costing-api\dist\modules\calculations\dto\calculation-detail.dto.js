"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiscountDetailDto = exports.TaxDetailItemDto = exports.CalculationDetailDto = exports.CityReferenceDto = exports.CalculationCustomItemDto = exports.CalculationLineItemDto = exports.CalculationLineItemOptionDto = exports.CurrencySummaryDto = exports.EventSummaryDto = exports.ClientSummaryDto = void 0;
const calculation_status_enum_1 = require("../enums/calculation-status.enum");
const swagger_1 = require("@nestjs/swagger");
const venue_reference_dto_1 = require("../../venues/dto/venue-reference.dto");
class ClientSummaryDto {
    id;
    client_name;
    contact_person;
    email;
}
exports.ClientSummaryDto = ClientSummaryDto;
class EventSummaryDto {
    id;
    event_name;
    event_start_datetime;
    event_end_datetime;
}
exports.EventSummaryDto = EventSummaryDto;
class CurrencySummaryDto {
    id;
    code;
}
exports.CurrencySummaryDto = CurrencySummaryDto;
class CalculationLineItemOptionDto {
    id;
    option_name_snapshot;
    price_adjustment_snapshot;
}
exports.CalculationLineItemOptionDto = CalculationLineItemOptionDto;
class CalculationLineItemDto {
    id;
    package_id;
    item_name_snapshot;
    option_summary_snapshot;
    item_quantity;
    duration_days;
    unit_base_price;
    options_total_adjustment;
    calculated_line_total;
    notes;
    unit_base_cost_snapshot;
    options_total_cost_snapshot;
    calculated_line_cost;
    options;
}
exports.CalculationLineItemDto = CalculationLineItemDto;
class CalculationCustomItemDto {
    id;
    item_name;
    description;
    quantity;
    unit_price;
    unit_cost;
}
exports.CalculationCustomItemDto = CalculationCustomItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CalculationCustomItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Item name',
    }),
    __metadata("design:type", String)
], CalculationCustomItemDto.prototype, "item_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Item description',
    }),
    __metadata("design:type", Object)
], CalculationCustomItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Item quantity',
    }),
    __metadata("design:type", Number)
], CalculationCustomItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Item unit price',
    }),
    __metadata("design:type", Number)
], CalculationCustomItemDto.prototype, "unit_price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Item unit cost',
    }),
    __metadata("design:type", Number)
], CalculationCustomItemDto.prototype, "unit_cost", void 0);
class CityReferenceDto {
    id;
    name;
}
exports.CityReferenceDto = CityReferenceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CityReferenceDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'City name',
    }),
    __metadata("design:type", String)
], CityReferenceDto.prototype, "name", void 0);
class CalculationDetailDto {
    id;
    name;
    status;
    event_type_id;
    attendees;
    event_start_date;
    event_end_date;
    notes;
    version_notes;
    created_at;
    updated_at;
    created_by;
    currency;
    city;
    client;
    event;
    venues;
    line_items;
    custom_items;
    subtotal;
    taxes;
    discount;
    total;
    total_cost;
    estimated_profit;
}
exports.CalculationDetailDto = CalculationDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Calculation name',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        enum: calculation_status_enum_1.CalculationStatus,
        description: 'Calculation status',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        nullable: true,
        description: 'Event type ID (foreign key reference)',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "event_type_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        nullable: true,
        description: 'Number of attendees',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "attendees", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        format: 'date',
        description: 'Event start date',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "event_start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        format: 'date',
        description: 'Event end date',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "event_end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Calculation notes',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Version notes',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "version_notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'date-time',
        description: 'Created at',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'date-time',
        description: 'Updated at',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        description: 'Created by user ID',
    }),
    __metadata("design:type", String)
], CalculationDetailDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => CurrencySummaryDto,
        nullable: true,
        description: 'Currency details',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => CityReferenceDto,
        nullable: true,
        description: 'City details',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => ClientSummaryDto,
        nullable: true,
        description: 'Client details',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "client", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => EventSummaryDto,
        nullable: true,
        description: 'Event details',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "event", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => venue_reference_dto_1.VenueReferenceDto,
        isArray: true,
        description: 'Venues associated with this calculation',
    }),
    __metadata("design:type", Array)
], CalculationDetailDto.prototype, "venues", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => CalculationLineItemDto,
        isArray: true,
        description: 'Line items',
    }),
    __metadata("design:type", Array)
], CalculationDetailDto.prototype, "line_items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => CalculationCustomItemDto,
        isArray: true,
        description: 'Custom items',
    }),
    __metadata("design:type", Array)
], CalculationDetailDto.prototype, "custom_items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Subtotal before taxes and discounts',
    }),
    __metadata("design:type", Number)
], CalculationDetailDto.prototype, "subtotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => TaxDetailItemDto,
        isArray: true,
        nullable: true,
        description: 'Applied taxes details (JSONB)',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "taxes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => DiscountDetailDto,
        nullable: true,
        description: 'Applied discount details (JSONB)',
    }),
    __metadata("design:type", Object)
], CalculationDetailDto.prototype, "discount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Grand total after taxes and discounts',
    }),
    __metadata("design:type", Number)
], CalculationDetailDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Total estimated cost of goods/services',
    }),
    __metadata("design:type", Number)
], CalculationDetailDto.prototype, "total_cost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Estimated profit (total - total_cost)',
    }),
    __metadata("design:type", Number)
], CalculationDetailDto.prototype, "estimated_profit", void 0);
class TaxDetailItemDto {
    name;
    rate;
    amount;
}
exports.TaxDetailItemDto = TaxDetailItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], TaxDetailItemDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], TaxDetailItemDto.prototype, "rate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], TaxDetailItemDto.prototype, "amount", void 0);
class DiscountDetailDto {
    name;
    amount;
    description;
    percentage;
}
exports.DiscountDetailDto = DiscountDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], DiscountDetailDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], DiscountDetailDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], DiscountDetailDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], DiscountDetailDto.prototype, "percentage", void 0);
//# sourceMappingURL=calculation-detail.dto.js.map