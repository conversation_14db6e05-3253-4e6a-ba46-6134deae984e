import { CategoryDto } from '../../categories/dto/category.dto';
import { CityDto } from '../../cities/dto/city.dto';
import { DivisionDto } from '../../divisions/dto/division.dto';
import { AdminDashboardFiltersDto } from './admin-dashboard-filters.dto';
export declare class SystemOverviewDto {
    totalPackages: number;
    totalTemplates: number;
    totalCalculations: number;
    totalUsers: number;
    totalCategories: number;
    totalCities: number;
    totalDivisions: number;
}
export declare class PackagesOverviewDto {
    totalCount: number;
    activeCount: number;
    deletedCount: number;
}
export declare class TemplatesOverviewDto {
    totalCount: number;
    activeCount: number;
    publicCount: number;
    privateCount: number;
}
export declare class CalculationsOverviewDto {
    totalCount: number;
    draftCount: number;
    finalizedCount: number;
}
export declare class UsersOverviewDto {
    totalCount: number;
    activeCount: number;
}
export declare class RecentActivityDto {
    type: string;
    id: string;
    name: string;
    action: string;
    timestamp: string;
}
export declare class AdminDashboardFilterInfoDto {
    applied: AdminDashboardFiltersDto;
}
export declare class AdminDashboardMetadataDto {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    dataPoints: {
        categoriesCount: number;
        citiesCount: number;
        divisionsCount: number;
        packagesCount: number;
        templatesCount: number;
        calculationsCount: number;
        usersCount: number;
        recentActivityCount: number;
    };
}
export declare class AdminDashboardDataDto {
    overview: SystemOverviewDto;
    categories: CategoryDto[];
    cities: CityDto[];
    divisions: DivisionDto[];
    packages: PackagesOverviewDto;
    templates: TemplatesOverviewDto;
    calculations: CalculationsOverviewDto;
    users: UsersOverviewDto;
    recentActivity: RecentActivityDto[];
    filters: AdminDashboardFilterInfoDto;
    metadata: AdminDashboardMetadataDto;
}
