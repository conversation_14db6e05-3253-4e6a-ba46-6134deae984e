"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchPackageOptionsResponseDto = exports.BatchPackageOptionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
let BatchPackageOptionDto = class BatchPackageOptionDto {
    id;
    option_name;
    description;
    price_adjustment;
    cost_adjustment;
    is_default_for_package;
    is_required;
};
exports.BatchPackageOptionDto = BatchPackageOptionDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BatchPackageOptionDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], BatchPackageOptionDto.prototype, "option_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], BatchPackageOptionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], BatchPackageOptionDto.prototype, "price_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], BatchPackageOptionDto.prototype, "cost_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], BatchPackageOptionDto.prototype, "is_default_for_package", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], BatchPackageOptionDto.prototype, "is_required", void 0);
exports.BatchPackageOptionDto = BatchPackageOptionDto = __decorate([
    (0, swagger_1.ApiExtraModels)()
], BatchPackageOptionDto);
class BatchPackageOptionsResponseDto {
    options;
}
exports.BatchPackageOptionsResponseDto = BatchPackageOptionsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: Object }),
    __metadata("design:type", Object)
], BatchPackageOptionsResponseDto.prototype, "options", void 0);
//# sourceMappingURL=batch-package-options-response.dto.js.map