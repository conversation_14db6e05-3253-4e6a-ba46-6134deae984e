"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCalculationStatusDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const calculation_status_enum_1 = require("../enums/calculation-status.enum");
class UpdateCalculationStatusDto {
    status;
}
exports.UpdateCalculationStatusDto = UpdateCalculationStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The new status for the calculation',
        enum: calculation_status_enum_1.CalculationStatus,
        example: calculation_status_enum_1.CalculationStatus.COMPLETED,
    }),
    (0, class_validator_1.IsEnum)(calculation_status_enum_1.CalculationStatus),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateCalculationStatusDto.prototype, "status", void 0);
//# sourceMappingURL=update-calculation-status.dto.js.map