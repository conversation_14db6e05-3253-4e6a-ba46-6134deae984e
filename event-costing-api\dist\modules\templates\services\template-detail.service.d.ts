import { SupabaseService } from 'src/core/supabase/supabase.service';
import { TemplateDetailDto, EnhancedTemplateDetailDto } from '../dto/template-summary.dto';
import { TemplateVenueService } from './template-venue.service';
export declare class TemplateDetailService {
    private readonly supabaseService;
    private readonly templateVenueService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateVenueService: TemplateVenueService);
    findOnePublic(id: string): Promise<TemplateDetailDto>;
    findOnePublicEnhanced(id: string): Promise<EnhancedTemplateDetailDto>;
    private enhanceTemplateWithNames;
}
