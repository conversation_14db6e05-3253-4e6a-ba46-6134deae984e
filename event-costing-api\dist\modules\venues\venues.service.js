"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VenuesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VenuesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let VenuesService = VenuesService_1 = class VenuesService {
    supabaseService;
    logger = new common_1.Logger(VenuesService_1.name);
    tableName = 'venues';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        this.logger.log('Finding all venues');
        return this.findAllWithFilters();
    }
    async findAllWithFilters(cityId, active) {
        this.logger.log(`Finding venues with filters: cityId=${cityId}, active=${active}`);
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from(this.tableName)
            .select(`
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        cities (name)
      `)
            .eq('is_deleted', false);
        if (cityId) {
            query = query.eq('city_id', cityId);
        }
        if (active !== undefined) {
            query = query.eq('is_active', active);
        }
        else {
            query = query.eq('is_active', true);
        }
        query = query.order('name');
        const { data, error } = await query;
        if (error) {
            this.logger.error(`Error fetching venues: ${error.message}`);
            throw error;
        }
        if (data.length > 0) {
            this.logger.debug(`Venue data structure: ${JSON.stringify(data[0])}`);
            this.logger.debug(`Cities structure: ${JSON.stringify(data[0].cities)}`);
        }
        return data.map(venue => {
            let cityName = undefined;
            if (venue.cities) {
                if (Array.isArray(venue.cities) && venue.cities.length > 0) {
                    cityName = venue.cities[0].name;
                }
                else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
                    cityName = venue.cities.name;
                }
            }
            return {
                id: venue.id,
                name: venue.name,
                address: venue.address,
                city_id: venue.city_id,
                city_name: cityName,
                classification: venue.classification,
                capacity: venue.capacity,
                image_url: venue.image_url,
                features: venue.features,
            };
        });
    }
    async findByIds(ids) {
        if (!ids || ids.length === 0) {
            return [];
        }
        this.logger.log(`Finding venues by IDs: ${ids.join(', ')}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(`
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        cities (name)
      `)
            .in('id', ids)
            .eq('is_deleted', false)
            .eq('is_active', true)
            .order('name');
        if (error) {
            this.logger.error(`Error fetching venues by IDs: ${error.message}`);
            throw error;
        }
        return data.map(venue => {
            let cityName = undefined;
            if (venue.cities) {
                if (Array.isArray(venue.cities) && venue.cities.length > 0) {
                    cityName = venue.cities[0].name;
                }
                else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
                    cityName = venue.cities.name;
                }
            }
            return {
                id: venue.id,
                name: venue.name,
                address: venue.address,
                city_id: venue.city_id,
                city_name: cityName,
                classification: venue.classification,
                capacity: venue.capacity,
                image_url: venue.image_url,
                features: venue.features,
            };
        });
    }
    async findWithEnhancedFilters(cityId, active, classification, minCapacity, maxCapacity) {
        this.logger.log(`Finding venues with enhanced filters: cityId=${cityId}, active=${active}, classification=${classification}, minCapacity=${minCapacity}, maxCapacity=${maxCapacity}`);
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from(this.tableName)
            .select(`
        id,
        name,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        cities (name)
      `)
            .eq('is_deleted', false);
        if (cityId) {
            query = query.eq('city_id', cityId);
        }
        if (active !== undefined) {
            query = query.eq('is_active', active);
        }
        else {
            query = query.eq('is_active', true);
        }
        if (classification) {
            query = query.eq('classification', classification);
        }
        if (minCapacity !== undefined) {
            query = query.gte('capacity', minCapacity);
        }
        if (maxCapacity !== undefined) {
            query = query.lte('capacity', maxCapacity);
        }
        query = query.order('capacity', { ascending: false }).order('name');
        const { data, error } = await query;
        if (error) {
            this.logger.error(`Error fetching venues with enhanced filters: ${error.message}`);
            throw error;
        }
        return data.map(venue => {
            let cityName = undefined;
            if (venue.cities) {
                if (Array.isArray(venue.cities) && venue.cities.length > 0) {
                    cityName = venue.cities[0].name;
                }
                else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
                    cityName = venue.cities.name;
                }
            }
            return {
                id: venue.id,
                name: venue.name,
                address: venue.address,
                city_id: venue.city_id,
                city_name: cityName,
                classification: venue.classification,
                capacity: venue.capacity,
                image_url: venue.image_url,
                features: venue.features,
            };
        });
    }
    async findAllAdmin(queryDto) {
        this.logger.log(`Finding all venues (admin) with filters: ${JSON.stringify(queryDto)}`);
        const supabase = this.supabaseService.getClient();
        const page = queryDto.page || 1;
        const pageSize = queryDto.pageSize || 10;
        const sortBy = queryDto.sortBy || 'name';
        const sortOrder = queryDto.sortOrder || 'asc';
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        let query = supabase.from(this.tableName).select(`
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `, { count: 'exact' });
        if (queryDto.search) {
            query = query.ilike('name', `%${queryDto.search}%`);
        }
        if (queryDto.cityId) {
            query = query.eq('city_id', queryDto.cityId);
        }
        if (queryDto.classification) {
            query = query.eq('classification', queryDto.classification);
        }
        if (queryDto.minCapacity) {
            query = query.gte('capacity', queryDto.minCapacity);
        }
        if (queryDto.maxCapacity) {
            query = query.lte('capacity', queryDto.maxCapacity);
        }
        if (!queryDto.showDeleted) {
            query = query.eq('is_deleted', false);
        }
        query = query
            .order(sortBy, { ascending: sortOrder === 'asc' })
            .range(from, to);
        const { data, error, count } = await query;
        if (error) {
            this.logger.error(`Error fetching venues (admin): ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to fetch venues');
        }
        if (data.length > 0) {
            this.logger.debug(`Admin venue data structure: ${JSON.stringify(data[0])}`);
            this.logger.debug(`Cities structure: ${JSON.stringify(data[0].cities)}`);
        }
        const venues = data.map(venue => {
            let cityName = undefined;
            if (venue.cities) {
                if (Array.isArray(venue.cities) && venue.cities.length > 0) {
                    cityName = venue.cities[0].name;
                }
                else if (typeof venue.cities === 'object' && 'name' in venue.cities) {
                    cityName = venue.cities.name;
                }
            }
            return {
                id: venue.id,
                name: venue.name,
                description: venue.description,
                address: venue.address,
                city_id: venue.city_id,
                city_name: cityName,
                classification: venue.classification,
                capacity: venue.capacity,
                image_url: venue.image_url,
                features: venue.features,
                is_active: venue.is_active,
                is_deleted: venue.is_deleted,
                deleted_at: venue.deleted_at,
                created_at: venue.created_at,
                updated_at: venue.updated_at,
            };
        });
        const totalCount = count || 0;
        const totalPages = Math.ceil(totalCount / pageSize);
        return {
            data: venues,
            totalCount,
            page,
            pageSize,
            totalPages,
        };
    }
    async findOneAdmin(id) {
        this.logger.log(`Finding venue (admin) with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(`
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `)
            .eq('id', id)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Venue with ID ${id} not found`);
            }
            this.logger.error(`Error fetching venue (admin) with ID ${id}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to fetch venue');
        }
        this.logger.debug(`Venue detail data structure: ${JSON.stringify(data)}`);
        if (data.cities) {
            this.logger.debug(`Cities structure: ${JSON.stringify(data.cities)}`);
        }
        let cityName = undefined;
        if (data.cities) {
            if (Array.isArray(data.cities) && data.cities.length > 0) {
                cityName = data.cities[0].name;
            }
            else if (typeof data.cities === 'object' && 'name' in data.cities) {
                cityName = data.cities.name;
            }
        }
        return {
            id: data.id,
            name: data.name,
            description: data.description,
            address: data.address,
            city_id: data.city_id,
            city_name: cityName,
            classification: data.classification,
            capacity: data.capacity,
            image_url: data.image_url,
            features: data.features,
            is_active: data.is_active,
            is_deleted: data.is_deleted,
            deleted_at: data.deleted_at,
            created_at: data.created_at,
            updated_at: data.updated_at,
        };
    }
    async create(createDto) {
        this.logger.log(`Creating venue: ${JSON.stringify(createDto)}`);
        const supabase = this.supabaseService.getClient();
        const venueData = {
            name: createDto.name,
            description: createDto.description || null,
            address: createDto.address || null,
            city_id: createDto.city_id || null,
            classification: createDto.classification || null,
            capacity: createDto.capacity || null,
            image_url: createDto.image_url || null,
            features: createDto.features || null,
            is_active: createDto.is_active !== undefined ? createDto.is_active : true,
            is_deleted: false,
        };
        const { data, error } = await supabase
            .from(this.tableName)
            .insert([venueData])
            .select(`
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `)
            .single();
        if (error) {
            this.logger.error(`Error creating venue: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to create venue');
        }
        if (data.cities) {
            this.logger.debug(`Cities structure for create: ${JSON.stringify(data.cities)}`);
        }
        let cityName = undefined;
        if (data.cities) {
            if (Array.isArray(data.cities) && data.cities.length > 0) {
                cityName = data.cities[0].name;
            }
            else if (typeof data.cities === 'object' && 'name' in data.cities) {
                cityName = data.cities.name;
            }
        }
        return {
            id: data.id,
            name: data.name,
            description: data.description,
            address: data.address,
            city_id: data.city_id,
            city_name: cityName,
            classification: data.classification,
            capacity: data.capacity,
            image_url: data.image_url,
            features: data.features,
            is_active: data.is_active,
            is_deleted: data.is_deleted,
            deleted_at: data.deleted_at,
            created_at: data.created_at,
            updated_at: data.updated_at,
        };
    }
    async update(id, updateDto) {
        this.logger.log(`Updating venue with ID ${id}: ${JSON.stringify(updateDto)}`);
        const supabase = this.supabaseService.getClient();
        const { data: existingVenue, error: checkError } = await supabase
            .from(this.tableName)
            .select('id')
            .eq('id', id)
            .single();
        if (checkError) {
            if (checkError.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Venue with ID ${id} not found`);
            }
            this.logger.error(`Error checking venue existence: ${checkError.message}`);
            throw new common_1.InternalServerErrorException('Failed to update venue');
        }
        const { data, error } = await supabase
            .from(this.tableName)
            .update({
            name: updateDto.name,
            description: updateDto.description,
            address: updateDto.address,
            city_id: updateDto.city_id,
            classification: updateDto.classification,
            capacity: updateDto.capacity,
            image_url: updateDto.image_url,
            features: updateDto.features,
            is_active: updateDto.is_active,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select(`
        id,
        name,
        description,
        address,
        city_id,
        classification,
        capacity,
        image_url,
        features,
        is_active,
        is_deleted,
        deleted_at,
        created_at,
        updated_at,
        cities (name)
        `)
            .single();
        if (error) {
            this.logger.error(`Error updating venue: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to update venue');
        }
        if (data.cities) {
            this.logger.debug(`Cities structure for update: ${JSON.stringify(data.cities)}`);
        }
        let cityName = undefined;
        if (data.cities) {
            if (Array.isArray(data.cities) && data.cities.length > 0) {
                cityName = data.cities[0].name;
            }
            else if (typeof data.cities === 'object' && 'name' in data.cities) {
                cityName = data.cities.name;
            }
        }
        return {
            id: data.id,
            name: data.name,
            description: data.description,
            address: data.address,
            city_id: data.city_id,
            city_name: cityName,
            classification: data.classification,
            capacity: data.capacity,
            image_url: data.image_url,
            features: data.features,
            is_active: data.is_active,
            is_deleted: data.is_deleted,
            deleted_at: data.deleted_at,
            created_at: data.created_at,
            updated_at: data.updated_at,
        };
    }
    async remove(id) {
        this.logger.log(`Soft deleting venue with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data: existingVenue, error: checkError } = await supabase
            .from(this.tableName)
            .select('id, is_deleted')
            .eq('id', id)
            .single();
        if (checkError) {
            if (checkError.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Venue with ID ${id} not found`);
            }
            this.logger.error(`Error checking venue existence: ${checkError.message}`);
            throw new common_1.InternalServerErrorException('Failed to delete venue');
        }
        if (existingVenue.is_deleted) {
            this.logger.warn(`Venue with ID ${id} is already deleted`);
            return;
        }
        const { error } = await supabase
            .from(this.tableName)
            .update({
            is_deleted: true,
            deleted_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        })
            .eq('id', id);
        if (error) {
            this.logger.error(`Error deleting venue: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to delete venue');
        }
    }
    async restore(id) {
        this.logger.log(`Restoring venue with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data: existingVenue, error: checkError } = await supabase
            .from(this.tableName)
            .select('id, is_deleted')
            .eq('id', id)
            .single();
        if (checkError) {
            if (checkError.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Venue with ID ${id} not found`);
            }
            this.logger.error(`Error checking venue existence: ${checkError.message}`);
            throw new common_1.InternalServerErrorException('Failed to restore venue');
        }
        if (!existingVenue.is_deleted) {
            this.logger.warn(`Venue with ID ${id} is not deleted`);
            return;
        }
        const { error } = await supabase
            .from(this.tableName)
            .update({
            is_deleted: false,
            deleted_at: null,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id);
        if (error) {
            this.logger.error(`Error restoring venue: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to restore venue');
        }
    }
};
exports.VenuesService = VenuesService;
exports.VenuesService = VenuesService = VenuesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], VenuesService);
//# sourceMappingURL=venues.service.js.map