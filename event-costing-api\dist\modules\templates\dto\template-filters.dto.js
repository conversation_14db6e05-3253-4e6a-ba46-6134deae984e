"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateFiltersDto = exports.TemplateVisibility = exports.TemplateStatus = exports.TemplateSortBy = exports.SortOrder = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var SortOrder;
(function (SortOrder) {
    SortOrder["ASC"] = "asc";
    SortOrder["DESC"] = "desc";
})(SortOrder || (exports.SortOrder = SortOrder = {}));
var TemplateSortBy;
(function (TemplateSortBy) {
    TemplateSortBy["NAME"] = "name";
    TemplateSortBy["CREATED_AT"] = "created_at";
    TemplateSortBy["UPDATED_AT"] = "updated_at";
    TemplateSortBy["EVENT_TYPE"] = "event_type";
    TemplateSortBy["ATTENDEES"] = "attendees";
    TemplateSortBy["STATUS"] = "is_active";
    TemplateSortBy["PUBLIC"] = "is_public";
})(TemplateSortBy || (exports.TemplateSortBy = TemplateSortBy = {}));
var TemplateStatus;
(function (TemplateStatus) {
    TemplateStatus["ALL"] = "all";
    TemplateStatus["ACTIVE"] = "active";
    TemplateStatus["INACTIVE"] = "inactive";
})(TemplateStatus || (exports.TemplateStatus = TemplateStatus = {}));
var TemplateVisibility;
(function (TemplateVisibility) {
    TemplateVisibility["ALL"] = "all";
    TemplateVisibility["PUBLIC"] = "public";
    TemplateVisibility["PRIVATE"] = "private";
})(TemplateVisibility || (exports.TemplateVisibility = TemplateVisibility = {}));
class TemplateFiltersDto {
    search;
    eventType;
    categoryId;
    cityId;
    venueIds;
    minAttendees;
    maxAttendees;
    status;
    visibility;
    createdBy;
    myTemplatesOnly;
    hasPackagesFromCategory;
    hasCalculations;
    createdAfter;
    createdBefore;
    page;
    pageSize;
    sortBy;
    sortOrder;
    includePackages;
    includeCalculations;
    includeStatistics;
    tags;
    excludeId;
}
exports.TemplateFiltersDto = TemplateFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search term for template name or description',
        example: 'corporate event',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by event type',
        example: 'corporate',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "eventType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by category ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by city ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "cityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by venue IDs',
        type: [String],
        example: ['venue-1', 'venue-2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], TemplateFiltersDto.prototype, "venueIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum attendees filter',
        example: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TemplateFiltersDto.prototype, "minAttendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum attendees filter',
        example: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TemplateFiltersDto.prototype, "maxAttendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by template status',
        enum: TemplateStatus,
        example: TemplateStatus.ACTIVE,
        default: TemplateStatus.ALL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(TemplateStatus),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by template visibility',
        enum: TemplateVisibility,
        example: TemplateVisibility.PUBLIC,
        default: TemplateVisibility.ALL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(TemplateVisibility),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "visibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by template creator (user ID)',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only templates created by current user',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TemplateFiltersDto.prototype, "myTemplatesOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include templates with packages from specific category',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "hasPackagesFromCategory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only templates with calculations',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TemplateFiltersDto.prototype, "hasCalculations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by date range - start date',
        type: String,
        format: 'date',
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "createdAfter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by date range - end date',
        type: String,
        format: 'date',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "createdBefore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Page number for pagination',
        example: 1,
        default: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TemplateFiltersDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of items per page',
        example: 10,
        default: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], TemplateFiltersDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort by field',
        enum: TemplateSortBy,
        example: TemplateSortBy.NAME,
        default: TemplateSortBy.CREATED_AT,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(TemplateSortBy),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        enum: SortOrder,
        example: SortOrder.DESC,
        default: SortOrder.DESC,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(SortOrder),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include template packages in response',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TemplateFiltersDto.prototype, "includePackages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include template calculations in response',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TemplateFiltersDto.prototype, "includeCalculations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include template statistics in response',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TemplateFiltersDto.prototype, "includeStatistics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by tags',
        type: [String],
        example: ['premium', 'outdoor'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], TemplateFiltersDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Exclude template with specific ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TemplateFiltersDto.prototype, "excludeId", void 0);
//# sourceMappingURL=template-filters.dto.js.map