"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageQueryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageQueryService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../../core/supabase/supabase.service");
let PackageQueryService = PackageQueryService_1 = class PackageQueryService {
    supabaseService;
    logger = new common_1.Logger(PackageQueryService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAllPackages(queryDto) {
        this.logger.log(`Finding all packages with query: ${JSON.stringify(queryDto)}`);
        const supabase = this.supabaseService.getClient();
        const { name, categoryId, isDeleted, sortBy, sortOrder } = queryDto;
        const queryBuilder = supabase
            .from('packages')
            .select('*', { count: 'exact' });
        const limit = queryDto.limit ?? 20;
        const offset = queryDto.offset ?? 0;
        queryBuilder.eq('is_deleted', isDeleted === 'true');
        if (name) {
            queryBuilder.ilike('name', `%${name}%`);
        }
        if (categoryId) {
            queryBuilder.eq('category_id', categoryId);
        }
        const sortColumn = sortBy || 'name';
        const sortDirection = sortOrder || 'asc';
        queryBuilder.order(sortColumn, { ascending: sortDirection === 'asc' });
        queryBuilder.range(offset, offset + limit - 1);
        this.logger.log(`Executing query for findAll with limit: ${limit}, offset: ${offset}, sort: ${sortColumn} ${sortDirection}`);
        const { data, error, count } = await queryBuilder;
        if (error) {
            this.logger.error(`Error finding packages: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve packages.');
        }
        return {
            data: data || [],
            count: count || 0,
            limit: limit,
            offset: offset,
        };
    }
    async findPackageById(id) {
        this.logger.log(`Fetching package with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('packages')
            .select('*')
            .eq('id', id)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error fetching package with ID ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to fetch package: ${error.message}`);
        }
        if (!data) {
            this.logger.warn(`Package with ID ${id} not found.`);
            throw new common_1.NotFoundException(`Package with ID ${id} not found.`);
        }
        return data;
    }
    getPackageIds(packages) {
        return packages?.map(pkg => pkg.id) || [];
    }
    async fetchCategoriesMap(categoryIds) {
        if (categoryIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: categoriesData, error: categoriesError } = await supabase
            .from('categories')
            .select('id, name')
            .in('id', categoryIds);
        if (categoriesError) {
            this.logger.error(`Error fetching categories: ${categoriesError.message}`);
            return new Map();
        }
        return new Map(categoriesData?.map(category => [category.id, category.name]) || []);
    }
    async fetchDivisionsMap(divisionIds) {
        if (divisionIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: divisionsData, error: divisionsError } = await supabase
            .from('divisions')
            .select('id, name')
            .in('id', divisionIds);
        if (divisionsError) {
            this.logger.error(`Error fetching divisions: ${divisionsError.message}`);
            return new Map();
        }
        return new Map(divisionsData?.map(division => [division.id, division.name]) || []);
    }
    async fetchPackagePricesMap(packageIds) {
        if (packageIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: pricesData, error: pricesError } = await supabase
            .from('package_prices')
            .select('package_id, price, unit_base_cost, currency_id')
            .in('package_id', packageIds);
        if (pricesError) {
            this.logger.error(`Error fetching package prices: ${pricesError.message}`);
            return new Map();
        }
        if (!pricesData || pricesData.length === 0) {
            return new Map();
        }
        const currencyIds = [
            ...new Set(pricesData
                .filter(price => price.currency_id)
                .map(price => price.currency_id)),
        ];
        const currencyMap = await this.fetchCurrenciesMap(currencyIds);
        const priceMap = new Map();
        pricesData.forEach(price => {
            priceMap.set(price.package_id, {
                price: price.price?.toString() || null,
                unitBaseCost: price.unit_base_cost?.toString() || null,
                currencySymbol: price.currency_id
                    ? currencyMap.get(price.currency_id) || 'Rp'
                    : 'Rp',
                hasPricing: true,
            });
        });
        return priceMap;
    }
    async fetchCurrenciesMap(currencyIds) {
        if (currencyIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: currenciesData, error: currenciesError } = await supabase
            .from('currencies')
            .select('id, code')
            .in('id', currencyIds);
        if (currenciesError) {
            this.logger.error(`Error fetching currencies: ${currenciesError.message}`);
            return new Map();
        }
        return new Map(currenciesData?.map(currency => [currency.id, currency.code]) || []);
    }
    async fetchPackageCitiesMap(packageIds) {
        if (packageIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: packageCitiesData, error: packageCitiesError } = await supabase
            .from('package_cities')
            .select('package_id, city_id')
            .in('package_id', packageIds);
        if (packageCitiesError) {
            this.logger.error(`Error fetching package cities: ${packageCitiesError.message}`);
            return new Map();
        }
        if (!packageCitiesData || packageCitiesData.length === 0) {
            return new Map();
        }
        const cityIds = [
            ...new Set(packageCitiesData.filter(pc => pc.city_id).map(pc => pc.city_id)),
        ];
        const cityMap = await this.fetchCitiesMap(cityIds);
        const packageCityMap = new Map();
        packageCitiesData.forEach(pc => {
            if (!packageCityMap.has(pc.package_id)) {
                packageCityMap.set(pc.package_id, []);
            }
            const cityName = cityMap.get(pc.city_id);
            if (cityName) {
                packageCityMap.get(pc.package_id).push(cityName);
            }
        });
        return packageCityMap;
    }
    async fetchCitiesMap(cityIds) {
        if (cityIds.length === 0)
            return new Map();
        const supabase = this.supabaseService.getClient();
        const { data: citiesData, error: citiesError } = await supabase
            .from('cities')
            .select('id, name')
            .in('id', cityIds);
        if (citiesError) {
            this.logger.error(`Error fetching cities: ${citiesError.message}`);
            return new Map();
        }
        return new Map(citiesData?.map(city => [city.id, city.name]) || []);
    }
};
exports.PackageQueryService = PackageQueryService;
exports.PackageQueryService = PackageQueryService = PackageQueryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageQueryService);
//# sourceMappingURL=package-query.service.js.map