import { SettingsService } from './settings.service';
import { SettingDto } from './dto/setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
export declare class AdminSettingsController {
    private readonly settingsService;
    private readonly logger;
    constructor(settingsService: SettingsService);
    getSetting(key: string): Promise<SettingDto>;
    updateSetting(key: string, updateDto: UpdateSettingDto): Promise<SettingDto>;
}
