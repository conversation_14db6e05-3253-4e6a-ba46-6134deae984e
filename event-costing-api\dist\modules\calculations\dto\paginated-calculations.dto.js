"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationSummaryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const calculation_status_enum_1 = require("../enums/calculation-status.enum");
class CalculationSummaryDto {
    id;
    name;
    status;
    event_start_date;
    event_end_date;
    total;
    currency_id;
    created_at;
    updated_at;
    client_id;
    event_id;
}
exports.CalculationSummaryDto = CalculationSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'uuid' }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: calculation_status_enum_1.CalculationStatus }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'date', nullable: true }),
    __metadata("design:type", Object)
], CalculationSummaryDto.prototype, "event_start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'date', nullable: true }),
    __metadata("design:type", Object)
], CalculationSummaryDto.prototype, "event_end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        description: 'Pre-calculated total from calculation_history',
    }),
    __metadata("design:type", Number)
], CalculationSummaryDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'uuid' }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'date-time' }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'date-time' }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'uuid', nullable: true }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "client_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'uuid', nullable: true }),
    __metadata("design:type", String)
], CalculationSummaryDto.prototype, "event_id", void 0);
//# sourceMappingURL=paginated-calculations.dto.js.map