import { PackageCatalogService } from '../services/package-catalog.service';
import { PackageCatalogDto } from '../dto/package-catalog.dto';
import { PackageFiltersDto } from '../dto/package-filters.dto';
import { User } from '@supabase/supabase-js';
export declare class PackageCatalogController {
    private readonly packageCatalogService;
    private readonly logger;
    constructor(packageCatalogService: PackageCatalogService);
    getCatalogData(filters: PackageFiltersDto, user: User): Promise<PackageCatalogDto>;
    getAdvancedCatalogData(filters: PackageFiltersDto & {
        includeOptions?: boolean;
        includeDependencies?: boolean;
        includeAvailability?: boolean;
    }, user: User): Promise<PackageCatalogDto>;
    getCatalogSummary(user: User): Promise<any>;
}
