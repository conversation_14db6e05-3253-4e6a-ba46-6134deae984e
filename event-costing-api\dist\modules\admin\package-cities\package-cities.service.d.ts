import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageCityDto } from './dto/package-city.dto';
export declare class PackageCitiesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly PACKAGE_CITIES_TABLE;
    private readonly CITIES_TABLE;
    private readonly PACKAGES_TABLE;
    constructor(supabaseService: SupabaseService);
    addCityToPackage(packageId: string, cityId: string): Promise<{
        id: string;
    }>;
    listCitiesForPackage(packageId: string): Promise<PackageCityDto[]>;
    removeCityFromPackage(packageId: string, cityId: string): Promise<void>;
}
