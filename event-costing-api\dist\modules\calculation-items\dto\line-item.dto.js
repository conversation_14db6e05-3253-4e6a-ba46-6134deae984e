"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LineItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class LineItemDto {
    id;
    calculation_id;
    package_id;
    item_name;
    item_name_snapshot;
    description;
    notes;
    quantity;
    item_quantity;
    item_quantity_basis;
    duration_days;
    quantity_basis;
    unit_price;
    unit_base_price;
    calculated_line_total;
    category_id;
    is_custom;
    options;
    created_at;
    updated_at;
}
exports.LineItemDto = LineItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Line item ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], LineItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], LineItemDto.prototype, "calculation_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Package ID (null for custom items)',
        type: String,
        format: 'uuid',
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Item name',
        type: String,
    }),
    __metadata("design:type", String)
], LineItemDto.prototype, "item_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Item name snapshot (for package items)',
        type: String,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "item_name_snapshot", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Description (for custom items)',
        type: String,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notes (for package items)',
        type: String,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantity',
        type: Number,
    }),
    __metadata("design:type", Number)
], LineItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Item quantity (for package items)',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "item_quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Item quantity basis (number of days/units)',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "item_quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Duration in days (for package items)',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "duration_days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Quantity basis type (PER_EVENT, PER_DAY, etc.)',
        type: String,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unit price (for custom items)',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "unit_price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unit base price (for package items)',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "unit_base_price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Calculated line total',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "calculated_line_total", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category ID',
        type: String,
        format: 'uuid',
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether this is a custom item',
        type: Boolean,
    }),
    __metadata("design:type", Boolean)
], LineItemDto.prototype, "is_custom", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Selected options',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                option_id: { type: 'string', format: 'uuid' },
                option_name: { type: 'string', nullable: true },
            },
        },
        nullable: true,
    }),
    __metadata("design:type", Object)
], LineItemDto.prototype, "options", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], LineItemDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], LineItemDto.prototype, "updated_at", void 0);
//# sourceMappingURL=line-item.dto.js.map