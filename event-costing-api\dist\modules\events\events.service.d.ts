import { SupabaseService } from '../../core/supabase/supabase.service';
import { EventDto } from './dto/event.dto';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { EventStatus } from './dto/event-status.enum';
export declare class EventsService {
    private readonly supabaseService;
    private readonly logger;
    private readonly TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    private handleSupabaseError;
    create(createEventDto: CreateEventDto): Promise<EventDto>;
    findAll(search?: string, status?: EventStatus, clientId?: string, contactId?: string): Promise<EventDto[]>;
    findOne(id: string): Promise<EventDto>;
    update(id: string, updateEventDto: UpdateEventDto): Promise<EventDto>;
    remove(id: string): Promise<void>;
}
