import { PackageOptionsService, PackageOptionListQueryDto } from './package-options.service';
import { CreatePackageOptionDto } from './dto/create-package-option.dto';
import { UpdatePackageOptionDto } from './dto/update-package-option.dto';
import { PackageOptionDto } from './dto/package-option.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
export declare class PackageOptionsController {
    private readonly packageOptionsService;
    constructor(packageOptionsService: PackageOptionsService);
    create(packageId: string, createDto: CreatePackageOptionDto): Promise<PackageOptionDto>;
    findAll(packageId: string, queryDto: PackageOptionListQueryDto): Promise<PaginatedResponseDto<PackageOptionDto>>;
    findOne(packageId: string, id: string): Promise<PackageOptionDto>;
    update(packageId: string, id: string, updateDto: UpdatePackageOptionDto): Promise<PackageOptionDto>;
    remove(packageId: string, id: string): Promise<void>;
}
