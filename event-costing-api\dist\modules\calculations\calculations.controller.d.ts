import { CalculationsService } from './calculations.service';
import { CalculationTemplateService } from './calculation-template.service';
import { CalculationLogicService } from './calculation-logic.service';
import { CreateCalculationDto } from './dto/create-calculation.dto';
import { CreateCalculationFromTemplateDto } from './dto/create-calculation-from-template.dto';
import { ListCalculationsDto } from './dto/list-calculations.dto';
import { CalculationDetailDto } from './dto/calculation-detail.dto';
import { UpdateCalculationDto } from './dto/update-calculation.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { CalculationSummaryDto } from './dto/paginated-calculations.dto';
import { User } from '@supabase/supabase-js';
import { CalculationTotalsDto } from './dto/calculation-totals.dto';
import { UpdateCalculationStatusDto } from './dto/update-calculation-status.dto';
import { CalculationIdResponse } from './dto/calculation-id-response.dto';
import { CalculationCompleteDataDto } from './dto/calculation-complete-data.dto';
import { CalculationCompleteDataService } from './services/calculation-complete-data.service';
export declare class CalculationsController {
    private readonly calculationsService;
    private readonly calculationTemplateService;
    private readonly calculationLogicService;
    private readonly calculationCompleteDataService;
    private readonly logger;
    constructor(calculationsService: CalculationsService, calculationTemplateService: CalculationTemplateService, calculationLogicService: CalculationLogicService, calculationCompleteDataService: CalculationCompleteDataService);
    createCalculation(createCalculationDto: CreateCalculationDto, user: User): Promise<CalculationIdResponse>;
    createFromTemplate(templateId: string, customizationDto: CreateCalculationFromTemplateDto, user: User): Promise<CalculationIdResponse>;
    findUserCalculations(queryDto: ListCalculationsDto, user: User): Promise<PaginatedResponseDto<CalculationSummaryDto>>;
    findCalculationById(id: string, user: User): Promise<CalculationDetailDto>;
    getCompleteCalculationData(id: string, user: User): Promise<CalculationCompleteDataDto>;
    getTotals(id: string, user: User): Promise<CalculationTotalsDto>;
    getCalculationSummary(id: string, user: User): Promise<any>;
    updateCalculation(id: string, updateCalculationDto: UpdateCalculationDto, user: User): Promise<CalculationDetailDto>;
    updateStatus(id: string, updateStatusDto: UpdateCalculationStatusDto, user: User): Promise<void>;
    remove(id: string, user: User): Promise<void>;
    recalculateTotals(id: string, user: User): Promise<void>;
}
