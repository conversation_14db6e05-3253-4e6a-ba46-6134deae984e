"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomItemsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomItemsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../modules/auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../../modules/auth/decorators/get-current-user.decorator");
const custom_items_service_1 = require("../services/custom-items.service");
const add_custom_line_item_dto_1 = require("../dto/add-custom-line-item.dto");
const update_line_item_dto_1 = require("../dto/update-line-item.dto");
const custom_item_dto_1 = require("../dto/custom-item.dto");
const item_id_response_dto_1 = require("../dto/item-id-response.dto");
let CustomItemsController = CustomItemsController_1 = class CustomItemsController {
    customItemsService;
    logger = new common_1.Logger(CustomItemsController_1.name);
    constructor(customItemsService) {
        this.customItemsService = customItemsService;
    }
    async getCustomItems(calcId) {
        this.logger.log(`Fetching custom items for calculation ${calcId}`);
        return this.customItemsService.getCustomItems(calcId);
    }
    async getCustomItemById(calcId, itemId) {
        this.logger.log(`Fetching custom item ${itemId} for calculation ${calcId}`);
        return this.customItemsService.getCustomItemById(calcId, itemId);
    }
    async addCustomItem(calcId, addDto, user) {
        this.logger.log(`User ${user.email} adding custom item '${addDto.itemName}' to calc ${calcId}`);
        return this.customItemsService.addCustomItem(calcId, addDto, user);
    }
    async updateCustomItem(calcId, itemId, updateDto, user) {
        this.logger.log(`User ${user.email} updating custom item ${itemId} in calc ${calcId}`);
        return this.customItemsService.updateCustomItem(calcId, itemId, updateDto, user);
    }
    async deleteCustomItem(calcId, itemId, user) {
        this.logger.log(`User ${user.email} deleting custom item ${itemId} from calc ${calcId}`);
        await this.customItemsService.deleteCustomItem(calcId, itemId, user);
    }
};
exports.CustomItemsController = CustomItemsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all custom items for a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Custom items retrieved', type: [custom_item_dto_1.CustomItemDto] }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomItemsController.prototype, "getCustomItems", null);
__decorate([
    (0, common_1.Get)(':itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific custom item' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Custom item retrieved', type: custom_item_dto_1.CustomItemDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or custom item not found.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], CustomItemsController.prototype, "getCustomItemById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Add a new custom item to a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Custom item added', type: item_id_response_dto_1.ItemIdResponse }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_custom_line_item_dto_1.AddCustomLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CustomItemsController.prototype, "addCustomItem", null);
__decorate([
    (0, common_1.Put)(':itemId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a custom item' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Custom item updated', type: custom_item_dto_1.CustomItemDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or custom item not found.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_line_item_dto_1.UpdateLineItemDto, Object]),
    __metadata("design:returntype", Promise)
], CustomItemsController.prototype, "updateCustomItem", null);
__decorate([
    (0, common_1.Delete)(':itemId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a custom item' }),
    (0, swagger_1.ApiParam)({ name: 'calcId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiParam)({ name: 'itemId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Custom item deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation or custom item not found.',
    }),
    __param(0, (0, common_1.Param)('calcId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('itemId', common_1.ParseUUIDPipe)),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CustomItemsController.prototype, "deleteCustomItem", null);
exports.CustomItemsController = CustomItemsController = CustomItemsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Custom Items'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('calculations/:calcId/custom-items'),
    __metadata("design:paramtypes", [custom_items_service_1.CustomItemsService])
], CustomItemsController);
//# sourceMappingURL=custom-items.controller.js.map