import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackageDto } from '../dto/create-package.dto';
import { UpdatePackageDto } from '../dto/update-package.dto';
export declare class PackageRelationsService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    createCityAssociations(packageId: string, cityIds: string[]): Promise<void>;
    createVenueAssociations(packageId: string, enableVenues: boolean, venueIds?: string[]): Promise<void>;
    createPriceInformation(packageId: string, createPackageDto: CreatePackageDto): Promise<void>;
    updateCityAssociations(packageId: string, cityIds?: string[]): Promise<void>;
    updateVenueAssociations(packageId: string, enableVenues?: boolean, venueIds?: string[]): Promise<void>;
    private handleVenueUpdates;
    updatePriceInformation(packageId: string, updatePackageDto: UpdatePackageDto): Promise<void>;
    private cleanupInvalidVenueAssociations;
}
