import { CurrenciesService } from './currencies.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { CurrencyDto } from './dto/currency.dto';
export declare class AdminCurrenciesController {
    private readonly currenciesService;
    private readonly logger;
    constructor(currenciesService: CurrenciesService);
    createCurrency(createDto: CreateCurrencyDto): Promise<CurrencyDto>;
    findOne(id: string): Promise<CurrencyDto>;
    updateCurrency(id: string, updateDto: UpdateCurrencyDto): Promise<CurrencyDto>;
    deleteCurrency(id: string): Promise<void>;
}
