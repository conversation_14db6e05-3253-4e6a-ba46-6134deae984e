"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CurrenciesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrenciesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let CurrenciesService = CurrenciesService_1 = class CurrenciesService {
    supabaseService;
    logger = new common_1.Logger(CurrenciesService_1.name);
    tableName = 'currencies';
    selectFields = 'id, code, description';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(this.selectFields)
            .order('code', { ascending: true });
        if (error) {
            this.logger.error(`Failed to fetch currencies: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve currencies.');
        }
        return data || [];
    }
    async findOneById(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(this.selectFields)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Currency with ID ${id} not found.`);
        }
        return data;
    }
    async createCurrency(createDto) {
        this.logger.debug(`Creating currency: ${createDto.code} - ${createDto.description}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .insert({
            code: createDto.code.toUpperCase(),
            description: createDto.description,
        })
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === '23505') {
                this.logger.warn(`Attempted to create duplicate currency code: ${createDto.code}`);
                throw new common_1.ConflictException(`A currency with the code "${createDto.code}" already exists.`);
            }
            this.logger.error(`Failed to create currency: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not create currency.');
        }
        if (!data) {
            this.logger.error('Currency insert succeeded but returned no data.');
            throw new common_1.InternalServerErrorException('Failed to retrieve newly created currency.');
        }
        this.logger.log(`Successfully created currency ID: ${data.id}, Code: ${data.code}`);
        return data;
    }
    async updateCurrency(id, updateDto) {
        this.logger.debug(`Updating currency ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {};
        if (updateDto.description !== undefined) {
            updateData.description = updateDto.description;
        }
        if (Object.keys(updateData).length === 0) {
            return this.findOneById(id);
        }
        const { data, error } = await supabase
            .from(this.tableName)
            .update(updateData)
            .eq('id', id)
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                this.logger.warn(`Currency not found for update: ID ${id}`);
                throw new common_1.NotFoundException(`Currency with ID ${id} not found.`);
            }
            this.logger.error(`Failed to update currency ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update currency.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Currency with ID ${id} not found.`);
        }
        this.logger.log(`Successfully updated currency ID: ${id}`);
        return data;
    }
    async deleteCurrency(id) {
        this.logger.debug(`Deleting currency ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.tableName)
            .delete()
            .eq('id', id);
        if (error) {
            this.logger.error(`Failed to delete currency ${id}: ${error.message}`, error.stack);
            if (error.code === '23503') {
                this.logger.warn(`Attempted to delete currency ${id} which is still referenced.`);
                throw new common_1.ConflictException(`Cannot delete currency because it is referenced by other records (e.g., calculations, package prices).`);
            }
            throw new common_1.InternalServerErrorException('Could not delete currency.');
        }
        if (count === 0) {
            this.logger.warn(`Currency not found for deletion: ID ${id}`);
            throw new common_1.NotFoundException(`Currency with ID ${id} not found.`);
        }
        this.logger.log(`Successfully deleted currency ID: ${id}`);
    }
};
exports.CurrenciesService = CurrenciesService;
exports.CurrenciesService = CurrenciesService = CurrenciesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CurrenciesService);
//# sourceMappingURL=currencies.service.js.map