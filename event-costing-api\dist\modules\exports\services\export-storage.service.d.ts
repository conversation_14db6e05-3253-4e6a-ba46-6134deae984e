import { SupabaseService } from '../../../core/supabase/supabase.service';
export declare class ExportStorageService {
    private readonly supabaseService;
    private readonly logger;
    private readonly BUCKET_NAME;
    constructor(supabaseService: SupabaseService);
    uploadExportFile(userId: string, fileName: string, fileBuffer: Buffer, mimeType: string): Promise<string>;
    getSignedUrl(storagePath: string | null | undefined): Promise<string | null>;
}
