"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCalculationDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const calculation_status_enum_1 = require("../enums/calculation-status.enum");
const swagger_1 = require("@nestjs/swagger");
class TaxDetailItemDto {
    name;
    rate;
    type;
    basis;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the tax', example: 'PPN' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TaxDetailItemDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tax rate (percentage)',
        example: 11,
        type: 'number',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TaxDetailItemDto.prototype, "rate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of tax',
        example: 'percentage',
        enum: ['percentage', 'fixed'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['percentage', 'fixed']),
    __metadata("design:type", String)
], TaxDetailItemDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Basis for percentage calculation',
        example: 'subtotal',
        enum: ['subtotal', 'total'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['subtotal', 'total']),
    __metadata("design:type", String)
], TaxDetailItemDto.prototype, "basis", void 0);
class DiscountDetailDto {
    name;
    amount;
    reason;
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name or description of the discount',
        example: 'Early Bird',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DiscountDetailDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Discount amount (fixed value)',
        example: 100.0,
        type: 'number',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DiscountDetailDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for the discount',
        example: 'Promotion',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DiscountDetailDto.prototype, "reason", void 0);
class UpdateCalculationDto {
    name;
    city_id;
    venue_ids;
    event_start_date;
    event_end_date;
    attendees;
    event_type_id;
    notes;
    version_notes;
    client_id;
    event_id;
    status;
    taxes;
    discount;
}
exports.UpdateCalculationDto = UpdateCalculationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        required: false,
        nullable: true,
        example: 'Calculation Name',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateCalculationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City ID',
        example: '029f586d-70da-4637-b58a-176470d3e528',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs',
        type: [String],
        format: 'uuid',
        example: ['a1b2c3d4-e5f6-7890-1234-567890abcdef'],
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Start date and time of the event (ISO 8601 format)',
        example: '2025-12-31T10:00:00.000Z',
        format: 'date-time',
        required: false,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)({ strict: true, strictSeparator: true }),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "event_start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: End date and time of the event (ISO 8601 format)',
        example: '2026-01-01T10:00:00.000Z',
        format: 'date-time',
        required: false,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)({ strict: true, strictSeparator: true }),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "event_end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Number of attendees expected',
        example: 150,
        minimum: 0,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "attendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Event type ID (UUID reference to event_types table)',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "event_type_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: General notes about the calculation',
        example: 'Final guest list confirmed. Pending catering finalization.',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Notes specific to this version/update of the calculation',
        example: 'Increased attendee count from 120 to 150 based on client update.',
        maxLength: 2000,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2000),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "version_notes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: ID of the client associated with this calculation',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "client_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: ID of the event this calculation belongs to',
        example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "event_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: The current status of the calculation',
        enum: calculation_status_enum_1.CalculationStatus,
        example: calculation_status_enum_1.CalculationStatus.COMPLETED,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(calculation_status_enum_1.CalculationStatus),
    __metadata("design:type", String)
], UpdateCalculationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Array of tax objects to apply. Replaces the existing taxes.',
        type: [TaxDetailItemDto],
        example: [{ name: 'PPN', rate: 12, type: 'percentage', basis: 'subtotal' }],
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TaxDetailItemDto),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "taxes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional: Discount object to apply. Replaces the existing discount.',
        type: DiscountDetailDto,
        example: { name: 'Early Bird', amount: 100.0, reason: 'Promotion' },
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => DiscountDetailDto),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateCalculationDto.prototype, "discount", void 0);
//# sourceMappingURL=update-calculation.dto.js.map