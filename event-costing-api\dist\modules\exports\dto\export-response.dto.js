"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ExportResponseDto {
    exportId;
    status;
    message;
    createdAt;
}
exports.ExportResponseDto = ExportResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique ID for this export history record.',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], ExportResponseDto.prototype, "exportId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The initial status of the export process.',
        example: 'PENDING',
    }),
    __metadata("design:type", String)
], ExportResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A confirmation message.',
    }),
    __metadata("design:type", String)
], ExportResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the export process was initiated.',
    }),
    __metadata("design:type", Date)
], ExportResponseDto.prototype, "createdAt", void 0);
//# sourceMappingURL=export-response.dto.js.map