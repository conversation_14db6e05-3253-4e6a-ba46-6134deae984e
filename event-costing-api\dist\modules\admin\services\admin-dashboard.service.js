"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdminDashboardService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDashboardService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const categories_service_1 = require("../../categories/categories.service");
const cities_service_1 = require("../../cities/cities.service");
const divisions_service_1 = require("../../divisions/divisions.service");
let AdminDashboardService = AdminDashboardService_1 = class AdminDashboardService {
    supabaseService;
    categoriesService;
    citiesService;
    divisionsService;
    logger = new common_1.Logger(AdminDashboardService_1.name);
    constructor(supabaseService, categoriesService, citiesService, divisionsService) {
        this.supabaseService = supabaseService;
        this.categoriesService = categoriesService;
        this.citiesService = citiesService;
        this.divisionsService = divisionsService;
    }
    async getAdminDashboardData(filters = {}, user) {
        this.logger.log(`Fetching admin dashboard data for user: ${user.email}`);
        const startTime = Date.now();
        try {
            const [overviewResult, categoriesResult, citiesResult, divisionsResult, packagesResult, templatesResult, calculationsResult, usersResult, recentActivityResult,] = await Promise.allSettled([
                this.getSystemOverview(),
                this.getCategories(),
                this.getCities(),
                this.getDivisions(),
                this.getPackagesOverview(),
                this.getTemplatesOverview(),
                this.getCalculationsOverview(),
                this.getUsersOverview(),
                this.getRecentActivity(filters.activityDays || 7),
            ]);
            const overview = this.extractResult(overviewResult, 'overview', {
                totalPackages: 0,
                totalTemplates: 0,
                totalCalculations: 0,
                totalUsers: 0,
                totalCategories: 0,
                totalCities: 0,
                totalDivisions: 0,
            });
            const categories = this.extractResult(categoriesResult, 'categories', []);
            const cities = this.extractResult(citiesResult, 'cities', []);
            const divisions = this.extractResult(divisionsResult, 'divisions', []);
            const packages = this.extractResult(packagesResult, 'packages', {
                totalCount: 0,
                activeCount: 0,
                deletedCount: 0,
            });
            const templates = this.extractResult(templatesResult, 'templates', {
                totalCount: 0,
                activeCount: 0,
                publicCount: 0,
                privateCount: 0,
            });
            const calculations = this.extractResult(calculationsResult, 'calculations', {
                totalCount: 0,
                draftCount: 0,
                finalizedCount: 0,
            });
            const users = this.extractResult(usersResult, 'users', {
                totalCount: 0,
                activeCount: 0,
            });
            const recentActivity = this.extractResult(recentActivityResult, 'recentActivity', []);
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                overviewResult,
                categoriesResult,
                citiesResult,
                divisionsResult,
                packagesResult,
                templatesResult,
                calculationsResult,
                usersResult,
                recentActivityResult,
            ]);
            const result = {
                overview,
                categories,
                cities,
                divisions,
                packages,
                templates,
                calculations,
                users,
                recentActivity,
                filters: {
                    applied: filters,
                },
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                    dataPoints: {
                        categoriesCount: categories.length,
                        citiesCount: cities.length,
                        divisionsCount: divisions.length,
                        packagesCount: packages.totalCount || 0,
                        templatesCount: templates.totalCount || 0,
                        calculationsCount: calculations.totalCount || 0,
                        usersCount: users.totalCount || 0,
                        recentActivityCount: recentActivity.length,
                    },
                },
            };
            this.logger.log(`Successfully fetched admin dashboard data in ${loadTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to fetch admin dashboard data`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load admin dashboard data. Please try again.');
        }
    }
    async getSystemOverview() {
        const supabase = this.supabaseService.getClient();
        const [packagesCount, templatesCount, calculationsCount, usersCount, categoriesCount, citiesCount, divisionsCount,] = await Promise.allSettled([
            supabase.from('packages').select('id', { count: 'exact', head: true }),
            supabase.from('templates').select('id', { count: 'exact', head: true }),
            supabase
                .from('calculations')
                .select('id', { count: 'exact', head: true }),
            supabase.from('users').select('id', { count: 'exact', head: true }),
            supabase.from('categories').select('id', { count: 'exact', head: true }),
            supabase.from('cities').select('id', { count: 'exact', head: true }),
            supabase.from('divisions').select('id', { count: 'exact', head: true }),
        ]);
        return {
            totalPackages: this.extractCount(packagesCount),
            totalTemplates: this.extractCount(templatesCount),
            totalCalculations: this.extractCount(calculationsCount),
            totalUsers: this.extractCount(usersCount),
            totalCategories: this.extractCount(categoriesCount),
            totalCities: this.extractCount(citiesCount),
            totalDivisions: this.extractCount(divisionsCount),
        };
    }
    async getCategories() {
        return this.categoriesService.findAll();
    }
    async getCities() {
        return this.citiesService.findAll();
    }
    async getDivisions() {
        return this.divisionsService.findAll();
    }
    async getPackagesOverview() {
        const supabase = this.supabaseService.getClient();
        const [totalResult, activeResult, deletedResult] = await Promise.allSettled([
            supabase.from('packages').select('id', { count: 'exact', head: true }),
            supabase
                .from('packages')
                .select('id', { count: 'exact', head: true })
                .eq('is_deleted', false),
            supabase
                .from('packages')
                .select('id', { count: 'exact', head: true })
                .eq('is_deleted', true),
        ]);
        return {
            totalCount: this.extractCount(totalResult),
            activeCount: this.extractCount(activeResult),
            deletedCount: this.extractCount(deletedResult),
        };
    }
    async getTemplatesOverview() {
        const supabase = this.supabaseService.getClient();
        const [totalResult, activeResult, publicResult, privateResult] = await Promise.allSettled([
            supabase.from('templates').select('id', { count: 'exact', head: true }),
            supabase
                .from('templates')
                .select('id', { count: 'exact', head: true })
                .eq('is_active', true),
            supabase
                .from('templates')
                .select('id', { count: 'exact', head: true })
                .eq('is_public', true),
            supabase
                .from('templates')
                .select('id', { count: 'exact', head: true })
                .eq('is_public', false),
        ]);
        return {
            totalCount: this.extractCount(totalResult),
            activeCount: this.extractCount(activeResult),
            publicCount: this.extractCount(publicResult),
            privateCount: this.extractCount(privateResult),
        };
    }
    async getCalculationsOverview() {
        const supabase = this.supabaseService.getClient();
        const [totalResult, draftResult, finalizedResult] = await Promise.allSettled([
            supabase
                .from('calculations')
                .select('id', { count: 'exact', head: true }),
            supabase
                .from('calculations')
                .select('id', { count: 'exact', head: true })
                .eq('status', 'draft'),
            supabase
                .from('calculations')
                .select('id', { count: 'exact', head: true })
                .eq('status', 'finalized'),
        ]);
        return {
            totalCount: this.extractCount(totalResult),
            draftCount: this.extractCount(draftResult),
            finalizedCount: this.extractCount(finalizedResult),
        };
    }
    async getUsersOverview() {
        const supabase = this.supabaseService.getClient();
        const [totalResult, activeResult] = await Promise.allSettled([
            supabase.from('users').select('id', { count: 'exact', head: true }),
            supabase
                .from('users')
                .select('id', { count: 'exact', head: true })
                .eq('is_active', true),
        ]);
        return {
            totalCount: this.extractCount(totalResult),
            activeCount: this.extractCount(activeResult),
        };
    }
    async getRecentActivity(days = 7) {
        const supabase = this.supabaseService.getClient();
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const cutoffIso = cutoffDate.toISOString();
        const [recentPackages, recentTemplates, recentCalculations] = await Promise.allSettled([
            supabase
                .from('packages')
                .select('id, name, created_at, updated_at')
                .gte('created_at', cutoffIso)
                .order('created_at', { ascending: false })
                .limit(10),
            supabase
                .from('templates')
                .select('id, name, created_at, updated_at')
                .gte('created_at', cutoffIso)
                .order('created_at', { ascending: false })
                .limit(10),
            supabase
                .from('calculations')
                .select('id, name, created_at, updated_at')
                .gte('created_at', cutoffIso)
                .order('created_at', { ascending: false })
                .limit(10),
        ]);
        const activities = [];
        if (recentPackages.status === 'fulfilled' && recentPackages.value.data) {
            activities.push(...recentPackages.value.data.map(item => ({
                type: 'package',
                id: item.id,
                name: item.name,
                action: 'created',
                timestamp: item.created_at,
            })));
        }
        if (recentTemplates.status === 'fulfilled' && recentTemplates.value.data) {
            activities.push(...recentTemplates.value.data.map(item => ({
                type: 'template',
                id: item.id,
                name: item.name,
                action: 'created',
                timestamp: item.created_at,
            })));
        }
        if (recentCalculations.status === 'fulfilled' &&
            recentCalculations.value.data) {
            activities.push(...recentCalculations.value.data.map(item => ({
                type: 'calculation',
                id: item.id,
                name: item.name,
                action: 'created',
                timestamp: item.created_at,
            })));
        }
        return activities
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            .slice(0, 20);
    }
    extractCount(result) {
        if (result.status === 'fulfilled' && result.value.count !== null) {
            return result.value.count;
        }
        return 0;
    }
    extractResult(result, name, defaultValue) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        this.logger.warn(`Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`);
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        return [];
    }
    collectErrors(results) {
        return results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason?.message || 'Unknown error');
    }
};
exports.AdminDashboardService = AdminDashboardService;
exports.AdminDashboardService = AdminDashboardService = AdminDashboardService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        categories_service_1.CategoriesService,
        cities_service_1.CitiesService,
        divisions_service_1.DivisionsService])
], AdminDashboardService);
//# sourceMappingURL=admin-dashboard.service.js.map