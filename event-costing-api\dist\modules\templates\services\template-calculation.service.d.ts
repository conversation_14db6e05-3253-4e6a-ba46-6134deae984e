import { SupabaseService } from 'src/core/supabase/supabase.service';
import { TemplateCalculationResultDto, TemplateCalculationSummaryDto } from '../dto/template-calculation.dto';
export declare class TemplateCalculationService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    calculateTemplateTotal(templateId: string): Promise<TemplateCalculationResultDto>;
    getCalculationSummary(templateId: string): Promise<TemplateCalculationSummaryDto>;
}
