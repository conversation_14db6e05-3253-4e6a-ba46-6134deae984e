"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePackageOptionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePackageOptionDto {
    option_code;
    option_name;
    currency_id;
    price_adjustment;
    cost_adjustment;
    description;
    option_group;
    is_default_for_package;
    is_required;
}
exports.CreatePackageOptionDto = CreatePackageOptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique code for the option within the package.',
        example: 'OPT_VEGAN',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreatePackageOptionDto.prototype, "option_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Display name for the option.',
        example: 'Vegan Meal Option',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreatePackageOptionDto.prototype, "option_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency ID for the price/cost adjustments.',
        format: 'uuid',
        example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackageOptionDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Price adjustment when this option is selected.',
        type: Number,
        default: 0,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    __metadata("design:type", Number)
], CreatePackageOptionDto.prototype, "price_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cost adjustment when this option is selected.',
        type: Number,
        default: 0,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    __metadata("design:type", Number)
], CreatePackageOptionDto.prototype, "cost_adjustment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Optional description.', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePackageOptionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional group name for mutual exclusivity.',
        required: false,
        example: 'MEAL_CHOICE',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreatePackageOptionDto.prototype, "option_group", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is this option selected by default?',
        type: Boolean,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePackageOptionDto.prototype, "is_default_for_package", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is this option mandatory?',
        type: Boolean,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePackageOptionDto.prototype, "is_required", void 0);
//# sourceMappingURL=create-package-option.dto.js.map