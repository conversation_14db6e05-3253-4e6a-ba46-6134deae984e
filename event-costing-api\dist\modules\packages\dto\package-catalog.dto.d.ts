import { PackageVariationDto } from './package-variation.dto';
import { CategoryDto } from '../../categories/dto/category.dto';
import { PackageFiltersDto } from './package-filters.dto';
export declare class PaginatedPackagesDto {
    data: PackageVariationDto[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
export declare class CityInfoDto {
    id: string;
    name: string;
    code?: string;
}
export declare class DivisionInfoDto {
    id: string;
    name: string;
    code: string;
}
export declare class CurrencyInfoDto {
    id: string;
    code: string;
    symbol: string;
    name: string;
}
export declare class AvailableFiltersDto {
    categories: Array<{
        id: string;
        name: string;
    }>;
    cities: Array<{
        id: string;
        name: string;
    }>;
    divisions: Array<{
        id: string;
        name: string;
    }>;
}
export declare class FilterInfoDto {
    applied: PackageFiltersDto;
    available: AvailableFiltersDto;
}
export declare class PackageCatalogMetadataDto {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalPackages: number;
    appliedFilters: number;
}
export declare class PackageCatalogDto {
    packages: PaginatedPackagesDto;
    categories: CategoryDto[];
    cities: CityInfoDto[];
    divisions: DivisionInfoDto[];
    currencies: CurrencyInfoDto[];
    filters: FilterInfoDto;
    metadata: PackageCatalogMetadataDto;
}
