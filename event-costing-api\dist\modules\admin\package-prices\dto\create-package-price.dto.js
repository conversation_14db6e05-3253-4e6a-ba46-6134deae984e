"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePackagePriceDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePackagePriceDto {
    currency_id;
    price;
    unit_base_cost;
    description;
}
exports.CreatePackagePriceDto = CreatePackagePriceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The currency ID for this price.',
        format: 'uuid',
        example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePackagePriceDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The base unit price for the package in this currency.',
        example: 1500000.0,
        type: Number,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreatePackagePriceDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The base unit cost for the package in this currency.',
        example: 750000.0,
        type: Number,
        default: 0,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreatePackagePriceDto.prototype, "unit_base_cost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional description for this price point.',
        example: 'Standard price for IDR market.',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePackagePriceDto.prototype, "description", void 0);
//# sourceMappingURL=create-package-price.dto.js.map