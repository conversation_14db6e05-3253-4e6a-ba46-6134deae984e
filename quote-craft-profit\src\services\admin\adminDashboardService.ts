/**
 * Consolidated Admin Dashboard Service
 * 
 * This service implements the consolidated endpoint pattern for admin dashboard operations.
 * It replaces the need for multiple separate API calls with a single consolidated endpoint.
 */

import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { showError } from '@/lib/notifications';

/**
 * Types for the consolidated admin dashboard response
 */
export interface AdminDashboardData {
  overview: {
    totalPackages: number;
    totalTemplates: number;
    totalCalculations: number;
    totalUsers: number;
    totalCategories: number;
    totalCities: number;
    totalDivisions: number;
  };
  categories: Array<{
    id: string;
    name: string;
    display_order: number;
    is_deleted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  cities: Array<{
    id: string;
    name: string;
    code?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }>;
  divisions: Array<{
    id: string;
    code: string;
    name: string;
    description?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }>;
  packages: {
    totalCount: number;
    activeCount: number;
    deletedCount: number;
  };
  templates: {
    totalCount: number;
    activeCount: number;
    publicCount: number;
    privateCount: number;
  };
  calculations: {
    totalCount: number;
    draftCount: number;
    finalizedCount: number;
  };
  users: {
    totalCount: number;
    activeCount: number;
  };
  recentActivity: Array<{
    type: string;
    id: string;
    name: string;
    action: string;
    timestamp: string;
  }>;
  filters: {
    applied: AdminDashboardFilters;
  };
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    dataPoints: {
      categoriesCount: number;
      citiesCount: number;
      divisionsCount: number;
      packagesCount: number;
      templatesCount: number;
      calculationsCount: number;
      usersCount: number;
      recentActivityCount: number;
    };
  };
}

/**
 * Admin dashboard summary data
 */
export interface AdminDashboardSummary {
  systemHealth: {
    status: string;
    uptime: number;
    lastCheck: string;
  };
  quickStats: {
    totalPackages: number;
    totalTemplates: number;
    totalCalculations: number;
    totalUsers: number;
    activeUsers: number;
    recentActivity: number;
  };
  performance: {
    avgResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  alerts: Array<{
    type: string;
    message: string;
    timestamp: string;
  }>;
  metadata: {
    loadTime: number;
    timestamp: string;
  };
}

/**
 * Admin dashboard health check data
 */
export interface AdminDashboardHealth {
  status: string;
  timestamp: string;
  checks: {
    database: string;
    cache: string;
    storage: string;
    api: string;
  };
  metrics: {
    responseTime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}

/**
 * Admin dashboard filters interface
 */
export interface AdminDashboardFilters {
  timePeriod?: 'last_7_days' | 'last_30_days' | 'last_90_days' | 'last_year' | 'all_time';
  activityDays?: number;
  viewMode?: 'overview' | 'detailed' | 'analytics';
  includeStatistics?: boolean;
  includeActivity?: boolean;
  includeHealth?: boolean;
  includePerformance?: boolean;
  categoryId?: string;
  cityId?: string;
  divisionId?: string;
  userId?: string;
  dateStart?: string;
  dateEnd?: string;
  refreshInterval?: number;
  includeCacheMetrics?: boolean;
  includeDatabaseMetrics?: boolean;
  includeApiMetrics?: boolean;
  activityLimit?: number;
  includeUserActivity?: boolean;
  includeAlerts?: boolean;
  includeTrends?: boolean;
}

/**
 * Fetch complete admin dashboard data in a single API call
 * This replaces the need for multiple separate API calls:
 * 1. GET /admin/categories (categories)
 * 2. GET /admin/cities (cities)
 * 3. GET /admin/divisions (divisions)
 * 4. GET /admin/packages (package statistics)
 * 5. GET /admin/templates (template statistics)
 * 6. GET /admin/calculations (calculation statistics)
 * 7. GET /admin/users (user statistics)
 * 8. Recent activity data
 * 
 * @param filters - Dashboard filters
 * @returns Complete admin dashboard data with metadata
 */
export const getAdminDashboardData = async (
  filters: AdminDashboardFilters = {},
): Promise<AdminDashboardData> => {
  try {
    console.log(`[API] Fetching admin dashboard data with filters:`, filters);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Map frontend filters to backend parameters
    if (filters.timePeriod) queryParams.append('timePeriod', filters.timePeriod);
    if (filters.activityDays) queryParams.append('activityDays', filters.activityDays.toString());
    if (filters.viewMode) queryParams.append('viewMode', filters.viewMode);
    if (filters.includeStatistics !== undefined) {
      queryParams.append('includeStatistics', filters.includeStatistics.toString());
    }
    if (filters.includeActivity !== undefined) {
      queryParams.append('includeActivity', filters.includeActivity.toString());
    }
    if (filters.includeHealth !== undefined) {
      queryParams.append('includeHealth', filters.includeHealth.toString());
    }
    if (filters.includePerformance !== undefined) {
      queryParams.append('includePerformance', filters.includePerformance.toString());
    }
    if (filters.categoryId) queryParams.append('categoryId', filters.categoryId);
    if (filters.cityId) queryParams.append('cityId', filters.cityId);
    if (filters.divisionId) queryParams.append('divisionId', filters.divisionId);
    if (filters.userId) queryParams.append('userId', filters.userId);
    if (filters.dateStart) queryParams.append('dateStart', filters.dateStart);
    if (filters.dateEnd) queryParams.append('dateEnd', filters.dateEnd);
    if (filters.refreshInterval) queryParams.append('refreshInterval', filters.refreshInterval.toString());
    if (filters.includeCacheMetrics !== undefined) {
      queryParams.append('includeCacheMetrics', filters.includeCacheMetrics.toString());
    }
    if (filters.includeDatabaseMetrics !== undefined) {
      queryParams.append('includeDatabaseMetrics', filters.includeDatabaseMetrics.toString());
    }
    if (filters.includeApiMetrics !== undefined) {
      queryParams.append('includeApiMetrics', filters.includeApiMetrics.toString());
    }
    if (filters.activityLimit) queryParams.append('activityLimit', filters.activityLimit.toString());
    if (filters.includeUserActivity !== undefined) {
      queryParams.append('includeUserActivity', filters.includeUserActivity.toString());
    }
    if (filters.includeAlerts !== undefined) {
      queryParams.append('includeAlerts', filters.includeAlerts.toString());
    }
    if (filters.includeTrends !== undefined) {
      queryParams.append('includeTrends', filters.includeTrends.toString());
    }

    // Make single API request to get all data
    const url = `${API_ENDPOINTS.ADMIN.DASHBOARD.GET_ALL}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    
    const response = await authClient.get(url);
    const data = response.data as AdminDashboardData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched admin dashboard data in ${loadTime}ms:`, {
      overview: data.overview,
      categories: data.categories.length,
      cities: data.cities.length,
      divisions: data.divisions.length,
      packages: data.packages,
      templates: data.templates,
      calculations: data.calculations,
      users: data.users,
      recentActivity: data.recentActivity.length,
      errors: data.metadata.errors?.length || 0,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching admin dashboard data:`, error);
    showError('Failed to load admin dashboard data', {
      description: 'There was an error loading the admin dashboard data. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch admin dashboard summary statistics
 * Provides quick overview metrics for admin dashboard
 * 
 * @returns Admin dashboard summary data
 */
export const getAdminDashboardSummary = async (): Promise<AdminDashboardSummary> => {
  try {
    console.log(`[API] Fetching admin dashboard summary`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to summary endpoint
    const response = await authClient.get(API_ENDPOINTS.ADMIN.DASHBOARD.GET_SUMMARY);
    const data = response.data as AdminDashboardSummary;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched admin dashboard summary in ${loadTime}ms:`, {
      systemHealth: data.systemHealth.status,
      quickStats: data.quickStats,
      performance: data.performance,
      alertsCount: data.alerts.length,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching admin dashboard summary:`, error);
    showError('Failed to load admin dashboard summary', {
      description: 'There was an error loading the admin dashboard summary. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch admin dashboard health check
 * Provides system health status for monitoring
 * 
 * @returns Admin dashboard health check data
 */
export const getAdminDashboardHealth = async (): Promise<AdminDashboardHealth> => {
  try {
    console.log(`[API] Fetching admin dashboard health check`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to health endpoint
    const response = await authClient.get(API_ENDPOINTS.ADMIN.DASHBOARD.GET_HEALTH);
    const data = response.data as AdminDashboardHealth;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched admin dashboard health check in ${loadTime}ms:`, {
      status: data.status,
      checks: data.checks,
      metrics: data.metrics,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching admin dashboard health check:`, error);
    showError('Failed to load admin dashboard health check', {
      description: 'There was an error loading the admin dashboard health check. Please try again.',
    });
    throw error;
  }
};
