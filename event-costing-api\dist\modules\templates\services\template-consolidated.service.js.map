{"version": 3, "file": "template-consolidated.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/templates/services/template-consolidated.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAKwB;AACxB,8EAA0E;AAE1E,uEAAkE;AAClE,iFAA4E;AAC5E,qEAAgE;AAChE,sEAAkE;AAClE,4EAAwE;AASjE,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAInB;IACA;IACA;IACA;IACA;IACA;IARF,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAEvE,YACmB,eAAgC,EAChC,qBAA4C,EAC5C,0BAAsD,EACtD,oBAA0C,EAC1C,eAAgC,EAChC,iBAAoC;QALpC,oBAAe,GAAf,eAAe,CAAiB;QAChC,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,+BAA0B,GAA1B,0BAA0B,CAA4B;QACtD,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,oBAAe,GAAf,eAAe,CAAiB;QAChC,sBAAiB,GAAjB,iBAAiB,CAAmB;IACpD,CAAC;IAUJ,KAAK,CAAC,yBAAyB,CAC7B,UAA8B,EAAE,EAChC,IAAU;QAEV,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,+CAA+C,IAAI,CAAC,KAAK,EAAE,CAC5D,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,CACJ,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,aAAa,EACd,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC3B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;gBAChC,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;aAC9B,CAAC,CAAC;YAGH,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,EAAE;gBACjE,IAAI,EAAE,EAAE;gBACR,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACvE,MAAM,OAAO,GAAG,WAAW,IAAI,SAAS,CAAC;YAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;gBAChC,eAAe;gBACf,gBAAgB;gBAChB,gBAAgB;gBAChB,cAAc;gBACd,aAAa;aACd,CAAC,CAAC;YAEH,MAAM,MAAM,GAA4B;gBACtC,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,OAAO,EAAE;oBACP,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE;wBACT,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;wBACnE,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;qBACjE;iBACF;gBACD,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY,EAAE,KAAK;oBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,MAAM;oBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,cAAc,EAAE,SAAS,CAAC,UAAU;oBACpC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;iBAC5C;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,oDAAoD,QAAQ,IAAI,CACjE,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,EAC1C,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,4DAA4D,CAC7D,CAAC;QACJ,CAAC;IACH,CAAC;IAMD,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,IAAU;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,kDAAkD,UAAU,EAAE,CAC/D,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,CACJ,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EACjB,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC;gBACtC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC;gBAC1C,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC;gBAC7C,IAAI,CAAC,aAAa,EAAE;aACrB,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CACpC,iBAAiB,EACjB,aAAa,EACb,IAAI,CACL,CAAC;YACF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;YAE1E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;gBAChC,cAAc;gBACd,cAAc;gBACd,iBAAiB;gBACjB,gBAAgB;aACjB,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ;gBACR,QAAQ;gBACR,WAAW;gBACX,UAAU;gBACV,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY,EAAE,KAAK;oBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,MAAM;oBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gDAAgD,UAAU,EAAE,EAC5D,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,wDAAwD,CACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,OAA2B,EAAE,IAAU;QAEhE,MAAM,QAAQ,GAAG;YACf,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,SAAS,EAAE,OAAO,CAAC,YAAY;YAC/B,OAAO,EAAE,OAAO,CAAC,aAAa;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAa;YAC7B,SAAS,EAAE,OAAO,CAAC,SAAgB;YACnC,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAC7B,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;SAC7D,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAC9D,IAAI,EACJ,QAAQ,CACT,CAAC;QAGF,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,UAAU,EAAE,MAAM,CAAC,KAAK;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;YACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;SAC/D,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,IAAU;QAC1D,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IAC9D,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,UAAkB,EAAE,IAAU;QAE9D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,mBAAmB,CAAC;aACzB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAEjC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,IAAU;QACjE,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,CACjE,UAAU,CACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,aAAa,CAAC;aACnB,MAAM,CAAC,uBAAuB,CAAC;aAC/B,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,KAAK,CAAC,MAAM,CAAC,CAAC;QAEjB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,IAAU;QAE3C,MAAM,QAAQ,GAAG;YACf,UAAU,EAAE,qBAAqB;YACjC,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,CAAC;SACV,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnE,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,IAAU;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,WAAW,CAAC;aACnB,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,WAAW,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QACvE,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QAE1E,OAAO;YACL,cAAc,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;YACzC,eAAe,EAAE,WAAW;YAC5B,iBAAiB,EAAE,aAAa;YAChC,eAAe,EAAE,CAAC;YAClB,gBAAgB,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;SAC5C,CAAC;IACJ,CAAC;IAKO,aAAa,CACnB,MAA+B,EAC/B,IAAY,EACZ,YAAgB;QAEhB,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mBAAmB,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,EAAE,CACxE,CAAC;QAEF,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,YAAY,CAAC;QACtB,CAAC;QAGD,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,MAAM,MAAM,CAAC,MAAM,CAAC;QACtB,CAAC;QAGD,OAAO,EAAkB,CAAC;IAC5B,CAAC;IAKO,aAAa,CAAC,OAAoC;QACxD,OAAO,OAAO;aACX,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC;aAC9C,GAAG,CACF,MAAM,CAAC,EAAE,CACN,MAAgC,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,CACvE,CAAC;IACN,CAAC;CACF,CAAA;AArWY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAKyB,kCAAe;QACT,+CAAqB;QAChB,yDAA0B;QAChC,6CAAoB;QACzB,kCAAe;QACb,sCAAiB;GAT5C,2BAA2B,CAqWvC"}