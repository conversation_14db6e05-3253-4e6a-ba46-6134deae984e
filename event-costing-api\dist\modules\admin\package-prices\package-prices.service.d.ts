import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackagePriceDto } from './dto/create-package-price.dto';
import { PackagePriceDto } from './dto/package-price.dto';
import { UpdatePackagePriceDto } from './dto/update-package-price.dto';
export declare class PackagePricesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    create(packageId: string, createDto: CreatePackagePriceDto): Promise<PackagePriceDto>;
    findAll(packageId: string): Promise<PackagePriceDto[]>;
    findOne(packageId: string, packagePriceId: string): Promise<PackagePriceDto>;
    update(packageId: string, packagePriceId: string, updateDto: UpdatePackagePriceDto): Promise<PackagePriceDto>;
    remove(packageId: string, packagePriceId: string): Promise<void>;
    private handlePackagePriceError;
    private checkPackageExists;
}
