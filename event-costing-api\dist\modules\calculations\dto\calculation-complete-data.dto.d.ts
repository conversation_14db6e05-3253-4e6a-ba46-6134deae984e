import { CalculationDetailDto } from './calculation-detail.dto';
import { LineItemDto } from '../../calculation-items/dto/line-item.dto';
import { CategoryWithPackagesDto } from '../../packages/dto/packages-by-category-response.dto';
import { CategoryDto } from '../../categories/dto/category.dto';
export declare class CalculationCompleteDataMetadataDto {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
}
export declare class CalculationCompleteDataDto {
    calculation: CalculationDetailDto;
    lineItems: LineItemDto[];
    packages: CategoryWithPackagesDto[];
    categories: CategoryDto[];
    metadata: CalculationCompleteDataMetadataDto;
}
