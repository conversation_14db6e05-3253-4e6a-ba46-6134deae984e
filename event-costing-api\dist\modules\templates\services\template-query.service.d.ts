import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListTemplatesDto } from '../dto/list-templates.dto';
import { PaginatedTemplatesResponse } from '../dto/template-summary.dto';
import { TemplateVenueService } from './template-venue.service';
export declare class TemplateQueryService {
    private readonly supabaseService;
    private readonly templateVenueService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateVenueService: TemplateVenueService);
    findUserTemplates(user: User, queryDto: ListTemplatesDto): Promise<PaginatedTemplatesResponse>;
    findPublicTemplates(queryDto: ListTemplatesDto): Promise<PaginatedTemplatesResponse>;
}
