"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDashboardFiltersDto = exports.DashboardViewMode = exports.DashboardTimePeriod = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var DashboardTimePeriod;
(function (DashboardTimePeriod) {
    DashboardTimePeriod["LAST_7_DAYS"] = "last_7_days";
    DashboardTimePeriod["LAST_30_DAYS"] = "last_30_days";
    DashboardTimePeriod["LAST_90_DAYS"] = "last_90_days";
    DashboardTimePeriod["LAST_YEAR"] = "last_year";
    DashboardTimePeriod["ALL_TIME"] = "all_time";
})(DashboardTimePeriod || (exports.DashboardTimePeriod = DashboardTimePeriod = {}));
var DashboardViewMode;
(function (DashboardViewMode) {
    DashboardViewMode["OVERVIEW"] = "overview";
    DashboardViewMode["DETAILED"] = "detailed";
    DashboardViewMode["ANALYTICS"] = "analytics";
})(DashboardViewMode || (exports.DashboardViewMode = DashboardViewMode = {}));
class AdminDashboardFiltersDto {
    timePeriod;
    activityDays;
    viewMode;
    includeStatistics;
    includeActivity;
    includeHealth;
    includePerformance;
    categoryId;
    cityId;
    divisionId;
    userId;
    dateStart;
    dateEnd;
    refreshInterval;
    includeCacheMetrics;
    includeDatabaseMetrics;
    includeApiMetrics;
    activityLimit;
    includeUserActivity;
    includeAlerts;
    includeTrends;
}
exports.AdminDashboardFiltersDto = AdminDashboardFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Time period for activity and statistics',
        enum: DashboardTimePeriod,
        example: DashboardTimePeriod.LAST_30_DAYS,
        default: DashboardTimePeriod.LAST_30_DAYS,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(DashboardTimePeriod),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "timePeriod", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of days for recent activity',
        example: 7,
        default: 7,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], AdminDashboardFiltersDto.prototype, "activityDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dashboard view mode',
        enum: DashboardViewMode,
        example: DashboardViewMode.OVERVIEW,
        default: DashboardViewMode.OVERVIEW,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(DashboardViewMode),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "viewMode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include detailed statistics',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeStatistics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include recent activity',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeActivity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include system health metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeHealth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include performance metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includePerformance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by specific category ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "categoryId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by specific city ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "cityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by specific division ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "divisionId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by specific user ID',
        type: String,
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Custom date range start',
        type: String,
        format: 'date',
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "dateStart", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Custom date range end',
        type: String,
        format: 'date',
        example: '2024-12-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AdminDashboardFiltersDto.prototype, "dateEnd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Refresh interval in seconds for real-time updates',
        example: 30,
        default: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(3600),
    __metadata("design:type", Number)
], AdminDashboardFiltersDto.prototype, "refreshInterval", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include cache performance metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeCacheMetrics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include database performance metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeDatabaseMetrics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include API usage metrics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeApiMetrics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Limit for recent activity items',
        example: 20,
        default: 20,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], AdminDashboardFiltersDto.prototype, "activityLimit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include user activity breakdown',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeUserActivity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include system alerts and warnings',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeAlerts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include growth trends and analytics',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdminDashboardFiltersDto.prototype, "includeTrends", void 0);
//# sourceMappingURL=admin-dashboard-filters.dto.js.map