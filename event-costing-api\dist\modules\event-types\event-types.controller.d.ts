import { EventTypesService } from './event-types.service';
import { CreateEventTypeDto, UpdateEventTypeDto, EventTypeDto } from './dto/event-type.dto';
export declare class EventTypesController {
    private readonly eventTypesService;
    constructor(eventTypesService: EventTypesService);
    findAll(): Promise<{
        data: EventTypeDto[];
    }>;
    findOne(id: string): Promise<{
        data: EventTypeDto;
    }>;
}
export declare class AdminEventTypesController {
    private readonly eventTypesService;
    constructor(eventTypesService: EventTypesService);
    findAllAdmin(): Promise<{
        data: EventTypeDto[];
    }>;
    findOne(id: string): Promise<{
        data: EventTypeDto;
    }>;
    create(createEventTypeDto: CreateEventTypeDto): Promise<{
        data: EventTypeDto;
    }>;
    update(id: string, updateEventTypeDto: UpdateEventTypeDto): Promise<{
        data: EventTypeDto;
    }>;
    remove(id: string): Promise<void>;
}
