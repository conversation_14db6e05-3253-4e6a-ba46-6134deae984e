"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TemplatesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatesController = void 0;
const common_1 = require("@nestjs/common");
const templates_service_1 = require("./templates.service");
const list_templates_dto_1 = require("./dto/list-templates.dto");
const template_summary_dto_1 = require("./dto/template-summary.dto");
const template_summary_dto_2 = require("./dto/template-summary.dto");
const swagger_1 = require("@nestjs/swagger");
let TemplatesController = TemplatesController_1 = class TemplatesController {
    templatesService;
    logger = new common_1.Logger(TemplatesController_1.name);
    constructor(templatesService) {
        this.templatesService = templatesService;
    }
    async findPublicTemplates(queryDto) {
        this.logger.log(`Public request fetching templates with query: ${JSON.stringify(queryDto)}`);
        return this.templatesService.findPublicTemplates(queryDto);
    }
    async findOnePublic(id) {
        this.logger.log(`Public request fetching template details for ID: ${id}`);
        const result = await this.templatesService.findOnePublic(id);
        this.logger.log(`Public template response for ID ${id}: ${JSON.stringify({
            id: result.id,
            name: result.name,
            venue_ids: result.venue_ids,
            has_venue_ids: !!result.venue_ids,
            venue_ids_length: result.venue_ids?.length || 0,
        })}`);
        return result;
    }
    async findOnePublicEnhanced(id) {
        this.logger.log(`Public request fetching enhanced template details for ID: ${id}`);
        const result = await this.templatesService.findOnePublicEnhanced(id);
        this.logger.log(`Enhanced public template response for ID ${id}: ${JSON.stringify({
            id: result.id,
            name: result.name,
            package_selections_count: result.package_selections?.length || 0,
            has_package_names: result.package_selections?.some(sel => sel.package_name) || false,
        })}`);
        return result;
    }
};
exports.TemplatesController = TemplatesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List public templates' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'List of public templates with pagination.',
        type: template_summary_dto_1.PaginatedTemplatesResponse,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_templates_dto_1.ListTemplatesDto]),
    __metadata("design:returntype", Promise)
], TemplatesController.prototype, "findPublicTemplates", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific public template by ID' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Public template details.',
        type: template_summary_dto_2.TemplateDetailDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Public template not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TemplatesController.prototype, "findOnePublic", null);
__decorate([
    (0, common_1.Get)(':id/enhanced'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific public template by ID with enhanced package and option names' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Public template details with package and option names.',
        type: template_summary_dto_2.EnhancedTemplateDetailDto,
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Public template not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TemplatesController.prototype, "findOnePublicEnhanced", null);
exports.TemplatesController = TemplatesController = TemplatesController_1 = __decorate([
    (0, common_1.Controller)('templates'),
    (0, swagger_1.ApiTags)('Templates'),
    __metadata("design:paramtypes", [templates_service_1.TemplatesService])
], TemplatesController);
//# sourceMappingURL=templates.controller.js.map