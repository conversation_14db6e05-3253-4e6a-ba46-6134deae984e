"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateDetailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateDetailService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_venue_service_1 = require("./template-venue.service");
const template_constants_1 = require("../constants/template.constants");
let TemplateDetailService = TemplateDetailService_1 = class TemplateDetailService {
    supabaseService;
    templateVenueService;
    logger = new common_1.Logger(TemplateDetailService_1.name);
    constructor(supabaseService, templateVenueService) {
        this.supabaseService = supabaseService;
        this.templateVenueService = templateVenueService;
    }
    async findOnePublic(id) {
        this.logger.log(`Finding public template with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .select(template_constants_1.TemplateConstants.DETAIL_SELECT_FIELDS)
            .eq('id', id)
            .eq('is_public', true)
            .eq('is_deleted', false)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Public template with ID ${id} not found.`);
            }
            this.logger.error(`Error fetching public template ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve public template.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Public template with ID ${id} not found.`);
        }
        const template = {
            ...data,
            template_start_date: data.template_start_date
                ? new Date(data.template_start_date)
                : undefined,
            template_end_date: data.template_end_date
                ? new Date(data.template_end_date)
                : undefined,
            created_at: new Date(data.created_at),
            updated_at: new Date(data.updated_at),
            is_deleted: data.is_deleted || false,
        };
        await this.templateVenueService.addVenueIdsToTemplate(template);
        return template;
    }
    async findOnePublicEnhanced(id) {
        this.logger.log(`Finding enhanced public template with ID: ${id}`);
        const template = await this.findOnePublic(id);
        const enhancedTemplate = await this.enhanceTemplateWithNames(template);
        return enhancedTemplate;
    }
    async enhanceTemplateWithNames(template) {
        const supabase = this.supabaseService.getClient();
        if (!template.package_selections || template.package_selections.length === 0) {
            return {
                ...template,
                package_selections: [],
            };
        }
        const packageIds = [...new Set(template.package_selections.map(sel => sel.package_id))];
        const allOptionIds = [...new Set(template.package_selections.flatMap(sel => sel.option_ids))];
        const { data: packages, error: packagesError } = await supabase
            .from('packages')
            .select('id, name')
            .in('id', packageIds);
        if (packagesError) {
            this.logger.error(`Error fetching package names: ${packagesError.message}`);
            throw new common_1.InternalServerErrorException('Could not retrieve package names.');
        }
        let options = [];
        if (allOptionIds.length > 0) {
            const { data: optionsData, error: optionsError } = await supabase
                .from('package_options')
                .select('id, option_name')
                .in('id', allOptionIds);
            if (optionsError) {
                this.logger.error(`Error fetching option names: ${optionsError.message}`);
                throw new common_1.InternalServerErrorException('Could not retrieve option names.');
            }
            options = optionsData || [];
        }
        const packageNameMap = new Map(packages?.map(pkg => [pkg.id, pkg.name]) || []);
        const optionNameMap = new Map(options.map(opt => [opt.id, opt.option_name]));
        const enhancedPackageSelections = template.package_selections.map(selection => ({
            ...selection,
            package_name: packageNameMap.get(selection.package_id) || 'Unknown Package',
            option_names: selection.option_ids.map(optionId => optionNameMap.get(optionId) || 'Unknown Option'),
        }));
        return {
            ...template,
            package_selections: enhancedPackageSelections,
        };
    }
};
exports.TemplateDetailService = TemplateDetailService;
exports.TemplateDetailService = TemplateDetailService = TemplateDetailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        template_venue_service_1.TemplateVenueService])
], TemplateDetailService);
//# sourceMappingURL=template-detail.service.js.map