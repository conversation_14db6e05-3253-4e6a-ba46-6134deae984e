import { ExportManagementFiltersDto } from './export-management-filters.dto';
import { ExportFormat } from '../enums/export-format.enum';
export declare class PaginatedExportsDto {
    data: Array<{
        id: string;
        calculation_id: string;
        format: ExportFormat;
        status: string;
        file_url?: string;
        download_url?: string;
        error_message?: string;
        created_at: string;
        updated_at: string;
        calculations?: any;
    }>;
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
export declare class CalculationInfoDto {
    id: string;
    name: string;
    status: string;
    created_at: string;
}
export declare class ExportStatisticsDto {
    totalExports: number;
    completedExports: number;
    failedExports: number;
    pendingExports: number;
    exportsByFormat: Record<string, number>;
    exportsByStatus: Record<string, number>;
}
export declare class RecentExportActivityDto {
    id: string;
    calculationId: string;
    calculationName: string;
    format: ExportFormat;
    status: string;
    timestamp: string;
}
export declare class ExportAvailableFiltersDto {
    formats: ExportFormat[];
    statuses: string[];
    calculations: Array<{
        id: string;
        name: string;
    }>;
}
export declare class ExportFilterInfoDto {
    applied: ExportManagementFiltersDto;
    available: ExportAvailableFiltersDto;
}
export declare class ExportManagementMetadataDto {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalExports: number;
    appliedFilters: number;
}
export declare class ExportManagementDataDto {
    exports: PaginatedExportsDto;
    calculations: CalculationInfoDto[];
    statistics: ExportStatisticsDto;
    recentActivity: RecentExportActivityDto[];
    supportedFormats: ExportFormat[];
    filters: ExportFilterInfoDto;
    metadata: ExportManagementMetadataDto;
}
