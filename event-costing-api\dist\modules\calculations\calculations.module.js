"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationsModule = void 0;
const common_1 = require("@nestjs/common");
const calculations_service_1 = require("./calculations.service");
const calculations_controller_1 = require("./calculations.controller");
const calculations_packages_controller_1 = require("./calculations-packages.controller");
const calculation_line_items_controller_1 = require("./controllers/calculation-line-items.controller");
const calculation_recalculation_controller_1 = require("./controllers/calculation-recalculation.controller");
const auth_module_1 = require("../auth/auth.module");
const calculation_items_module_1 = require("../calculation-items/calculation-items.module");
const packages_module_1 = require("../packages/packages.module");
const categories_module_1 = require("../categories/categories.module");
const calculation_logic_service_1 = require("./calculation-logic.service");
const calculation_template_service_1 = require("./calculation-template.service");
const services_1 = require("./services");
const calculation_complete_data_service_1 = require("./services/calculation-complete-data.service");
let CalculationsModule = class CalculationsModule {
};
exports.CalculationsModule = CalculationsModule;
exports.CalculationsModule = CalculationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            calculation_items_module_1.CalculationItemsModule,
            packages_module_1.PackagesModule,
            categories_module_1.CategoriesModule,
        ],
        controllers: [
            calculations_controller_1.CalculationsController,
            calculations_packages_controller_1.CalculationsPackagesController,
            calculation_line_items_controller_1.CalculationLineItemsController,
            calculation_recalculation_controller_1.CalculationRecalculationController,
        ],
        providers: [
            calculations_service_1.CalculationsService,
            calculation_logic_service_1.CalculationLogicService,
            calculation_template_service_1.CalculationTemplateService,
            services_1.CalculationCrudService,
            services_1.CalculationVenueService,
            services_1.CalculationValidationService,
            services_1.CalculationTransformationService,
            services_1.CalculationStatusService,
            calculation_complete_data_service_1.CalculationCompleteDataService,
        ],
        exports: [
            calculations_service_1.CalculationsService,
            calculation_logic_service_1.CalculationLogicService,
            calculation_template_service_1.CalculationTemplateService,
            services_1.CalculationCrudService,
            services_1.CalculationVenueService,
            services_1.CalculationValidationService,
            services_1.CalculationTransformationService,
            services_1.CalculationStatusService,
        ],
    })
], CalculationsModule);
//# sourceMappingURL=calculations.module.js.map