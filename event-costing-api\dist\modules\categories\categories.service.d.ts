import { SupabaseService } from '../../core/supabase/supabase.service';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryOrderItemDto } from './dto/update-category-order.dto';
import { CategoryOrderResponseDto } from './dto/category-order-response.dto';
export declare class CategoriesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    private readonly selectFields;
    private readonly uniqueConstraint;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<CategoryDto[]>;
    findOneById(id: string): Promise<CategoryDto>;
    createCategory(createDto: CreateCategoryDto): Promise<CategoryDto>;
    updateCategory(id: string, updateDto: UpdateCategoryDto): Promise<CategoryDto>;
    deleteCategory(id: string): Promise<void>;
    updateCategoryOrder(categories: CategoryOrderItemDto[]): Promise<CategoryOrderResponseDto>;
}
