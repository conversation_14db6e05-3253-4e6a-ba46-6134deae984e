export {};
import { CalculationStatus } from '../enums/calculation-status.enum';
export interface TaxDetailItem {
    name?: string | null;
    rate?: number | null;
    amount?: number | null;
}
export interface DiscountDetail {
    name?: string | null;
    amount?: number | null;
    description?: string | null;
    percentage?: number | null;
}
export interface PackageSelectionItem {
    package_id: string;
    option_ids: string[];
    item_quantity: number | null;
    item_quantity_basis: number | null;
}
export interface TemplateData {
    id: string;
    template_name: string;
    template_start_date?: string | null;
    template_end_date?: string | null;
    attendees?: number | null;
    currency_id: string;
    city_id: string | null;
    event_type: string | null;
    notes: string | null;
    package_selections: PackageSelectionItem[] | null;
    created_by: string;
    taxes?: unknown;
    discount?: unknown;
}
export interface CalculationHistoryRaw {
    id: string;
    name: string;
    currency_id: string;
    city_id: string | null;
    client_id: string | null;
    event_id: string | null;
    event_start_date: string | null;
    event_end_date: string | null;
    attendees: number | null;
    event_type_id: string | null;
    notes: string | null;
    version_notes: string | null;
    status: CalculationStatus;
    subtotal: number;
    taxes: unknown;
    discount: unknown;
    total: number;
    total_cost: number;
    estimated_profit: number;
    created_by: string;
    created_at: string;
    updated_at: string;
    currency: {
        id: string;
        code: string;
    } | null;
    cities?: {
        id: string;
        name: string;
    } | null;
    clients?: {
        id: string;
        client_name: string;
        contact_person?: string | null;
        email?: string | null;
    } | null;
    events?: {
        id: string;
        event_name: string;
    } | null;
    calculation_line_items?: CalculationLineItemRaw[];
    calculation_custom_items?: CalculationCustomItemRaw[];
}
export interface CalculationLineItemRaw {
    id: string;
    package_id: string | null;
    item_name_snapshot: string;
    option_summary_snapshot: string | null;
    item_quantity: number;
    duration_days: number;
    unit_base_price: number;
    options_total_adjustment: number;
    calculated_line_total: number;
    notes: string | null;
    unit_base_cost_snapshot: number;
    options_total_cost_snapshot: number;
    calculated_line_cost: number;
    calculation_line_item_options?: CalculationLineItemOptionRaw[];
}
export interface CalculationLineItemOptionRaw {
    option_id: string;
    price_adjustment_snapshot: number;
    package_options?: {
        option_name: string;
    } | null;
}
export interface CalculationCustomItemRaw {
    id: string;
    item_name: string;
    description: string | null;
    quantity: number;
    unit_price: number;
    unit_cost: number;
    currency_id: string;
    city_id: string | null;
    category_id: string | null;
}
export interface CalculationTotalsRaw {
    subtotal: number | null;
    total_package_price: number | null;
    total_custom_price: number | null;
    total_tax_amount: number | null;
    total_discount_amount: number | null;
    total: number | null;
    total_base_cost: number | null;
    total_package_cost: number | null;
    total_custom_cost: number | null;
}
export interface CalculationSummaryRaw {
    id: string;
    name: string;
    status: CalculationStatus;
    created_at: string;
    updated_at: string;
    version_notes: string | null;
    event_start_date: string | null;
    event_end_date: string | null;
    currency_id: string;
    client_id: string | null;
    event_id: string | null;
    clients: {
        id: string;
        client_name: string;
    } | null;
    events: {
        id: string;
        event_name: string;
    } | null;
    currencies: {
        id: string;
        code: string;
    } | null;
    total: number | null;
    total_cost: number | null;
    estimated_profit: number | null;
}
