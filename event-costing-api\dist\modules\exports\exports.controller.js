"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ExportsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const exports_service_1 = require("./exports.service");
const create_export_dto_1 = require("./dto/create-export.dto");
const export_response_dto_1 = require("./dto/export-response.dto");
const export_status_response_dto_1 = require("./dto/export-status-response.dto");
let ExportsController = ExportsController_1 = class ExportsController {
    exportsService;
    logger = new common_1.Logger(ExportsController_1.name);
    constructor(exportsService) {
        this.exportsService = exportsService;
    }
    async initiateExport(createExportDto, user) {
        this.logger.log(`User ${user.id} initiating export for calc ${createExportDto.calculationId} as ${createExportDto.format}`);
        try {
            return await this.exportsService.initiateExport(createExportDto, user);
        }
        catch (error) {
            this.logger.error(`Failed to initiate export for user ${user.id}, calc ${createExportDto.calculationId}: ${error instanceof Error ? error.message : error}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getExportsByCalculation(calculationId, user) {
        this.logger.log(`User ${user.id} fetching exports for calculation ${calculationId}`);
        try {
            return await this.exportsService.getExportsByCalculation(calculationId, user.id);
        }
        catch (error) {
            this.logger.error(`Failed to get exports for calculation ${calculationId}, user ${user.id}: ${error instanceof Error ? error.message : error}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async getExportStatus(id, user) {
        this.logger.log(`User ${user.id} checking status for export ${id}`);
        try {
            return await this.exportsService.getExportStatus(id, user.id);
        }
        catch (error) {
            this.logger.error(`Failed to get status for export ${id}, user ${user.id}: ${error instanceof Error ? error.message : error}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
};
exports.ExportsController = ExportsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.ACCEPTED),
    (0, swagger_1.ApiOperation)({
        summary: 'Initiate Calculation Export',
        description: 'Starts an asynchronous job to generate an export file (xlsx or PDF) for a specific calculation. Returns the ID of the export record.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.ACCEPTED,
        description: 'Export process successfully initiated.',
        type: export_response_dto_1.ExportResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input (e.g., missing fields, invalid format).',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or not accessible by the user.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Failed to initiate export process.',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_export_dto_1.CreateExportDto, Object]),
    __metadata("design:returntype", Promise)
], ExportsController.prototype, "initiateExport", null);
__decorate([
    (0, common_1.Get)('calculation/:calculationId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Exports by Calculation ID',
        description: 'Retrieves all export records for a specific calculation.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'calculationId',
        type: 'string',
        format: 'uuid',
        description: 'Calculation ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Export history retrieved successfully.',
        type: [export_status_response_dto_1.ExportStatusResponseDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ExportsController.prototype, "getExportsByCalculation", null);
__decorate([
    (0, common_1.Get)(':id/status'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Export Status',
        description: 'Retrieves the status and details (like download URL if completed) of a specific export job.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The UUID of the export record.' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Export status retrieved successfully.',
        type: export_status_response_dto_1.ExportStatusResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Export record not found or not accessible by the user.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Failed to retrieve export status.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ExportsController.prototype, "getExportStatus", null);
exports.ExportsController = ExportsController = ExportsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Exports'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('exports'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [exports_service_1.ExportsService])
], ExportsController);
//# sourceMappingURL=exports.controller.js.map