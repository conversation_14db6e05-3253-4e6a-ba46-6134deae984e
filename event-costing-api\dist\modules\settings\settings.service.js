"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SettingsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let SettingsService = SettingsService_1 = class SettingsService {
    supabaseService;
    logger = new common_1.Logger(SettingsService_1.name);
    tableName = 'settings';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async getSetting(key) {
        this.logger.debug(`Fetching setting with key: ${key}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select('key, value, description, created_at, updated_at')
            .eq('key', key)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                this.logger.warn(`Setting not found for key: ${key}`);
                throw new common_1.NotFoundException(`Setting with key "${key}" not found.`);
            }
            this.logger.error(`Error fetching setting ${key}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve setting.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Setting with key "${key}" not found.`);
        }
        return data;
    }
    async updateSetting(key, updateDto) {
        this.logger.debug(`Upserting setting with key: ${key}`);
        const supabase = this.supabaseService.getClient();
        let parsedValue;
        try {
            parsedValue = JSON.parse(updateDto.value);
        }
        catch (e) {
            this.logger.error(`Failed to parse JSON value for setting ${key}`, e);
            throw new common_1.BadRequestException('Invalid JSON format for value.');
        }
        const upsertData = {
            key: key,
            value: parsedValue,
            description: updateDto.description,
        };
        const { data, error } = await supabase
            .from(this.tableName)
            .upsert(upsertData)
            .select('key, value, description, created_at, updated_at')
            .single();
        if (error) {
            this.logger.error(`Error upserting setting ${key}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update setting.');
        }
        if (!data) {
            this.logger.error(`Upsert for setting ${key} succeeded but returned no data.`);
            throw new common_1.InternalServerErrorException('Failed to retrieve updated setting.');
        }
        this.logger.log(`Successfully upserted setting: ${key}`);
        return data;
    }
};
exports.SettingsService = SettingsService;
exports.SettingsService = SettingsService = SettingsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], SettingsService);
//# sourceMappingURL=settings.service.js.map