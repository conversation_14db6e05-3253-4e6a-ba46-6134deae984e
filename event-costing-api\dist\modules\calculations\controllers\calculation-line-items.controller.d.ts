import { CalculationItemsService } from '../../calculation-items/calculation-items.service';
import { CalculationCrudService } from '../services/calculation-crud.service';
import { AddPackageLineItemDto } from '../../calculation-items/dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from '../../calculation-items/dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../../calculation-items/dto/update-line-item.dto';
import { LineItemDto } from '../../calculation-items/dto/line-item.dto';
import { ItemIdResponse } from '../../calculation-items/dto/item-id-response.dto';
import { User } from '@supabase/supabase-js';
export declare class CalculationLineItemsController {
    private readonly calculationItemsService;
    private readonly calculationCrudService;
    private readonly logger;
    constructor(calculationItemsService: CalculationItemsService, calculationCrudService: CalculationCrudService);
    private validateCalculationOwnership;
    addPackageLineItem(calculationId: string, addPackageDto: AddPackageLineItemDto, user: User): Promise<ItemIdResponse>;
    addCustomLineItem(calculationId: string, addCustomDto: AddCustomLineItemDto, user: User): Promise<ItemIdResponse>;
    updateLineItem(calculationId: string, lineItemId: string, updateDto: UpdateLineItemDto, user: User): Promise<LineItemDto>;
    deleteLineItem(calculationId: string, lineItemId: string, user: User): Promise<void>;
}
