"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationLogicService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationLogicService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let CalculationLogicService = CalculationLogicService_1 = class CalculationLogicService {
    supabaseService;
    logger = new common_1.Logger(CalculationLogicService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async fetchAndCalculateTotals(calcId) {
        const { data, error, } = await this.supabaseService
            .getClient()
            .from('calculation_history')
            .select(`
          id,
          subtotal,
          taxes,
          discount,
          total,
          total_cost,
          estimated_profit
        `)
            .eq('id', calcId)
            .maybeSingle();
        if (error) {
            let message;
            let stack = undefined;
            if (error instanceof Error) {
                message = error.message;
                stack = error.stack;
            }
            else if (typeof error === 'object' && error !== null) {
                try {
                    message = JSON.stringify(error);
                }
                catch {
                    message = '[Object cannot be stringified]';
                }
            }
            else {
                message =
                    typeof error === 'string'
                        ? error
                        : typeof error === 'number' || typeof error === 'boolean'
                            ? error.toString()
                            : '[Unknown error type]';
            }
            this.logger.error(`Error fetching calculation totals data for ID ${calcId}: ${message}`, stack);
            return null;
        }
        if (!data) {
            this.logger.warn(`No calculation history found for totals fetch with ID: ${calcId}`);
            return null;
        }
        let calculatedTotalTax = 0;
        if (data.taxes && Array.isArray(data.taxes)) {
            const subtotalForTax = data.subtotal ?? 0;
            calculatedTotalTax = data.taxes.reduce((sum, tax) => {
                let currentTaxAmount = 0;
                if (tax && typeof tax.amount === 'number') {
                    currentTaxAmount = tax.amount;
                }
                else if (tax && typeof tax.rate === 'number' && subtotalForTax > 0) {
                    currentTaxAmount = subtotalForTax * (tax.rate / 100);
                }
                return sum + currentTaxAmount;
            }, 0);
        }
        let discountAmount = 0;
        const fetchedDiscountAmount = data.discount?.amount;
        if (typeof fetchedDiscountAmount === 'number') {
            discountAmount = fetchedDiscountAmount;
        }
        const totalsDto = {
            subtotal: data.subtotal ?? 0,
            lineItemsTotal: 0,
            customItemsTotal: 0,
            taxTotal: calculatedTotalTax,
            discountAmount: discountAmount,
            total: data.total ?? 0,
            totalCost: data.total_cost ?? 0,
            estimatedProfit: data.estimated_profit ?? 0,
        };
        return totalsDto;
    }
    async recalculateTotals(calcId) {
        this.logger.log(`Triggering recalculation for calculation ID: ${calcId}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase.rpc('recalculate_calculation_totals', {
            p_calculation_id: calcId,
        });
        if (error) {
            this.logger.error(`Error recalculating totals for calculation ${calcId}: ${error.message}`, error.stack);
            throw new Error(`Failed to recalculate totals: ${error.message}`);
        }
        this.logger.log(`Recalculation completed for calculation ID: ${calcId}`);
    }
};
exports.CalculationLogicService = CalculationLogicService;
exports.CalculationLogicService = CalculationLogicService = CalculationLogicService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationLogicService);
//# sourceMappingURL=calculation-logic.service.js.map