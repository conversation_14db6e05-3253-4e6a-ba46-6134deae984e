"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminSettingsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminSettingsController = void 0;
const common_1 = require("@nestjs/common");
const settings_service_1 = require("./settings.service");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const setting_dto_1 = require("./dto/setting.dto");
const update_setting_dto_1 = require("./dto/update-setting.dto");
const swagger_1 = require("@nestjs/swagger");
let AdminSettingsController = AdminSettingsController_1 = class AdminSettingsController {
    settingsService;
    logger = new common_1.Logger(AdminSettingsController_1.name);
    constructor(settingsService) {
        this.settingsService = settingsService;
    }
    async getSetting(key) {
        this.logger.log(`Admin request to get setting: ${key}`);
        return await this.settingsService.getSetting(key);
    }
    async updateSetting(key, updateDto) {
        this.logger.log(`Admin request to update setting: ${key}`);
        return await this.settingsService.updateSetting(key, updateDto);
    }
};
exports.AdminSettingsController = AdminSettingsController;
__decorate([
    (0, common_1.Get)(':key'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific setting by key' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'The unique key of the setting',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting found', type: setting_dto_1.SettingDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Requires Admin Role)' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "getSetting", null);
__decorate([
    (0, common_1.Put)(':key'),
    (0, swagger_1.ApiOperation)({ summary: 'Update (or create) a setting by key' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'The unique key of the setting',
        type: String,
    }),
    (0, swagger_1.ApiBody)({ type: update_setting_dto_1.UpdateSettingDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Setting updated/created successfully',
        type: setting_dto_1.SettingDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Requires Admin Role)' }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_setting_dto_1.UpdateSettingDto]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "updateSetting", null);
exports.AdminSettingsController = AdminSettingsController = AdminSettingsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Settings'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __metadata("design:paramtypes", [settings_service_1.SettingsService])
], AdminSettingsController);
//# sourceMappingURL=admin-settings.controller.js.map