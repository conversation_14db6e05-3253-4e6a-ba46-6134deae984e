"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminCitiesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminCitiesController = void 0;
const common_1 = require("@nestjs/common");
const cities_service_1 = require("./cities.service");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const city_dto_1 = require("./dto/city.dto");
const create_city_dto_1 = require("./dto/create-city.dto");
const update_city_dto_1 = require("./dto/update-city.dto");
const swagger_1 = require("@nestjs/swagger");
let AdminCitiesController = AdminCitiesController_1 = class AdminCitiesController {
    citiesService;
    logger = new common_1.Logger(AdminCitiesController_1.name);
    constructor(citiesService) {
        this.citiesService = citiesService;
    }
    async createCity(createCityDto) {
        this.logger.log(`Admin request to create city: ${createCityDto.name}`);
        return await this.citiesService.createCity(createCityDto);
    }
    async updateCity(id, updateCityDto) {
        this.logger.log(`Admin request to update city ID: ${id}`);
        return await this.citiesService.updateCity(id, updateCityDto);
    }
    async deleteCity(id) {
        this.logger.log(`Admin request to delete city ID: ${id}`);
        await this.citiesService.deleteCity(id);
    }
};
exports.AdminCitiesController = AdminCitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new city' }),
    (0, swagger_1.ApiBody)({ type: create_city_dto_1.CreateCityDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'City created successfully',
        type: city_dto_1.CityDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Requires Admin Role)' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_city_dto_1.CreateCityDto]),
    __metadata("design:returntype", Promise)
], AdminCitiesController.prototype, "createCity", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an existing city' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: 'string',
        format: 'uuid',
        description: 'City ID',
    }),
    (0, swagger_1.ApiBody)({ type: update_city_dto_1.UpdateCityDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'City updated successfully',
        type: city_dto_1.CityDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'City not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Requires Admin Role)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_city_dto_1.UpdateCityDto]),
    __metadata("design:returntype", Promise)
], AdminCitiesController.prototype, "updateCity", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a city' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: 'string',
        format: 'uuid',
        description: 'City ID',
    }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'City deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'City not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Requires Admin Role)' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminCitiesController.prototype, "deleteCity", null);
exports.AdminCitiesController = AdminCitiesController = AdminCitiesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Cities'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/cities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __metadata("design:paramtypes", [cities_service_1.CitiesService])
], AdminCitiesController);
//# sourceMappingURL=admin-cities.controller.js.map