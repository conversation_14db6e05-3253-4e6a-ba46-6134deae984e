import { TemplatesService } from './templates.service';
import { User } from '@supabase/supabase-js';
import { TemplateSummaryDto, PaginatedAdminTemplatesResponse, TemplateDetailDto } from './dto/template-summary.dto';
import { CreateTemplateFromCalculationDto } from './dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from './dto/create-template.dto';
import { TemplateCalculationResultDto, TemplateCalculationSummaryDto } from './dto/template-calculation.dto';
import { ListAdminTemplatesQueryDto } from './dto/list-admin-templates.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { UpdateTemplateStatusDto } from './dto/update-template-status.dto';
export declare class AdminTemplatesController {
    private readonly templatesService;
    private readonly logger;
    constructor(templatesService: TemplatesService);
    createTemplate(createDto: CreateTemplateDto, user: User): Promise<TemplateSummaryDto>;
    createTemplateFromCalculation(createDto: CreateTemplateFromCalculationDto, user: User): Promise<TemplateSummaryDto>;
    findAllAdmin(queryDto: ListAdminTemplatesQueryDto): Promise<PaginatedAdminTemplatesResponse>;
    findOneAdmin(id: string): Promise<TemplateDetailDto>;
    updateTemplate(id: string, updateDto: UpdateTemplateDto): Promise<TemplateDetailDto>;
    updateTemplateStatus(id: string, updateStatusDto: UpdateTemplateStatusDto): Promise<TemplateDetailDto>;
    deleteTemplate(id: string): Promise<void>;
    calculateTemplate(id: string): Promise<TemplateCalculationResultDto>;
    getTemplateCalculationSummary(id: string): Promise<TemplateCalculationSummaryDto>;
}
