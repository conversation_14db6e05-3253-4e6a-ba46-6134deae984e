import { Response } from 'express';
import { StorageService } from './storage.service';
import { User } from '@supabase/supabase-js';
import { SupabaseService } from '../supabase/supabase.service';
export declare class StorageController {
    private readonly storageService;
    private readonly supabaseService;
    private readonly logger;
    constructor(storageService: StorageService, supabaseService: SupabaseService);
    getFileMetadata(bucket: string, filePath: string, user: User): Promise<any>;
    listFiles(bucket: string, path: string | undefined, user: User): Promise<any[]>;
    downloadFile(bucket: string, filePath: string, download: boolean, user: User, res: Response): Promise<void>;
    private checkFileAccess;
}
