"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportManagementDataDto = exports.ExportManagementMetadataDto = exports.ExportFilterInfoDto = exports.ExportAvailableFiltersDto = exports.RecentExportActivityDto = exports.ExportStatisticsDto = exports.CalculationInfoDto = exports.PaginatedExportsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const export_management_filters_dto_1 = require("./export-management-filters.dto");
const export_format_enum_1 = require("../enums/export-format.enum");
class PaginatedExportsDto {
    data;
    totalCount;
    page;
    pageSize;
    totalPages;
}
exports.PaginatedExportsDto = PaginatedExportsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of exports',
        type: [Object],
    }),
    __metadata("design:type", Array)
], PaginatedExportsDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of exports',
        example: 50,
    }),
    __metadata("design:type", Number)
], PaginatedExportsDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedExportsDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedExportsDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 5,
    }),
    __metadata("design:type", Number)
], PaginatedExportsDto.prototype, "totalPages", void 0);
class CalculationInfoDto {
    id;
    name;
    status;
    created_at;
}
exports.CalculationInfoDto = CalculationInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CalculationInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation name',
        example: 'Corporate Event - Jakarta',
    }),
    __metadata("design:type", String)
], CalculationInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation status',
        example: 'finalized',
    }),
    __metadata("design:type", String)
], CalculationInfoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], CalculationInfoDto.prototype, "created_at", void 0);
class ExportStatisticsDto {
    totalExports;
    completedExports;
    failedExports;
    pendingExports;
    exportsByFormat;
    exportsByStatus;
}
exports.ExportStatisticsDto = ExportStatisticsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of exports',
        example: 50,
    }),
    __metadata("design:type", Number)
], ExportStatisticsDto.prototype, "totalExports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of completed exports',
        example: 45,
    }),
    __metadata("design:type", Number)
], ExportStatisticsDto.prototype, "completedExports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of failed exports',
        example: 3,
    }),
    __metadata("design:type", Number)
], ExportStatisticsDto.prototype, "failedExports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of pending exports',
        example: 2,
    }),
    __metadata("design:type", Number)
], ExportStatisticsDto.prototype, "pendingExports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Exports grouped by format',
        type: Object,
        example: { pdf: 30, xlsx: 15, csv: 5 },
    }),
    __metadata("design:type", Object)
], ExportStatisticsDto.prototype, "exportsByFormat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Exports grouped by status',
        type: Object,
        example: { completed: 45, failed: 3, pending: 2 },
    }),
    __metadata("design:type", Object)
], ExportStatisticsDto.prototype, "exportsByStatus", void 0);
class RecentExportActivityDto {
    id;
    calculationId;
    calculationName;
    format;
    status;
    timestamp;
}
exports.RecentExportActivityDto = RecentExportActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "calculationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation name',
        example: 'Corporate Event - Jakarta',
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "calculationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export format',
        enum: export_format_enum_1.ExportFormat,
        example: export_format_enum_1.ExportFormat.PDF,
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export status',
        example: 'completed',
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], RecentExportActivityDto.prototype, "timestamp", void 0);
class ExportAvailableFiltersDto {
    formats;
    statuses;
    calculations;
}
exports.ExportAvailableFiltersDto = ExportAvailableFiltersDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available export formats',
        type: [String],
        enum: export_format_enum_1.ExportFormat,
    }),
    __metadata("design:type", Array)
], ExportAvailableFiltersDto.prototype, "formats", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available export statuses',
        type: [String],
        example: ['pending', 'processing', 'completed', 'failed'],
    }),
    __metadata("design:type", Array)
], ExportAvailableFiltersDto.prototype, "statuses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available calculations for export',
        type: [Object],
    }),
    __metadata("design:type", Array)
], ExportAvailableFiltersDto.prototype, "calculations", void 0);
class ExportFilterInfoDto {
    applied;
    available;
}
exports.ExportFilterInfoDto = ExportFilterInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Applied filters',
        type: export_management_filters_dto_1.ExportManagementFiltersDto,
    }),
    __metadata("design:type", export_management_filters_dto_1.ExportManagementFiltersDto)
], ExportFilterInfoDto.prototype, "applied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available filter options',
        type: ExportAvailableFiltersDto,
    }),
    __metadata("design:type", ExportAvailableFiltersDto)
], ExportFilterInfoDto.prototype, "available", void 0);
class ExportManagementMetadataDto {
    loadTime;
    cacheVersion;
    userId;
    errors;
    timestamp;
    totalExports;
    appliedFilters;
}
exports.ExportManagementMetadataDto = ExportManagementMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time taken to load the data in milliseconds',
        example: 320,
    }),
    __metadata("design:type", Number)
], ExportManagementMetadataDto.prototype, "loadTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache version for the response',
        example: '1.0',
    }),
    __metadata("design:type", String)
], ExportManagementMetadataDto.prototype, "cacheVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who requested the data',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], ExportManagementMetadataDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Any errors encountered during data loading',
        type: [String],
        example: [],
    }),
    __metadata("design:type", Array)
], ExportManagementMetadataDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the data was loaded',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], ExportManagementMetadataDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of exports',
        example: 50,
    }),
    __metadata("design:type", Number)
], ExportManagementMetadataDto.prototype, "totalExports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of filters applied',
        example: 2,
    }),
    __metadata("design:type", Number)
], ExportManagementMetadataDto.prototype, "appliedFilters", void 0);
class ExportManagementDataDto {
    exports;
    calculations;
    statistics;
    recentActivity;
    supportedFormats;
    filters;
    metadata;
}
exports.ExportManagementDataDto = ExportManagementDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Paginated export results',
        type: PaginatedExportsDto,
    }),
    __metadata("design:type", PaginatedExportsDto)
], ExportManagementDataDto.prototype, "exports", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available calculations for export',
        type: [CalculationInfoDto],
    }),
    __metadata("design:type", Array)
], ExportManagementDataDto.prototype, "calculations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Export statistics',
        type: ExportStatisticsDto,
    }),
    __metadata("design:type", ExportStatisticsDto)
], ExportManagementDataDto.prototype, "statistics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recent export activity',
        type: [RecentExportActivityDto],
    }),
    __metadata("design:type", Array)
], ExportManagementDataDto.prototype, "recentActivity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Supported export formats',
        type: [String],
        enum: export_format_enum_1.ExportFormat,
    }),
    __metadata("design:type", Array)
], ExportManagementDataDto.prototype, "supportedFormats", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter information',
        type: ExportFilterInfoDto,
    }),
    __metadata("design:type", ExportFilterInfoDto)
], ExportManagementDataDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata about the response',
        type: ExportManagementMetadataDto,
    }),
    __metadata("design:type", ExportManagementMetadataDto)
], ExportManagementDataDto.prototype, "metadata", void 0);
//# sourceMappingURL=export-management-data.dto.js.map