"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ExportsProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportsProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const bullmq_2 = require("bullmq");
const common_1 = require("@nestjs/common");
const fs = require("fs");
const path = require("path");
const os = require("os");
const calculations_service_1 = require("../calculations/calculations.service");
const export_format_enum_1 = require("./enums/export-format.enum");
const exports_service_1 = require("./exports.service");
const export_generation_service_1 = require("./services/export-generation.service");
const export_storage_service_1 = require("./services/export-storage.service");
const export_status_enum_1 = require("./enums/export-status.enum");
let ExportsProcessor = ExportsProcessor_1 = class ExportsProcessor extends bullmq_1.WorkerHost {
    calculationsService;
    exportsService;
    generationService;
    storageService;
    logger = new common_1.Logger(ExportsProcessor_1.name);
    constructor(calculationsService, exportsService, generationService, storageService) {
        super();
        this.calculationsService = calculationsService;
        this.exportsService = exportsService;
        this.generationService = generationService;
        this.storageService = storageService;
    }
    async process(job) {
        const { exportHistoryId, calculationId, format, userId } = job.data;
        this.logger.log(`Processing job ${job.id} for export history ${exportHistoryId} (Format: ${format})`);
        let tempFilePath = undefined;
        let generatedFileName = '';
        let storagePath = undefined;
        try {
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.PROCESSING);
            const calculationData = await this.calculationsService.findCalculationForExport(calculationId, userId);
            if (!calculationData) {
                throw new common_1.InternalServerErrorException(`Calculation data not found for ID: ${calculationId}`);
            }
            const transformedData = this.generationService.transformCalculationData(calculationData);
            const sanitizedCalcName = calculationData.name
                .replace(/[^a-z0-9]/gi, '_')
                .toLowerCase();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            let fileBuffer = undefined;
            let mimeType = '';
            if (format === export_format_enum_1.ExportFormat.XLSX) {
                generatedFileName = `${sanitizedCalcName}_${timestamp}.xlsx`;
                mimeType =
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                fileBuffer =
                    await this.generationService.generateXlsxBuffer(transformedData);
            }
            else if (format === export_format_enum_1.ExportFormat.PDF) {
                generatedFileName = `${sanitizedCalcName}_${timestamp}.pdf`;
                mimeType = 'application/pdf';
                tempFilePath = path.join(os.tmpdir(), generatedFileName);
                await this.generationService.generatePdfToFile(transformedData, tempFilePath);
                fileBuffer = fs.readFileSync(tempFilePath);
            }
            else if (format === export_format_enum_1.ExportFormat.CSV) {
                generatedFileName = `${sanitizedCalcName}_${timestamp}.csv`;
                mimeType = 'text/csv';
                fileBuffer =
                    await this.generationService.generateCsvBuffer(transformedData);
            }
            else {
                throw new Error(`Unsupported export format: ${format}`);
            }
            if (!fileBuffer) {
                throw new common_1.InternalServerErrorException('File buffer not generated.');
            }
            this.logger.log(`Generated file buffer for ${generatedFileName}`);
            storagePath = await this.storageService.uploadExportFile(userId, generatedFileName, fileBuffer, mimeType);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.COMPLETED, {
                storagePath: storagePath,
                fileName: generatedFileName,
                fileSize: fileBuffer.length,
                mimeType: mimeType,
            });
            this.logger.log(`Job ${job.id} completed successfully for export history ${exportHistoryId}`);
        }
        catch (error) {
            this.logger.error(`Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.FAILED, {
                error: error instanceof Error ? error.message : String(error),
                fileName: generatedFileName || undefined,
                storagePath: storagePath || undefined,
            });
            throw error;
        }
        finally {
            if (tempFilePath) {
                try {
                    fs.unlinkSync(tempFilePath);
                    this.logger.log(`Temporary file deleted: ${tempFilePath}`);
                }
                catch (unlinkErr) {
                    this.logger.warn(`Could not delete temporary file ${tempFilePath}: ${unlinkErr instanceof Error ? unlinkErr.message : String(unlinkErr)}`);
                }
            }
        }
    }
    onCompleted(job, result) {
        this.logger.log(`Job ${job.id} has completed! Result: ${JSON.stringify(result)}`);
    }
    onFailed(job, err) {
        this.logger.error(`Job ${job.id} has failed! Error: ${err.message}`, err.stack);
    }
    onActive(job) {
        this.logger.log(`Job ${job.id} has started`);
    }
};
exports.ExportsProcessor = ExportsProcessor;
__decorate([
    (0, bullmq_1.OnWorkerEvent)('completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Object]),
    __metadata("design:returntype", void 0)
], ExportsProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Error]),
    __metadata("design:returntype", void 0)
], ExportsProcessor.prototype, "onFailed", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job]),
    __metadata("design:returntype", void 0)
], ExportsProcessor.prototype, "onActive", null);
exports.ExportsProcessor = ExportsProcessor = ExportsProcessor_1 = __decorate([
    (0, bullmq_1.Processor)('exportQueue'),
    __metadata("design:paramtypes", [calculations_service_1.CalculationsService,
        exports_service_1.ExportsService,
        export_generation_service_1.ExportGenerationService,
        export_storage_service_1.ExportStorageService])
], ExportsProcessor);
//# sourceMappingURL=exports.processor.js.map