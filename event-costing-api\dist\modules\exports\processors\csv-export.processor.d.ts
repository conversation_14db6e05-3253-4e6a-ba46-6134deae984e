import { WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { ExportsService } from '../exports.service';
import { ExportStorageService } from '../services/export-storage.service';
import { ExportGenerationService } from '../services/export-generation.service';
import { CalculationsService } from '../../calculations/calculations.service';
export interface CsvExportJobData {
    exportHistoryId: string;
    calculationId: string;
    userId: string;
}
export declare class CsvExportProcessor extends WorkerHost {
    private readonly exportsService;
    private readonly storageService;
    private readonly generationService;
    private readonly calculationsService;
    private readonly logger;
    constructor(exportsService: ExportsService, storageService: ExportStorageService, generationService: ExportGenerationService, calculationsService: CalculationsService);
    process(job: Job<CsvExportJobData>): Promise<void>;
}
