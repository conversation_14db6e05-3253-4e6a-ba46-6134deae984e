"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ExportsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const calculations_service_1 = require("../calculations/calculations.service");
const bullmq_1 = require("@nestjs/bullmq");
const bullmq_2 = require("bullmq");
const export_status_enum_1 = require("./enums/export-status.enum");
const export_format_enum_1 = require("./enums/export-format.enum");
const export_storage_service_1 = require("./services/export-storage.service");
let ExportsService = ExportsService_1 = class ExportsService {
    supabaseService;
    calculationsService;
    storageService;
    csvExportsQueue;
    pdfExportsQueue;
    xlsxExportsQueue;
    logger = new common_1.Logger(ExportsService_1.name);
    EXPORT_HISTORY_TABLE = 'export_history';
    mapStatusToFrontend(dbStatus) {
        switch (dbStatus.toUpperCase()) {
            case 'PENDING':
                return 'pending';
            case 'PROCESSING':
                return 'processing';
            case 'COMPLETED':
                return 'completed';
            case 'FAILED':
                return 'failed';
            default:
                this.logger.warn(`Unknown export status: ${dbStatus}, defaulting to pending`);
                return 'pending';
        }
    }
    constructor(supabaseService, calculationsService, storageService, csvExportsQueue, pdfExportsQueue, xlsxExportsQueue) {
        this.supabaseService = supabaseService;
        this.calculationsService = calculationsService;
        this.storageService = storageService;
        this.csvExportsQueue = csvExportsQueue;
        this.pdfExportsQueue = pdfExportsQueue;
        this.xlsxExportsQueue = xlsxExportsQueue;
    }
    async initiateExport(createDto, user) {
        const { calculationId, format, recipient } = createDto;
        if (!calculationId || !format) {
            throw new common_1.BadRequestException('Invalid export request: Missing calculationId or format.');
        }
        this.logger.log(`User ${user.id} initiating export for calculation ${calculationId} as ${format}`);
        await this.calculationsService.checkCalculationOwnership(calculationId, user.id);
        const supabase = this.supabaseService.getClient();
        let historyRecord = null;
        try {
            const { data, error: insertError } = await supabase
                .from(this.EXPORT_HISTORY_TABLE)
                .insert({
                calculation_id: calculationId,
                created_by: user.id,
                export_type: format,
                recipient: recipient,
                status: export_status_enum_1.ExportStatus.PENDING,
            })
                .select('id, created_at')
                .single();
            if (insertError)
                throw insertError;
            if (!data)
                throw new Error('History record ID not returned.');
            historyRecord = {
                id: data.id,
                created_at: data.created_at,
                calculation_id: calculationId,
                created_by: user.id,
                export_type: format,
                status: export_status_enum_1.ExportStatus.PENDING,
                recipient: recipient,
            };
            this.logger.log(`Created export history record ${historyRecord.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to create export history record: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw new common_1.InternalServerErrorException('Failed to initiate export process (database error).');
        }
        const baseJobData = {
            calculationId,
            userId: user.id,
            exportHistoryId: historyRecord.id,
        };
        try {
            let queueName;
            let queue;
            switch (format) {
                case export_format_enum_1.ExportFormat.CSV:
                    queue = this.csvExportsQueue;
                    queueName = 'csv-exports';
                    break;
                case export_format_enum_1.ExportFormat.PDF:
                    queue = this.pdfExportsQueue;
                    queueName = 'pdf-exports';
                    break;
                case export_format_enum_1.ExportFormat.XLSX:
                    queue = this.xlsxExportsQueue;
                    queueName = 'xlsx-exports';
                    break;
                default:
                    throw new common_1.BadRequestException(`Unsupported export format: ${format}`);
            }
            await queue.add('process-export', baseJobData, {
                attempts: 3,
                backoff: { type: 'exponential', delay: 5000 },
                removeOnComplete: 1000,
                removeOnFail: 500,
            });
            this.logger.log(`Added ${format} export job to ${queueName} queue for history ID: ${historyRecord.id}`);
        }
        catch (queueError) {
            this.logger.error(`Failed to add job to exports queue for history ${historyRecord.id}: ${queueError instanceof Error ? queueError.message : String(queueError)}`, queueError instanceof Error ? queueError.stack : undefined);
            try {
                await this.updateExportHistoryStatus(historyRecord.id, export_status_enum_1.ExportStatus.FAILED, {
                    error: 'Failed to queue the export job.',
                });
            }
            catch (updateError) {
                this.logger.error(`Failed to update history ${historyRecord.id} to FAILED after queue error: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
            }
            throw new common_1.InternalServerErrorException('Failed to queue export job.');
        }
        return {
            exportId: historyRecord.id,
            status: export_status_enum_1.ExportStatus.PENDING,
            message: `Export process initiated for calculation ${calculationId} as ${format}. Check status later.`,
            createdAt: new Date(historyRecord.created_at),
        };
    }
    async updateExportHistoryStatus(historyId, status, details) {
        this.logger.log(`Updating export history ${historyId} to status ${status}`);
        const supabase = this.supabaseService.getClient();
        const updateData = { status };
        if (status === export_status_enum_1.ExportStatus.COMPLETED || status === export_status_enum_1.ExportStatus.FAILED) {
            updateData.completed_at = new Date().toISOString();
        }
        if (details?.storagePath)
            updateData.storage_path = details.storagePath;
        if (details?.fileName)
            updateData.file_name = details.fileName;
        if (details?.error)
            updateData.error_message = details.error;
        const { error } = await supabase
            .from(this.EXPORT_HISTORY_TABLE)
            .update(updateData)
            .eq('id', historyId);
        if (error) {
            this.logger.error(`Failed to update export history ${historyId} status: ${error.message}`);
        }
    }
    async getExportStatus(exportId, userId) {
        this.logger.log(`User ${userId} checking status for export ${exportId}`);
        await this.checkExportOwnership(exportId, userId);
        const supabase = this.supabaseService.getClient();
        const { data: historyRecord, error: fetchError } = await supabase
            .from(this.EXPORT_HISTORY_TABLE)
            .select('*')
            .eq('id', exportId)
            .single();
        if (fetchError) {
            this.logger.error(`Error fetching export status for ${exportId}: ${fetchError.message}`);
            throw new common_1.InternalServerErrorException('Could not retrieve export status.');
        }
        if (!historyRecord) {
            throw new common_1.NotFoundException(`Export record with ID ${exportId} not found.`);
        }
        const downloadUrl = await this.storageService.getSignedUrl(historyRecord.storage_path);
        const response = {
            exportId: historyRecord.id,
            status: this.mapStatusToFrontend(historyRecord.status),
            format: historyRecord.export_type.toLowerCase(),
            downloadUrl: downloadUrl ?? undefined,
            fileName: historyRecord.file_name ?? undefined,
            errorMessage: historyRecord.error_message ?? undefined,
            createdAt: new Date(historyRecord.created_at),
            completedAt: historyRecord.completed_at
                ? new Date(historyRecord.completed_at)
                : undefined,
        };
        return response;
    }
    async getExportsByCalculation(calculationId, userId) {
        this.logger.log(`User ${userId} fetching exports for calculation ${calculationId}`);
        await this.calculationsService.checkCalculationOwnership(calculationId, userId);
        const supabase = this.supabaseService.getClient();
        const { data: historyRecords, error: fetchError } = await supabase
            .from(this.EXPORT_HISTORY_TABLE)
            .select('*')
            .eq('calculation_id', calculationId)
            .eq('created_by', userId)
            .order('created_at', { ascending: false });
        if (fetchError) {
            this.logger.error(`Error fetching exports for calculation ${calculationId}: ${fetchError.message}`);
            throw new common_1.InternalServerErrorException('Could not retrieve export history.');
        }
        const responses = [];
        for (const record of historyRecords || []) {
            const downloadUrl = record.storage_path
                ? await this.storageService.getSignedUrl(record.storage_path)
                : undefined;
            responses.push({
                exportId: record.id,
                status: this.mapStatusToFrontend(record.status),
                format: record.export_type.toLowerCase(),
                downloadUrl: downloadUrl ?? undefined,
                fileName: record.file_name ?? undefined,
                errorMessage: record.error_message ?? undefined,
                createdAt: new Date(record.created_at),
                completedAt: record.completed_at
                    ? new Date(record.completed_at)
                    : undefined,
            });
        }
        return responses;
    }
    async checkExportOwnership(exportId, userId) {
        const { data: historyRecord, error } = await this.supabaseService
            .getClient()
            .from(this.EXPORT_HISTORY_TABLE)
            .select('id, created_by')
            .eq('id', exportId)
            .single();
        if (error) {
            this.logger.error(`Error fetching export history ${exportId} for ownership check: ${error.message}`);
            throw new common_1.InternalServerErrorException('Error checking export history ownership.');
        }
        if (!historyRecord) {
            throw new common_1.NotFoundException(`Export record with ID ${exportId} not found.`);
        }
        if (historyRecord.created_by !== userId) {
            this.logger.warn(`User ${userId} attempted to access export ${exportId} owned by ${historyRecord.created_by}`);
            throw new common_1.ForbiddenException('You do not have permission to access this export record.');
        }
    }
};
exports.ExportsService = ExportsService;
exports.ExportsService = ExportsService = ExportsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, bullmq_1.InjectQueue)('csv-exports')),
    __param(4, (0, bullmq_1.InjectQueue)('pdf-exports')),
    __param(5, (0, bullmq_1.InjectQueue)('xlsx-exports')),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        calculations_service_1.CalculationsService,
        export_storage_service_1.ExportStorageService,
        bullmq_2.Queue,
        bullmq_2.Queue,
        bullmq_2.Queue])
], ExportsService);
//# sourceMappingURL=exports.service.js.map