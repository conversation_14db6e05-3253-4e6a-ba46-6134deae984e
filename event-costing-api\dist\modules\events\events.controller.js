"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EventsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsController = void 0;
const common_1 = require("@nestjs/common");
const events_service_1 = require("./events.service");
const event_dto_1 = require("./dto/event.dto");
const create_event_dto_1 = require("./dto/create-event.dto");
const update_event_dto_1 = require("./dto/update-event.dto");
const event_status_enum_1 = require("./dto/event-status.enum");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
let EventsController = EventsController_1 = class EventsController {
    eventsService;
    logger = new common_1.Logger(EventsController_1.name);
    constructor(eventsService) {
        this.eventsService = eventsService;
    }
    async create(createEventDto) {
        this.logger.log(`Received request to create event: ${createEventDto.event_name}`);
        return this.eventsService.create(createEventDto);
    }
    async findAll(search, status, clientId, contactId) {
        this.logger.log(`Received request to list events ${search ? `with search: '${search}'` : ''} ${status ? `status: ${status}` : ''} ${clientId ? `clientId: ${clientId}` : ''} ${contactId ? `contactId: ${contactId}` : ''}`);
        return this.eventsService.findAll(search, status, clientId, contactId);
    }
    async findOne(id) {
        this.logger.log(`Received request to get event ID: ${id}`);
        return this.eventsService.findOne(id);
    }
    async update(id, updateEventDto) {
        this.logger.log(`Received request to update event ID: ${id}`);
        return this.eventsService.update(id, updateEventDto);
    }
    async remove(id) {
        this.logger.log(`Received request to delete event ID: ${id}`);
        await this.eventsService.remove(id);
    }
};
exports.EventsController = EventsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new event' }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Event created successfully.',
        type: event_dto_1.EventDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad Request (Validation Error)' }),
    (0, swagger_1.ApiNotFoundResponse)({
        description: 'Referenced client or contact not found',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_event_dto_1.CreateEventDto]),
    __metadata("design:returntype", Promise)
], EventsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all events with optional filtering' }),
    (0, swagger_1.ApiOkResponse)({ description: 'List of events.', type: [event_dto_1.EventDto] }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for event name or venue details',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        enum: event_status_enum_1.EventStatus,
        description: 'Filter by event status',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clientId',
        required: false,
        type: String,
        format: 'uuid',
        description: 'Filter by associated client ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'contactId',
        required: false,
        type: String,
        format: 'uuid',
        description: 'Filter by primary contact user ID',
    }),
    __param(0, (0, common_1.Query)('search')),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('clientId')),
    __param(3, (0, common_1.Query)('contactId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], EventsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific event by ID' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Event details.', type: event_dto_1.EventDto }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Event not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an event' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Event updated successfully.', type: event_dto_1.EventDto }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Event, client, or contact not found.' }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad Request (Validation Error)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_event_dto_1.UpdateEventDto]),
    __metadata("design:returntype", Promise)
], EventsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete (soft) an event' }),
    (0, swagger_1.ApiNoContentResponse)({ description: 'Event deleted successfully.' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Event not found or already deleted.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventsController.prototype, "remove", null);
exports.EventsController = EventsController = EventsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Events'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('events'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [events_service_1.EventsService])
], EventsController);
//# sourceMappingURL=events.controller.js.map