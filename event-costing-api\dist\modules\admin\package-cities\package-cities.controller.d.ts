import { PackageCitiesService } from './package-cities.service';
import { PackageCityDto } from './dto/package-city.dto';
import { AddPackageCityDto } from './dto/add-package-city.dto';
export declare class PackageCitiesController {
    private readonly packageCitiesService;
    private readonly logger;
    constructor(packageCitiesService: PackageCitiesService);
    addCity(packageId: string, addDto: AddPackageCityDto): Promise<{
        id: string;
    }>;
    listCities(packageId: string): Promise<PackageCityDto[]>;
    removeCity(packageId: string, cityId: string): Promise<void>;
}
