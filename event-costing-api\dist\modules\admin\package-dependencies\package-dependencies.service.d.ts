import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackageDependencyDto } from './dto/create-package-dependency.dto';
import { PackageDependencyDto } from './dto/package-dependency.dto';
export declare class PackageDependenciesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    private handlePostgrestError;
    create(packageId: string, createDto: CreatePackageDependencyDto): Promise<PackageDependencyDto>;
    findAllByPackage(packageId: string): Promise<PackageDependencyDto[]>;
    remove(dependencyId: string): Promise<void>;
}
