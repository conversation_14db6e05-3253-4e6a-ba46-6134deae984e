import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { TemplateDetailService } from './template-detail.service';
import { TemplateCalculationService } from './template-calculation.service';
import { TemplateQueryService } from './template-query.service';
import { PackagesService } from '../../packages/packages.service';
import { CategoriesService } from '../../categories/categories.service';
import { TemplateCompleteDataDto } from '../dto/template-complete-data.dto';
import { TemplateFiltersDto } from '../dto/template-filters.dto';
export declare class TemplateConsolidatedService {
    private readonly supabaseService;
    private readonly templateDetailService;
    private readonly templateCalculationService;
    private readonly templateQueryService;
    private readonly packagesService;
    private readonly categoriesService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateDetailService: TemplateDetailService, templateCalculationService: TemplateCalculationService, templateQueryService: TemplateQueryService, packagesService: PackagesService, categoriesService: CategoriesService);
    getTemplateManagementData(filters: TemplateFiltersDto | undefined, user: User): Promise<TemplateCompleteDataDto>;
    getTemplateDetailData(templateId: string, user: User): Promise<any>;
    private getTemplates;
    private getTemplateById;
    private getTemplatePackages;
    private getTemplateCalculation;
    private getCategories;
    private getEventTypes;
    private getAvailablePackages;
    private getTemplateSummary;
    private extractResult;
    private collectErrors;
}
