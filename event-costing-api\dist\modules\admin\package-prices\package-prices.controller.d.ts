import { PackagePricesService } from './package-prices.service';
import { CreatePackagePriceDto } from './dto/create-package-price.dto';
import { UpdatePackagePriceDto } from './dto/update-package-price.dto';
import { PackagePriceDto } from './dto/package-price.dto';
export declare class PackagePricesController {
    private readonly packagePricesService;
    private readonly logger;
    constructor(packagePricesService: PackagePricesService);
    create(packageId: string, createPackagePriceDto: CreatePackagePriceDto): Promise<PackagePriceDto>;
    findAllByPackage(packageId: string): Promise<PackagePriceDto[]>;
    findOne(packageId: string, packagePriceId: string): Promise<PackagePriceDto>;
    update(packageId: string, packagePriceId: string, updatePackagePriceDto: UpdatePackagePriceDto): Promise<PackagePriceDto>;
    remove(packageId: string, packagePriceId: string): Promise<void>;
}
