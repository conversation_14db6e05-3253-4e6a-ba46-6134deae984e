import { CalculationsService } from './calculations.service';
import { PackagesService } from '../packages/packages.service';
import { User } from '@supabase/supabase-js';
import { PackagesByCategoryResponseDto } from '../packages/dto/packages-by-category-response.dto';
export declare class CalculationsPackagesController {
    private readonly calculationsService;
    private readonly packagesService;
    private readonly logger;
    constructor(calculationsService: CalculationsService, packagesService: PackagesService);
    getAvailablePackagesByCategory(calculationId: string, includeOptions: boolean | undefined, user: User): Promise<PackagesByCategoryResponseDto>;
}
