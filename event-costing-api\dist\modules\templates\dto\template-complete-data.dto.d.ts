import { CategoryDto } from '../../categories/dto/category.dto';
import { TemplateFiltersDto } from './template-filters.dto';
export declare class PaginatedTemplatesDto {
    data: any[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
export declare class EventTypeInfoDto {
    id: string;
    name: string;
    description?: string;
}
export declare class TemplatePackageDto {
    package_id: string;
    name: string;
    description: string | null;
    category_id: string;
    price: number;
    quantity_basis: string;
    is_available_in_city: boolean;
    is_available_in_venue: boolean;
}
export declare class TemplateStatsDto {
    totalTemplates: number;
    activeTemplates: number;
    inactiveTemplates: number;
    publicTemplates: number;
    privateTemplates: number;
}
export declare class TemplateAvailableFiltersDto {
    categories: Array<{
        id: string;
        name: string;
    }>;
    eventTypes: Array<{
        id: string;
        name: string;
    }>;
}
export declare class TemplateFilterInfoDto {
    applied: TemplateFiltersDto;
    available: TemplateAvailableFiltersDto;
}
export declare class TemplateManagementMetadataDto {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalTemplates: number;
    appliedFilters: number;
}
export declare class TemplateCompleteDataDto {
    templates: PaginatedTemplatesDto;
    categories: CategoryDto[];
    eventTypes: EventTypeInfoDto[];
    packages: TemplatePackageDto[];
    summary?: TemplateStatsDto;
    filters: TemplateFilterInfoDto;
    metadata: TemplateManagementMetadataDto;
}
