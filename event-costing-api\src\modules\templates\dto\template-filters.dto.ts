import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsNumber, IsArray, IsEnum, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Sort order enum
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Sort by options enum for templates
 */
export enum TemplateSortBy {
  NAME = 'name',
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
  EVENT_TYPE = 'event_type',
  ATTENDEES = 'attendees',
  STATUS = 'is_active',
  PUBLIC = 'is_public',
}

/**
 * Template status filter enum
 */
export enum TemplateStatus {
  ALL = 'all',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Template visibility filter enum
 */
export enum TemplateVisibility {
  ALL = 'all',
  PUBLIC = 'public',
  PRIVATE = 'private',
}

/**
 * DTO for template filtering and pagination
 */
export class TemplateFiltersDto {
  @ApiPropertyOptional({
    description: 'Search term for template name or description',
    example: 'corporate event',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by event type',
    example: 'corporate',
  })
  @IsOptional()
  @IsString()
  eventType?: string;

  @ApiPropertyOptional({
    description: 'Filter by category ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by city ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by venue IDs',
    type: [String],
    example: ['venue-1', 'venue-2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  venueIds?: string[];

  @ApiPropertyOptional({
    description: 'Minimum attendees filter',
    example: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  minAttendees?: number;

  @ApiPropertyOptional({
    description: 'Maximum attendees filter',
    example: 500,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  maxAttendees?: number;

  @ApiPropertyOptional({
    description: 'Filter by template status',
    enum: TemplateStatus,
    example: TemplateStatus.ACTIVE,
    default: TemplateStatus.ALL,
  })
  @IsOptional()
  @IsEnum(TemplateStatus)
  status?: TemplateStatus;

  @ApiPropertyOptional({
    description: 'Filter by template visibility',
    enum: TemplateVisibility,
    example: TemplateVisibility.PUBLIC,
    default: TemplateVisibility.ALL,
  })
  @IsOptional()
  @IsEnum(TemplateVisibility)
  visibility?: TemplateVisibility;

  @ApiPropertyOptional({
    description: 'Filter by template creator (user ID)',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Include only templates created by current user',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  myTemplatesOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Include templates with packages from specific category',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  hasPackagesFromCategory?: string;

  @ApiPropertyOptional({
    description: 'Include only templates with calculations',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  hasCalculations?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by date range - start date',
    type: String,
    format: 'date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsString()
  createdAfter?: string;

  @ApiPropertyOptional({
    description: 'Filter by date range - end date',
    type: String,
    format: 'date',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsString()
  createdBefore?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  pageSize?: number;

  @ApiPropertyOptional({
    description: 'Sort by field',
    enum: TemplateSortBy,
    example: TemplateSortBy.NAME,
    default: TemplateSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(TemplateSortBy)
  sortBy?: TemplateSortBy;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: SortOrder,
    example: SortOrder.DESC,
    default: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder;

  @ApiPropertyOptional({
    description: 'Include template packages in response',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includePackages?: boolean;

  @ApiPropertyOptional({
    description: 'Include template calculations in response',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeCalculations?: boolean;

  @ApiPropertyOptional({
    description: 'Include template statistics in response',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  includeStatistics?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by tags',
    type: [String],
    example: ['premium', 'outdoor'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Exclude template with specific ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  excludeId?: string;
}
