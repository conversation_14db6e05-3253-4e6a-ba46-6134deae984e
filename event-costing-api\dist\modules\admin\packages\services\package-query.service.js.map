{"version": 3, "file": "package-query.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/admin/packages/services/package-query.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAKwB;AACxB,iFAAqE;AAM9D,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAGD;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAE/D,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAOjE,KAAK,CAAC,eAAe,CACnB,QAA6B;QAE7B,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,oCAAoC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAC/D,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;QAGpE,MAAM,YAAY,GAAG,QAAQ;aAC1B,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;QAGnC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;QAGpC,YAAY,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,KAAK,MAAM,CAAC,CAAC;QAEpD,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,YAAY,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAC7C,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,IAAI,MAAM,CAAC;QACpC,MAAM,aAAa,GAAG,SAAS,IAAI,KAAK,CAAC;QACzC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,aAAa,KAAK,KAAK,EAAE,CAAC,CAAC;QAGvE,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,2CAA2C,KAAK,aAAa,MAAM,WAAW,UAAU,IAAI,aAAa,EAAE,CAC5G,CAAC;QAEF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,YAAY,CAAC;QAElD,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CAAC,8BAA8B,CAAC,CAAC;QACzE,CAAC;QAED,OAAO;YACL,IAAI,EAAE,IAAI,IAAI,EAAE;YAChB,KAAK,EAAE,KAAK,IAAI,CAAC;YACjB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;SACf,CAAC;IACJ,CAAC;IAOD,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;aACZ,WAAW,EAAE,CAAC;QAEjB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,EACxD,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAC5C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;YACrD,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,IAAkB,CAAC;IAC5B,CAAC;IAOD,aAAa,CAAC,QAAe;QAC3B,OAAO,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAOD,KAAK,CAAC,kBAAkB,CAAC,WAAqB;QAC5C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;aACpE,IAAI,CAAC,YAAY,CAAC;aAClB,MAAM,CAAC,UAAU,CAAC;aAClB,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEzB,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,eAAe,CAAC,OAAO,EAAE,CACxD,CAAC;YACF,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,GAAG,CACZ,cAAc,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CACpE,CAAC;IACJ,CAAC;IAOD,KAAK,CAAC,iBAAiB,CAAC,WAAqB;QAC3C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,QAAQ;aAClE,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,UAAU,CAAC;aAClB,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEzB,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6BAA6B,cAAc,CAAC,OAAO,EAAE,CACtD,CAAC;YACF,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,GAAG,CACZ,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CACnE,CAAC;IACJ,CAAC;IAOD,KAAK,CAAC,qBAAqB,CAAC,UAAoB;QAC9C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE9C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC5D,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,gDAAgD,CAAC;aACxD,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAEhC,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,WAAW,CAAC,OAAO,EAAE,CACxD,CAAC;YACF,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAGD,MAAM,WAAW,GAAG;YAClB,GAAG,IAAI,GAAG,CACR,UAAU;iBACP,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC;iBAClC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CACnC;SACF,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAG/D,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACzB,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE;gBAC7B,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,IAAI;gBACtC,YAAY,EAAE,KAAK,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,IAAI;gBACtD,cAAc,EAAE,KAAK,CAAC,WAAW;oBAC/B,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI;oBAC5C,CAAC,CAAC,IAAI;gBACR,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAOO,KAAK,CAAC,kBAAkB,CAAC,WAAqB;QACpD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;aACpE,IAAI,CAAC,YAAY,CAAC;aAClB,MAAM,CAAC,UAAU,CAAC;aAClB,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEzB,IAAI,eAAe,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,eAAe,CAAC,OAAO,EAAE,CACxD,CAAC;YACF,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,GAAG,CACZ,cAAc,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CACpE,CAAC;IACJ,CAAC;IAOD,KAAK,CAAC,qBAAqB,CAAC,UAAoB;QAC9C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE9C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,MAAM,QAAQ;aAC1E,IAAI,CAAC,gBAAgB,CAAC;aACtB,MAAM,CAAC,qBAAqB,CAAC;aAC7B,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAEhC,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,kBAAkB,CAAC,OAAO,EAAE,CAC/D,CAAC;YACF,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAGD,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,GAAG,CACR,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CACjE;SACF,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAGnD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoB,CAAC;QACnD,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACxC,CAAC;YACD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,QAAQ,EAAE,CAAC;gBACb,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IACxB,CAAC;IAOO,KAAK,CAAC,cAAc,CAAC,OAAiB;QAC5C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,GAAG,EAAE,CAAC;QAE3C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC5D,IAAI,CAAC,QAAQ,CAAC;aACd,MAAM,CAAC,UAAU,CAAC;aAClB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAErB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,GAAG,CACZ,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CACpD,CAAC;IACJ,CAAC;CACF,CAAA;AAzTY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,mBAAmB,CAyT/B"}