"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOptionsModule = void 0;
const common_1 = require("@nestjs/common");
const auth_module_1 = require("../../auth/auth.module");
const admin_module_1 = require("../../auth/admin.module");
const package_options_controller_js_1 = require("./package-options.controller.js");
const package_options_service_js_1 = require("./package-options.service.js");
let PackageOptionsModule = class PackageOptionsModule {
};
exports.PackageOptionsModule = PackageOptionsModule;
exports.PackageOptionsModule = PackageOptionsModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule, admin_module_1.AdminModule],
        controllers: [package_options_controller_js_1.PackageOptionsController],
        providers: [package_options_service_js_1.PackageOptionsService],
        exports: [package_options_service_js_1.PackageOptionsService],
    })
], PackageOptionsModule);
//# sourceMappingURL=package-options.module.js.map