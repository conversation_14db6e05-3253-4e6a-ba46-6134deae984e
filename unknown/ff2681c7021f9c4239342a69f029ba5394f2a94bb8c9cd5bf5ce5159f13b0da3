{"version": 3, "file": "package-catalog.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/packages/services/package-catalog.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAIwB;AACxB,8EAA0E;AAE1E,0DAAsD;AACtD,4EAAwE;AACxE,gEAA4D;AASrD,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIb;IACA;IACA;IACA;IANF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAEjE,YACmB,eAAgC,EAChC,eAAgC,EAChC,iBAAoC,EACpC,aAA4B;QAH5B,oBAAe,GAAf,eAAe,CAAiB;QAChC,oBAAe,GAAf,eAAe,CAAiB;QAChC,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAUJ,KAAK,CAAC,cAAc,CAClB,UAA6B,EAAE,EAC/B,IAAU;QAEV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAEzE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,CACJ,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,gBAAgB,EACjB,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACzB,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,aAAa,EAAE;aACrB,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,UAAU,EAAE;gBAC9D,IAAI,EAAE,EAAE;gBACR,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;YACvE,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;YAE1E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;gBAChC,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,eAAe;gBACf,gBAAgB;aACjB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAsB;gBAChC,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,UAAU;gBACV,OAAO,EAAE;oBACP,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE;wBACT,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;wBACnE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;wBAC9D,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;qBAClE;iBACF;gBACD,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY,EAAE,KAAK;oBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,MAAM;oBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,aAAa,EAAE,QAAQ,CAAC,UAAU;oBAClC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;iBAC5C;aACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,gDAAgD,QAAQ,IAAI,CAC7D,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,IAAI,qCAA4B,CACpC,wDAAwD,CACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,WAAW,CAAC,OAA0B;QAElD,MAAM,QAAQ,GAAG;YACf,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,qBAAqB;YACvD,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAa;YAC7B,SAAS,EAAE,OAAO,CAAC,SAAgB;YACnC,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAC7B,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;SAC7D,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAGnE,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAClC,GAAG,SAAS;gBACZ,EAAE,EAAE,SAAS,CAAC,UAAU;gBACxB,WAAW,EAAE,EAAE;aAChB,CAAC,CAAC;YACH,UAAU,EAAE,MAAM,CAAC,KAAK;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;YACvB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;YAChC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;SAC/D,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC;IAKO,KAAK,CAAC,SAAS;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACtC,CAAC;IAKO,KAAK,CAAC,YAAY;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,gBAAgB,CAAC;aACxB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,KAAK,CAAC,MAAM,CAAC,CAAC;QAEjB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,KAAK,CAAC,aAAa;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,YAAY,CAAC;aAClB,MAAM,CAAC,wBAAwB,CAAC;aAChC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEjB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,aAAa,CACnB,MAA+B,EAC/B,IAAY,EACZ,YAAgB;QAEhB,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mBAAmB,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,EAAE,CACxE,CAAC;QAEF,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,YAAY,CAAC;QACtB,CAAC;QAGD,MAAM,MAAM,CAAC,MAAM,CAAC;IACtB,CAAC;IAKO,aAAa,CAAC,OAAoC;QACxD,OAAO,OAAO;aACX,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC;aAC9C,GAAG,CACF,MAAM,CAAC,EAAE,CACN,MAAgC,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,CACvE,CAAC;IACN,CAAC;IAMD,KAAK,CAAC,kBAAkB,CACtB,OAIC,EACD,IAAU;QAEV,MAAM,EACJ,cAAc,GAAG,KAAK,EACtB,mBAAmB,GAAG,KAAK,EAC3B,mBAAmB,GAAG,KAAK,EAC3B,GAAG,WAAW,EACf,GAAG,OAAO,CAAC;QAGZ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAGjE,IAAI,cAAc,IAAI,mBAAmB,IAAI,mBAAmB,EAAE,CAAC;YACjE,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CACxC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAC,GAAG,EAAC,EAAE;gBACxC,MAAM,YAAY,GAAQ,EAAE,CAAC;gBAE7B,IAAI,cAAc,EAAE,CAAC;oBACnB,YAAY,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACtE,CAAC;gBAED,IAAI,mBAAmB,EAAE,CAAC;oBACxB,YAAY,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC3D,GAAG,CAAC,UAAU,CACf,CAAC;gBACJ,CAAC;gBAED,IAAI,mBAAmB,EAAE,CAAC;oBACxB,YAAY,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC3D,GAAG,CAAC,UAAU,CACf,CAAC;gBACJ,CAAC;gBAED,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;YACrC,CAAC,CAAC,CACH,CAAC;YAEF,WAAW,CAAC,QAAQ,CAAC,IAAI,GAAG,gBAAgB,CAAC;QAC/C,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,iBAAiB,CAAC;aACvB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;aAC3B,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,uCAAuC,SAAS,GAAG,EACnD,KAAK,CACN,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,sBAAsB,CAAC;aAC5B,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAE/B,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,4CAA4C,SAAS,GAAG,EACxD,KAAK,CACN,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,CAAC,YAAY,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;YAC5D,QAAQ;iBACL,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,SAAS,CAAC;iBACjB,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;YAC9B,QAAQ;iBACL,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;SAC/B,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EACJ,YAAY,CAAC,MAAM,KAAK,WAAW;gBACjC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;gBACpD,CAAC,CAAC,EAAE;YACR,MAAM,EACJ,YAAY,CAAC,MAAM,KAAK,WAAW;gBACjC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE;gBACrD,CAAC,CAAC,EAAE;SACT,CAAC;IACJ,CAAC;CACF,CAAA;AA3VY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAKyB,kCAAe;QACf,kCAAe;QACb,sCAAiB;QACrB,8BAAa;GAPpC,qBAAqB,CA2VjC"}