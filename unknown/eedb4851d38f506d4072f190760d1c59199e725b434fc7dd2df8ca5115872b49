"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageCatalogService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCatalogService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const packages_service_1 = require("../packages.service");
const categories_service_1 = require("../../categories/categories.service");
const cities_service_1 = require("../../cities/cities.service");
let PackageCatalogService = PackageCatalogService_1 = class PackageCatalogService {
    supabaseService;
    packagesService;
    categoriesService;
    citiesService;
    logger = new common_1.Logger(PackageCatalogService_1.name);
    constructor(supabaseService, packagesService, categoriesService, citiesService) {
        this.supabaseService = supabaseService;
        this.packagesService = packagesService;
        this.categoriesService = categoriesService;
        this.citiesService = citiesService;
    }
    async getCatalogData(filters = {}, user) {
        this.logger.log(`Fetching package catalog data for user: ${user.email}`);
        const startTime = Date.now();
        try {
            const [packagesResult, categoriesResult, citiesResult, divisionsResult, currenciesResult,] = await Promise.allSettled([
                this.getPackages(filters),
                this.getCategories(),
                this.getCities(),
                this.getDivisions(),
                this.getCurrencies(),
            ]);
            const packages = this.extractResult(packagesResult, 'packages', {
                data: [],
                totalCount: 0,
                page: 1,
                pageSize: 10,
                totalPages: 0,
            });
            const categories = this.extractResult(categoriesResult, 'categories', []);
            const cities = this.extractResult(citiesResult, 'cities', []);
            const divisions = this.extractResult(divisionsResult, 'divisions', []);
            const currencies = this.extractResult(currenciesResult, 'currencies', []);
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                packagesResult,
                categoriesResult,
                citiesResult,
                divisionsResult,
                currenciesResult,
            ]);
            const result = {
                packages,
                categories,
                cities,
                divisions,
                currencies,
                filters: {
                    applied: filters,
                    available: {
                        categories: categories.map(cat => ({ id: cat.id, name: cat.name })),
                        cities: cities.map(city => ({ id: city.id, name: city.name })),
                        divisions: divisions.map(div => ({ id: div.id, name: div.name })),
                    },
                },
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                    totalPackages: packages.totalCount,
                    appliedFilters: Object.keys(filters).length,
                },
            };
            this.logger.log(`Successfully fetched package catalog data in ${loadTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to fetch package catalog data`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load package catalog data. Please try again.');
        }
    }
    async getPackages(filters) {
        const queryDto = {
            categoryId: filters.categoryId,
            cityId: filters.cityId,
            venueIds: filters.venueIds,
            currencyId: filters.currencyId || 'default-currency-id',
            search: filters.search,
            sortBy: filters.sortBy,
            sortOrder: filters.sortOrder,
            limit: filters.pageSize || 10,
            offset: ((filters.page || 1) - 1) * (filters.pageSize || 10),
        };
        const result = await this.packagesService.findVariations(queryDto);
        return {
            data: result.data.map(variation => ({
                ...variation,
                id: variation.package_id,
                division_id: '',
            })),
            totalCount: result.count,
            page: filters.page || 1,
            pageSize: filters.pageSize || 10,
            totalPages: Math.ceil(result.count / (filters.pageSize || 10)),
        };
    }
    async getCategories() {
        return this.categoriesService.findAll();
    }
    async getCities() {
        return this.citiesService.findAll();
    }
    async getDivisions() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('divisions')
            .select('id, name, code')
            .eq('is_deleted', false)
            .order('name');
        if (error) {
            throw error;
        }
        return data || [];
    }
    async getCurrencies() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('currencies')
            .select('id, code, symbol, name')
            .order('code');
        if (error) {
            throw error;
        }
        return data || [];
    }
    extractResult(result, name, defaultValue) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        this.logger.warn(`Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`);
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw result.reason;
    }
    collectErrors(results) {
        return results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason?.message || 'Unknown error');
    }
    async getAdvancedCatalog(filters, user) {
        const { includeOptions = false, includeDependencies = false, includeAvailability = false, ...baseFilters } = filters;
        const catalogData = await this.getCatalogData(baseFilters, user);
        if (includeOptions || includeDependencies || includeAvailability) {
            const enhancedPackages = await Promise.all(catalogData.packages.data.map(async (pkg) => {
                const enhancements = {};
                if (includeOptions) {
                    enhancements.options = await this.getPackageOptions(pkg.package_id);
                }
                if (includeDependencies) {
                    enhancements.dependencies = await this.getPackageDependencies(pkg.package_id);
                }
                if (includeAvailability) {
                    enhancements.availability = await this.getPackageAvailability(pkg.package_id);
                }
                return { ...pkg, ...enhancements };
            }));
            catalogData.packages.data = enhancedPackages;
        }
        return catalogData;
    }
    async getPackageOptions(packageId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('package_options')
            .select('*')
            .eq('package_id', packageId)
            .eq('is_deleted', false);
        if (error) {
            this.logger.warn(`Failed to fetch options for package ${packageId}:`, error);
            return [];
        }
        return data || [];
    }
    async getPackageDependencies(packageId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('package_dependencies')
            .select('*')
            .eq('package_id', packageId);
        if (error) {
            this.logger.warn(`Failed to fetch dependencies for package ${packageId}:`, error);
            return [];
        }
        return data || [];
    }
    async getPackageAvailability(packageId) {
        const supabase = this.supabaseService.getClient();
        const [citiesResult, venuesResult] = await Promise.allSettled([
            supabase
                .from('package_cities')
                .select('city_id')
                .eq('package_id', packageId),
            supabase
                .from('package_venues')
                .select('venue_id')
                .eq('package_id', packageId),
        ]);
        return {
            cities: citiesResult.status === 'fulfilled'
                ? citiesResult.value.data?.map(c => c.city_id) || []
                : [],
            venues: venuesResult.status === 'fulfilled'
                ? venuesResult.value.data?.map(v => v.venue_id) || []
                : [],
        };
    }
};
exports.PackageCatalogService = PackageCatalogService;
exports.PackageCatalogService = PackageCatalogService = PackageCatalogService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        packages_service_1.PackagesService,
        categories_service_1.CategoriesService,
        cities_service_1.CitiesService])
], PackageCatalogService);
//# sourceMappingURL=package-catalog.service.js.map