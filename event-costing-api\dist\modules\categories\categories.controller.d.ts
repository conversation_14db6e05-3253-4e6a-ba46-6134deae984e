import { CategoriesService } from './categories.service';
import { CategoryDto } from './dto/category.dto';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { UpdateCategoryOrderDto } from './dto/update-category-order.dto';
import { CategoryOrderResponseDto } from './dto/category-order-response.dto';
export declare class CategoriesController {
    private readonly categoriesService;
    private readonly logger;
    constructor(categoriesService: CategoriesService);
    getCategories(): Promise<CategoryDto[]>;
    getCategoryById(id: string): Promise<CategoryDto>;
    createCategory(createCategoryDto: CreateCategoryDto): Promise<CategoryDto>;
    updateCategory(id: string, updateCategoryDto: UpdateCategoryDto): Promise<CategoryDto>;
    deleteCategory(id: string): Promise<void>;
    updateCategoryOrder(updateOrderDto: UpdateCategoryOrderDto): Promise<CategoryOrderResponseDto>;
}
