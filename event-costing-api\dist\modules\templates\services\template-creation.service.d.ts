import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateTemplateFromCalculationDto } from '../dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { TemplateSummaryDto } from '../dto/template-summary.dto';
import { TemplateVenueService } from './template-venue.service';
export declare class TemplateCreationService {
    private readonly supabaseService;
    private readonly templateVenueService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateVenueService: TemplateVenueService);
    createTemplateFromCalculation(createDto: CreateTemplateFromCalculationDto, user: User): Promise<TemplateSummaryDto>;
    createTemplate(createDto: CreateTemplateDto, user: User): Promise<TemplateSummaryDto>;
}
