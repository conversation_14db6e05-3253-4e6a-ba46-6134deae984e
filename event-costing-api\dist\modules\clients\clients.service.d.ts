import { SupabaseService } from '../../core/supabase/supabase.service';
import { ClientDto } from './dto/client.dto';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
export declare class ClientsService {
    private readonly supabaseService;
    private readonly logger;
    private readonly TABLE_NAME;
    constructor(supabaseService: SupabaseService);
    private handleSupabaseError;
    create(createClientDto: CreateClientDto): Promise<ClientDto>;
    findAll(search?: string): Promise<ClientDto[]>;
    findOne(id: string): Promise<ClientDto>;
    update(id: string, updateClientDto: UpdateClientDto): Promise<ClientDto>;
    remove(id: string): Promise<void>;
}
