import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CalculationCrudService } from './calculation-crud.service';
import { CalculationItemsService } from '../../calculation-items/calculation-items.service';
import { PackagesService } from '../../packages/packages.service';
import { CategoriesService } from '../../categories/categories.service';
import { CalculationCompleteDataDto } from '../dto/calculation-complete-data.dto';
export declare class CalculationCompleteDataService {
    private readonly supabaseService;
    private readonly calculationCrudService;
    private readonly calculationItemsService;
    private readonly packagesService;
    private readonly categoriesService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, calculationCrudService: CalculationCrudService, calculationItemsService: CalculationItemsService, packagesService: PackagesService, categoriesService: CategoriesService);
    getCompleteData(calculationId: string, user: User): Promise<CalculationCompleteDataDto>;
    private getCalculationDetails;
    private getLineItems;
    private getPackagesByCategory;
    private getCategories;
    private extractResult;
    private collectErrors;
}
