"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ClientsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientsController = void 0;
const common_1 = require("@nestjs/common");
const clients_service_1 = require("./clients.service");
const client_dto_1 = require("./dto/client.dto");
const create_client_dto_1 = require("./dto/create-client.dto");
const update_client_dto_1 = require("./dto/update-client.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
let ClientsController = ClientsController_1 = class ClientsController {
    clientsService;
    logger = new common_1.Logger(ClientsController_1.name);
    constructor(clientsService) {
        this.clientsService = clientsService;
    }
    async create(createClientDto) {
        this.logger.log(`Received request to create client: ${createClientDto.client_name}`);
        return this.clientsService.create(createClientDto);
    }
    async findAll(search) {
        this.logger.log(`Received request to list clients ${search ? `with search: '${search}'` : ''}`);
        return this.clientsService.findAll(search);
    }
    async findOne(id) {
        this.logger.log(`Received request to get client ID: ${id}`);
        return this.clientsService.findOne(id);
    }
    async update(id, updateClientDto) {
        this.logger.log(`Received request to update client ID: ${id}`);
        return this.clientsService.update(id, updateClientDto);
    }
    async remove(id) {
        this.logger.log(`Received request to delete client ID: ${id}`);
        await this.clientsService.remove(id);
    }
};
exports.ClientsController = ClientsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new client' }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Client created successfully.',
        type: client_dto_1.ClientDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request (Validation Error)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (e.g., duplicate email)' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_client_dto_1.CreateClientDto]),
    __metadata("design:returntype", Promise)
], ClientsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all clients with optional search' }),
    (0, swagger_1.ApiOkResponse)({ description: 'List of clients.', type: [client_dto_1.ClientDto] }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for client name, company, or email',
    }),
    __param(0, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific client by ID' }),
    (0, swagger_1.ApiOkResponse)({ description: 'Client details.', type: client_dto_1.ClientDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Client not found.' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a client' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Client updated successfully.',
        type: client_dto_1.ClientDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Client not found.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request (Validation Error)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (e.g., duplicate email)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_client_dto_1.UpdateClientDto]),
    __metadata("design:returntype", Promise)
], ClientsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a client' }),
    (0, swagger_1.ApiNoContentResponse)({ description: 'Client deleted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Client not found.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientsController.prototype, "remove", null);
exports.ClientsController = ClientsController = ClientsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Clients'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('clients'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [clients_service_1.ClientsService])
], ClientsController);
//# sourceMappingURL=clients.controller.js.map