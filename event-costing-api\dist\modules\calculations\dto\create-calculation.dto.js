"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCalculationDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateCalculationDto {
    name;
    currency_id;
    city_id;
    venue_ids;
    event_start_date;
    event_end_date;
    attendees;
    event_type_id;
    notes;
    version_notes;
    client_id;
    event_id;
}
exports.CreateCalculationDto = CreateCalculationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the calculation',
        example: 'Corporate Event 2025',
    }),
    (0, class_validator_1.IsString)({ message: 'Name must be a string.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Name should not be empty.' }),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the currency',
        format: 'uuid',
        example: '685860b9-257f-41eb-b223-b3e1fad8f3b9',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'UUID of the city',
        format: 'uuid',
        example: '029f586d-70da-4637-b58a-176470d3e528',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of venue IDs',
        type: [String],
        format: 'uuid',
        example: ['a1b2c3d4-e5f6-7890-1234-567890abcdef'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], CreateCalculationDto.prototype, "venue_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event start datetime (ISO 8601 format)',
        example: '2025-06-01T00:00:00.000Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsISO8601)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "event_start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event end datetime (ISO 8601 format)',
        example: '2025-06-03T23:59:59.999Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsISO8601)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "event_end_date", void 0);
__decorate([
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCalculationDto.prototype, "attendees", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event type ID (UUID reference to event_types table)',
        format: 'uuid',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "event_type_id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", Object)
], CreateCalculationDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", Object)
], CreateCalculationDto.prototype, "version_notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], CreateCalculationDto.prototype, "client_id", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCalculationDto.prototype, "event_id", void 0);
//# sourceMappingURL=create-calculation.dto.js.map