"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const jwt_validation_service_1 = require("../services/jwt-validation.service");
let JwtStrategy = JwtStrategy_1 = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    configService;
    jwtValidationService;
    logger = new common_1.Logger(JwtStrategy_1.name);
    constructor(configService, jwtValidationService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET') || 'your-secret-key',
        });
        this.configService = configService;
        this.jwtValidationService = jwtValidationService;
    }
    async validate(payload) {
        try {
            this.logger.debug(`Validating JWT payload for user: ${payload.email}`);
            const user = await this.jwtValidationService.validateUserFromJwtPayload(payload);
            if (!user) {
                this.logger.warn(`JWT validation failed for user: ${payload.email}`);
                throw new common_1.UnauthorizedException('User validation failed');
            }
            this.logger.debug(`JWT validation successful for user: ${payload.email}`);
            return user;
        }
        catch (error) {
            this.logger.error(`JWT validation error: ${error.message}`, error.stack);
            throw new common_1.UnauthorizedException('Invalid token or authentication failure');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = JwtStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        jwt_validation_service_1.JwtValidationService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map