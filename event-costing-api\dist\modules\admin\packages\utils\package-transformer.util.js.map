{"version": 3, "file": "package-transformer.util.js", "sourceRoot": "", "sources": ["../../../../../src/modules/admin/packages/utils/package-transformer.util.ts"], "names": [], "mappings": ";;;AAKA,MAAa,sBAAsB;IAUjC,MAAM,CAAC,8BAA8B,CACnC,QAAe,EACf,aAAkC,EAClC,YAAiC,EACjC,SAA2B,EAC3B,SAAgC;QAEhC,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI;gBACzC,KAAK,EAAE,IAAI;gBACX,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,KAAK;aAClB,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;YAE3C,OAAO;gBACL,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,SAAS;gBAC7D,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,SAAS;gBAC5D,oBAAoB,EAAE,GAAG,CAAC,oBAAoB;gBAC9C,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC;gBAC/B,cAAc,EAAE,GAAG,CAAC,cAAc;gBAClC,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,EAAE;gBAChC,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,SAAS,EAAE,MAAM;gBACjB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,cAAc,EAAE,SAAS,CAAC,cAAc;gBACxC,UAAU,EAAE,SAAS,CAAC,UAAU;aACjC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAWD,MAAM,CAAC,mCAAmC,CACxC,GAAQ,EACR,YAAqB,EACrB,YAAqB,EACrB,SAAe,EACf,MAAiB;QAEjB,MAAM,gBAAgB,GAAG;YACvB,KAAK,EAAE,IAAI;YACX,YAAY,EAAE,IAAI;YAClB,cAAc,EAAE,IAAI;YACpB,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,MAAM,cAAc,GAAG,SAAS,IAAI,gBAAgB,CAAC;QAErD,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,YAAY,EAAE,YAAY,IAAI,SAAS;YACvC,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,YAAY,EAAE,YAAY,IAAI,SAAS;YACvC,oBAAoB,EAAE,GAAG,CAAC,oBAAoB;YAC9C,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC;YAC/B,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,EAAE;YAChC,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,SAAS,EAAE,MAAM,IAAI,EAAE;YACvB,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,UAAU,EAAE,cAAc,CAAC,UAAU;SACtC,CAAC;IACJ,CAAC;IAQD,MAAM,CAAC,gBAAgB,CAAC,QAAe,EAAE,KAAa;QACpD,OAAO;YACL,GAAG,IAAI,GAAG,CACR,QAAQ;iBACL,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBACzB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAC1B;SACF,CAAC;IACJ,CAAC;IAQD,MAAM,CAAC,oBAAoB,CAAC,QAAe,EAAE,KAAa;QACxD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAiB,CAAC;QAEzC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrB,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC7B,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC9B,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAQD,MAAM,CAAC,cAAc,CACnB,QAAe,EACf,QAKC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAC,SAAS,KAAK,SAAS,IAAI,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC9E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACnE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACnE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBAC1F,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IASD,MAAM,CAAC,YAAY,CACjB,QAAe,EACf,KAAa,EACb,QAAwB,KAAK;QAE7B,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACxB,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAExB,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS;gBAAE,OAAO,CAAC,CAAC;YACtD,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS;gBAAE,OAAO,CAAC,CAAC,CAAC;YAEvD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC7D,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACpE,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IASD,MAAM,CAAC,gBAAgB,CACrB,QAAe,EACf,SAAiB,CAAC,EAClB,QAAgB,EAAE;QAQlB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC9B,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;QAEpD,OAAO;YACL,IAAI;YACJ,KAAK,EAAE,KAAK;YACZ,MAAM;YACN,KAAK;YACL,OAAO,EAAE,MAAM,GAAG,KAAK,GAAG,KAAK;SAChC,CAAC;IACJ,CAAC;IAOD,MAAM,CAAC,wBAAwB,CAAC,GAAQ;QAItC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,GAAG,CAAC,WAAW,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,GAAG,CAAC,WAAW,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,GAAG,CAAC,cAAc,IAAI,OAAO,GAAG,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAOD,MAAM,CAAC,gBAAgB,CAAC,GAAQ;QAC9B,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAvSD,wDAuSC"}