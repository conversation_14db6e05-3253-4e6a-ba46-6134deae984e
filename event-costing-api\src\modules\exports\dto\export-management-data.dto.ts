import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ExportManagementFiltersDto } from './export-management-filters.dto';
import { ExportFormat } from '../enums/export-format.enum';

/**
 * DTO for paginated export results
 */
export class PaginatedExportsDto {
  @ApiProperty({
    description: 'Array of exports',
    type: [Object],
  })
  data: Array<{
    id: string;
    calculation_id: string;
    format: ExportFormat;
    status: string;
    file_url?: string;
    download_url?: string;
    error_message?: string;
    created_at: string;
    updated_at: string;
    calculations?: any;
  }>;

  @ApiProperty({
    description: 'Total number of exports',
    example: 50,
  })
  totalCount: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  pageSize: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 5,
  })
  totalPages: number;
}

/**
 * DTO for calculation information
 */
export class CalculationInfoDto {
  @ApiProperty({
    description: 'Calculation ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Calculation name',
    example: 'Corporate Event - Jakarta',
  })
  name: string;

  @ApiProperty({
    description: 'Calculation status',
    example: 'finalized',
  })
  status: string;

  @ApiProperty({
    description: 'Creation timestamp',
    type: String,
    format: 'date-time',
  })
  created_at: string;
}

/**
 * DTO for export statistics
 */
export class ExportStatisticsDto {
  @ApiProperty({
    description: 'Total number of exports',
    example: 50,
  })
  totalExports: number;

  @ApiProperty({
    description: 'Number of completed exports',
    example: 45,
  })
  completedExports: number;

  @ApiProperty({
    description: 'Number of failed exports',
    example: 3,
  })
  failedExports: number;

  @ApiProperty({
    description: 'Number of pending exports',
    example: 2,
  })
  pendingExports: number;

  @ApiProperty({
    description: 'Exports grouped by format',
    type: Object,
    example: { pdf: 30, xlsx: 15, csv: 5 },
  })
  exportsByFormat: Record<string, number>;

  @ApiProperty({
    description: 'Exports grouped by status',
    type: Object,
    example: { completed: 45, failed: 3, pending: 2 },
  })
  exportsByStatus: Record<string, number>;
}

/**
 * DTO for recent export activity
 */
export class RecentExportActivityDto {
  @ApiProperty({
    description: 'Export ID',
    type: String,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Calculation ID',
    type: String,
    format: 'uuid',
  })
  calculationId: string;

  @ApiProperty({
    description: 'Calculation name',
    example: 'Corporate Event - Jakarta',
  })
  calculationName: string;

  @ApiProperty({
    description: 'Export format',
    enum: ExportFormat,
    example: ExportFormat.PDF,
  })
  format: ExportFormat;

  @ApiProperty({
    description: 'Export status',
    example: 'completed',
  })
  status: string;

  @ApiProperty({
    description: 'Activity timestamp',
    type: String,
    format: 'date-time',
  })
  timestamp: string;
}

/**
 * DTO for available filter options
 */
export class ExportAvailableFiltersDto {
  @ApiProperty({
    description: 'Available export formats',
    type: [String],
    enum: ExportFormat,
  })
  formats: ExportFormat[];

  @ApiProperty({
    description: 'Available export statuses',
    type: [String],
    example: ['pending', 'processing', 'completed', 'failed'],
  })
  statuses: string[];

  @ApiProperty({
    description: 'Available calculations for export',
    type: [Object],
  })
  calculations: Array<{ id: string; name: string }>;
}

/**
 * DTO for filter information
 */
export class ExportFilterInfoDto {
  @ApiProperty({
    description: 'Applied filters',
    type: ExportManagementFiltersDto,
  })
  applied: ExportManagementFiltersDto;

  @ApiProperty({
    description: 'Available filter options',
    type: ExportAvailableFiltersDto,
  })
  available: ExportAvailableFiltersDto;
}

/**
 * DTO for export management metadata
 */
export class ExportManagementMetadataDto {
  @ApiProperty({
    description: 'Time taken to load the data in milliseconds',
    example: 320,
  })
  loadTime: number;

  @ApiProperty({
    description: 'Cache version for the response',
    example: '1.0',
  })
  cacheVersion: string;

  @ApiProperty({
    description: 'User ID who requested the data',
    type: String,
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Any errors encountered during data loading',
    type: [String],
    example: [],
  })
  errors?: string[];

  @ApiProperty({
    description: 'Timestamp when the data was loaded',
    type: String,
    format: 'date-time',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Total number of exports',
    example: 50,
  })
  totalExports: number;

  @ApiProperty({
    description: 'Number of filters applied',
    example: 2,
  })
  appliedFilters: number;
}

/**
 * Complete export management data response DTO
 * Consolidates all export management-related data in a single response
 */
export class ExportManagementDataDto {
  @ApiProperty({
    description: 'Paginated export results',
    type: PaginatedExportsDto,
  })
  exports: PaginatedExportsDto;

  @ApiProperty({
    description: 'Available calculations for export',
    type: [CalculationInfoDto],
  })
  calculations: CalculationInfoDto[];

  @ApiProperty({
    description: 'Export statistics',
    type: ExportStatisticsDto,
  })
  statistics: ExportStatisticsDto;

  @ApiProperty({
    description: 'Recent export activity',
    type: [RecentExportActivityDto],
  })
  recentActivity: RecentExportActivityDto[];

  @ApiProperty({
    description: 'Supported export formats',
    type: [String],
    enum: ExportFormat,
  })
  supportedFormats: ExportFormat[];

  @ApiProperty({
    description: 'Filter information',
    type: ExportFilterInfoDto,
  })
  filters: ExportFilterInfoDto;

  @ApiProperty({
    description: 'Metadata about the response',
    type: ExportManagementMetadataDto,
  })
  metadata: ExportManagementMetadataDto;
}
