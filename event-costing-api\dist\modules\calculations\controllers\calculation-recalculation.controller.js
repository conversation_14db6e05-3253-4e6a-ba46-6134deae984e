"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CalculationRecalculationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationRecalculationController = exports.RecalculationResponseDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const calculation_crud_service_1 = require("../services/calculation-crud.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../auth/decorators/get-current-user.decorator");
class RecalculationResponseDto {
    success;
    message;
    timestamp;
}
exports.RecalculationResponseDto = RecalculationResponseDto;
let CalculationRecalculationController = CalculationRecalculationController_1 = class CalculationRecalculationController {
    calculationCrudService;
    logger = new common_1.Logger(CalculationRecalculationController_1.name);
    constructor(calculationCrudService) {
        this.calculationCrudService = calculationCrudService;
    }
    async validateCalculationOwnership(calculationId, user) {
        this.logger.debug(`Validating ownership for calculation: ${calculationId}, user: ${user.id}`);
        try {
            await this.calculationCrudService.findCalculationRawById(calculationId, user);
            this.logger.debug(`Ownership validated for calculation: ${calculationId}`);
        }
        catch (error) {
            this.logger.warn(`Ownership validation failed for calculation: ${calculationId}, user: ${user.id}`);
            throw error;
        }
    }
    async recalculateCalculation(calculationId, user) {
        this.logger.log(`User ${user.email} requesting recalculation for calculation ${calculationId}`);
        await this.validateCalculationOwnership(calculationId, user);
        const startTime = Date.now();
        try {
            await this.calculationCrudService.triggerRecalculation(calculationId);
            const duration = Date.now() - startTime;
            this.logger.log(`Calculation ${calculationId} recalculated successfully in ${duration}ms`);
            return {
                success: true,
                message: 'Calculation recalculated successfully',
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Failed to recalculate calculation ${calculationId} after ${duration}ms: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.CalculationRecalculationController = CalculationRecalculationController;
__decorate([
    (0, common_1.Post)('recalculate'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Recalculate calculation totals',
        description: 'Triggers recalculation of all totals, taxes, and profit for the calculation. Replaces the mixed Supabase RPC + API approach with a unified endpoint.',
    }),
    (0, swagger_1.ApiParam)({ name: 'calculationId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Calculation recalculated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Calculation recalculated successfully',
                },
                timestamp: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Failed to recalculate calculation totals.',
    }),
    __param(0, (0, common_1.Param)('calculationId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationRecalculationController.prototype, "recalculateCalculation", null);
exports.CalculationRecalculationController = CalculationRecalculationController = CalculationRecalculationController_1 = __decorate([
    (0, swagger_1.ApiTags)('Calculation Recalculation'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('calculations/:calculationId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => calculation_crud_service_1.CalculationCrudService))),
    __metadata("design:paramtypes", [calculation_crud_service_1.CalculationCrudService])
], CalculationRecalculationController);
//# sourceMappingURL=calculation-recalculation.controller.js.map