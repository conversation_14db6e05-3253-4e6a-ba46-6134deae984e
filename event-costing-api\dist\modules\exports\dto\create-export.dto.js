"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateExportDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const export_format_enum_1 = require("../enums/export-format.enum");
class CreateExportDto {
    calculationId;
    format;
    recipient;
}
exports.CreateExportDto = CreateExportDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the calculation history record to export.',
        format: 'uuid',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateExportDto.prototype, "calculationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The desired export format.',
        enum: export_format_enum_1.ExportFormat,
        example: export_format_enum_1.ExportFormat.PDF,
    }),
    (0, class_validator_1.IsEnum)(export_format_enum_1.ExportFormat),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateExportDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional email address to send the export to.',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateExportDto.prototype, "recipient", void 0);
//# sourceMappingURL=create-export.dto.js.map