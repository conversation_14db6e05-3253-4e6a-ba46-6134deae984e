import { SupabaseService } from '../../core/supabase/supabase.service';
import { CurrencyDto } from './dto/currency.dto';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
export declare class CurrenciesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    private readonly selectFields;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<CurrencyDto[]>;
    findOneById(id: string): Promise<CurrencyDto>;
    createCurrency(createDto: CreateCurrencyDto): Promise<CurrencyDto>;
    updateCurrency(id: string, updateDto: UpdateCurrencyDto): Promise<CurrencyDto>;
    deleteCurrency(id: string): Promise<void>;
}
