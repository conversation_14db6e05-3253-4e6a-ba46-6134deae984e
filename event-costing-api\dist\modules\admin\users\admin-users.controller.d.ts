import { AdminUsersService } from './admin-users.service';
import { AdminUserDto } from './dto/admin-user.dto';
import { RoleDto } from './dto/role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class AdminUsersController {
    private readonly adminUsersService;
    private readonly logger;
    constructor(adminUsersService: AdminUsersService);
    findAll(): Promise<AdminUserDto[]>;
    getRoles(): Promise<RoleDto[]>;
    findOne(id: string): Promise<AdminUserDto>;
    createUser(createUserDto: CreateUserDto): Promise<AdminUserDto>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<AdminUserDto>;
    updateRole(id: string, updateUserRoleDto: UpdateUserRoleDto): Promise<void>;
    updateStatus(id: string, updateUserStatusDto: UpdateUserStatusDto): Promise<void>;
}
