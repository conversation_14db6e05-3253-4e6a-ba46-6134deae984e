import { CalculationStatus } from '../enums/calculation-status.enum';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';
export declare class ClientSummaryDto {
    id: string;
    client_name: string;
    contact_person?: string | null;
    email?: string | null;
}
export declare class EventSummaryDto {
    id: string;
    event_name: string;
    event_start_datetime?: string | null;
    event_end_datetime?: string | null;
}
export declare class CurrencySummaryDto {
    id: string;
    code: string;
}
export declare class CalculationLineItemOptionDto {
    id: string;
    option_name_snapshot: string;
    price_adjustment_snapshot: number;
}
export declare class CalculationLineItemDto {
    id: string;
    package_id: string | null;
    item_name_snapshot: string;
    option_summary_snapshot: string | null;
    item_quantity: number;
    duration_days: number;
    unit_base_price: number;
    options_total_adjustment: number;
    calculated_line_total: number;
    notes: string | null;
    unit_base_cost_snapshot: number;
    options_total_cost_snapshot: number;
    calculated_line_cost: number;
    options: CalculationLineItemOptionDto[];
}
export declare class CalculationCustomItemDto {
    id: string;
    item_name: string;
    description: string | null;
    quantity: number;
    unit_price: number;
    unit_cost: number;
}
export declare class CityReferenceDto {
    id: string;
    name: string;
}
export declare class CalculationDetailDto {
    id: string;
    name: string;
    status: CalculationStatus;
    event_type_id: string | null;
    attendees: number | null;
    event_start_date: string | null;
    event_end_date: string | null;
    notes: string | null;
    version_notes: string | null;
    created_at: string;
    updated_at: string;
    created_by: string;
    currency: CurrencySummaryDto | null;
    city: CityReferenceDto | null;
    client: ClientSummaryDto | null;
    event: EventSummaryDto | null;
    venues: VenueReferenceDto[];
    line_items: CalculationLineItemDto[];
    custom_items: CalculationCustomItemDto[];
    subtotal: number;
    taxes: TaxDetailItemDto[] | null;
    discount: DiscountDetailDto | null;
    total: number;
    total_cost: number;
    estimated_profit: number;
}
export declare class TaxDetailItemDto {
    name?: string | null;
    rate?: number | null;
    amount?: number | null;
}
export declare class DiscountDetailDto {
    name?: string | null;
    amount?: number | null;
    description?: string | null;
    percentage?: number | null;
}
