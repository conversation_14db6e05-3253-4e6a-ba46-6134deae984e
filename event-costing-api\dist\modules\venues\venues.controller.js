"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VenuesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VenuesController = void 0;
const common_1 = require("@nestjs/common");
const venues_service_1 = require("./venues.service");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const venue_reference_dto_1 = require("./dto/venue-reference.dto");
let VenuesController = VenuesController_1 = class VenuesController {
    venuesService;
    logger = new common_1.Logger(VenuesController_1.name);
    constructor(venuesService) {
        this.venuesService = venuesService;
    }
    async getVenues(cityId, active, classification, minCapacity, maxCapacity) {
        this.logger.log(`Fetching venues with filters: cityId=${cityId}, active=${active}, classification=${classification}, minCapacity=${minCapacity}, maxCapacity=${maxCapacity}`);
        if (classification ||
            minCapacity !== undefined ||
            maxCapacity !== undefined) {
            return this.venuesService.findWithEnhancedFilters(cityId, active, classification, minCapacity, maxCapacity);
        }
        return this.venuesService.findAllWithFilters(cityId, active);
    }
    async getVenueById(id) {
        this.logger.log(`Fetching venue with ID: ${id}`);
        const venues = await this.venuesService.findByIds([id]);
        if (venues.length === 0) {
            throw new common_1.NotFoundException(`Venue with ID ${id} not found`);
        }
        return venues[0];
    }
};
exports.VenuesController = VenuesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiQuery)({
        name: 'cityId',
        required: false,
        type: String,
        description: 'Filter venues by city ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'active',
        required: false,
        type: Boolean,
        description: 'Filter venues by active status',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'classification',
        required: false,
        type: String,
        description: 'Filter venues by classification (outdoor, hotel, indoor, premium, luxury)',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'minCapacity',
        required: false,
        type: Number,
        description: 'Filter venues by minimum capacity',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'maxCapacity',
        required: false,
        type: Number,
        description: 'Filter venues by maximum capacity',
    }),
    (0, swagger_1.ApiOkResponse)({ type: [venue_reference_dto_1.VenueReferenceDto] }),
    __param(0, (0, common_1.Query)('cityId')),
    __param(1, (0, common_1.Query)('active')),
    __param(2, (0, common_1.Query)('classification')),
    __param(3, (0, common_1.Query)('minCapacity')),
    __param(4, (0, common_1.Query)('maxCapacity')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, String, Number, Number]),
    __metadata("design:returntype", Promise)
], VenuesController.prototype, "getVenues", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: venue_reference_dto_1.VenueReferenceDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VenuesController.prototype, "getVenueById", null);
exports.VenuesController = VenuesController = VenuesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Venues'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('venues'),
    __metadata("design:paramtypes", [venues_service_1.VenuesService])
], VenuesController);
//# sourceMappingURL=venues.controller.js.map