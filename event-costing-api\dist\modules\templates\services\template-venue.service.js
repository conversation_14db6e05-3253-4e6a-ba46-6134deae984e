"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateVenueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateVenueService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let TemplateVenueService = TemplateVenueService_1 = class TemplateVenueService {
    supabaseService;
    logger = new common_1.Logger(TemplateVenueService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async addVenueIdsToTemplates(templates) {
        if (templates.length === 0) {
            return;
        }
        const supabase = this.supabaseService.getClient();
        const templateIds = templates.map(template => template.id);
        const { data: venueData, error: venueError } = await supabase
            .from('template_venues')
            .select('template_id, venue_id')
            .in('template_id', templateIds);
        if (venueError) {
            this.logger.error(`Error fetching venues for templates: ${venueError.message}`, venueError.stack);
            return;
        }
        if (venueData) {
            const venuesByTemplate = venueData.reduce((acc, curr) => {
                if (!acc[curr.template_id]) {
                    acc[curr.template_id] = [];
                }
                acc[curr.template_id].push(curr.venue_id);
                return acc;
            }, {});
            templates.forEach(template => {
                template.venue_ids = venuesByTemplate[template.id] || [];
            });
            if (templates.length > 0) {
                this.logger.log(`Template venue info sample: ${JSON.stringify({
                    id: templates[0].id,
                    name: templates[0].name,
                    venue_ids: templates[0].venue_ids,
                    has_venue_ids: !!templates[0].venue_ids,
                    venue_ids_length: templates[0].venue_ids?.length || 0,
                })}`);
            }
        }
    }
    async addVenueIdsToTemplate(template) {
        const supabase = this.supabaseService.getClient();
        const { data: venueData, error: venueError } = await supabase
            .from('template_venues')
            .select('venue_id')
            .eq('template_id', template.id);
        if (venueError) {
            this.logger.error(`Error fetching venues for template ${template.id}: ${venueError.message}`, venueError.stack);
            return;
        }
        template.venue_ids = venueData?.map(v => v.venue_id) || [];
        this.logger.log(`Added ${template.venue_ids.length} venue IDs to template ${template.id}`);
    }
    async createTemplateVenueAssociations(templateId, venueIds) {
        if (venueIds.length === 0) {
            return;
        }
        const supabase = this.supabaseService.getClient();
        const templateVenues = venueIds.map(venueId => ({
            template_id: templateId,
            venue_id: venueId,
        }));
        const { error: venueInsertError } = await supabase
            .from('template_venues')
            .insert(templateVenues);
        if (venueInsertError) {
            this.logger.error(`Error inserting template venues: ${venueInsertError.message}`, venueInsertError.stack);
        }
        else {
            this.logger.log(`Successfully associated ${venueIds.length} venues with template ${templateId}`);
        }
    }
};
exports.TemplateVenueService = TemplateVenueService;
exports.TemplateVenueService = TemplateVenueService = TemplateVenueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], TemplateVenueService);
//# sourceMappingURL=template-venue.service.js.map