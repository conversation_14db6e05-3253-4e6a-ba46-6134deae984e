export declare class BaseTemplateDto {
    id: string;
    name: string;
    description?: string;
    event_type_id?: string;
    city_id?: string;
    currency_id?: string;
    attendees?: number;
    template_start_date?: Date;
    template_end_date?: Date;
    category_id?: string;
    created_at: Date;
    updated_at: Date;
    created_by: string;
    is_public: boolean;
    is_deleted: boolean;
    taxes?: unknown;
    discount?: unknown;
}
export declare class TemplateSummaryDto extends BaseTemplateDto {
    venue_ids?: string[];
}
declare class PackageSelectionItemDto {
    package_id: string;
    option_ids: string[];
    item_quantity: number | null;
    duration_days: number | null;
}
declare class EnhancedPackageSelectionItemDto {
    package_id: string;
    package_name: string;
    option_ids: string[];
    option_names: string[];
    item_quantity: number | null;
    duration_days: number | null;
}
declare class TemplateCustomItemDto {
    item_name: string;
    description?: string;
    item_quantity: number;
    unit_price: number;
    unit_cost?: number;
    currency_id?: string;
    category_id?: string;
    city_id?: string;
    item_quantity_basis?: number;
    quantity_basis?: string;
}
export declare class TemplateDetailDto extends BaseTemplateDto {
    package_selections: PackageSelectionItemDto[];
    venue_ids?: string[];
    custom_items?: TemplateCustomItemDto[];
}
export declare class EnhancedTemplateDetailDto extends BaseTemplateDto {
    package_selections: EnhancedPackageSelectionItemDto[];
    venue_ids?: string[];
    custom_items?: TemplateCustomItemDto[];
}
export declare class PaginatedTemplatesResponse {
    data: TemplateSummaryDto[];
    count: number;
}
export declare class PaginatedAdminTemplatesResponse {
    data: TemplateDetailDto[];
    count: number;
}
export {};
