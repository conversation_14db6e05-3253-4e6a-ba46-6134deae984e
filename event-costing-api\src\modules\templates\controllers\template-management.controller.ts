import {
  <PERSON>,
  Get,
  Query,
  Param,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiT<PERSON>s,
  ApiBearerAuth,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { TemplateConsolidatedService } from '../services/template-consolidated.service';
import { TemplateCompleteDataDto } from '../dto/template-complete-data.dto';
import { TemplateFiltersDto } from '../dto/template-filters.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';

/**
 * Controller for consolidated template management operations
 * Implements consolidated template management endpoints
 */
@ApiTags('Template Management')
@ApiBearerAuth()
@Controller('templates/management')
@UseGuards(JwtAuthGuard)
export class TemplateManagementController {
  private readonly logger = new Logger(TemplateManagementController.name);

  constructor(
    private readonly templateConsolidatedService: TemplateConsolidatedService,
  ) {}

  /**
   * Get complete template management data in a single API call
   * Replaces the need for multiple separate API calls for template management
   */
  @Get()
  @ApiOperation({ 
    summary: 'Get complete template management data',
    description: 'Consolidated endpoint that returns templates, categories, event types, packages, and statistics in a single response. Replaces the need for multiple separate API calls for template management.'
  })
  @ApiOkResponse({ 
    description: 'Complete template management data with metadata',
    type: TemplateCompleteDataDto 
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load template management data.',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for template name or description' })
  @ApiQuery({ name: 'eventType', required: false, description: 'Filter by event type' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'Filter by category ID' })
  @ApiQuery({ name: 'cityId', required: false, description: 'Filter by city ID' })
  @ApiQuery({ name: 'venueIds', required: false, description: 'Filter by venue IDs (comma-separated)' })
  @ApiQuery({ name: 'minAttendees', required: false, description: 'Minimum attendees filter' })
  @ApiQuery({ name: 'maxAttendees', required: false, description: 'Maximum attendees filter' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by template status (all/active/inactive)' })
  @ApiQuery({ name: 'visibility', required: false, description: 'Filter by template visibility (all/public/private)' })
  @ApiQuery({ name: 'createdBy', required: false, description: 'Filter by template creator' })
  @ApiQuery({ name: 'myTemplatesOnly', required: false, description: 'Include only current user templates' })
  @ApiQuery({ name: 'hasPackagesFromCategory', required: false, description: 'Include templates with packages from category' })
  @ApiQuery({ name: 'hasCalculations', required: false, description: 'Include only templates with calculations' })
  @ApiQuery({ name: 'createdAfter', required: false, description: 'Filter by creation date (after)' })
  @ApiQuery({ name: 'createdBefore', required: false, description: 'Filter by creation date (before)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number for pagination' })
  @ApiQuery({ name: 'pageSize', required: false, description: 'Number of items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort by field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (asc/desc)' })
  @ApiQuery({ name: 'includePackages', required: false, description: 'Include template packages' })
  @ApiQuery({ name: 'includeCalculations', required: false, description: 'Include template calculations' })
  @ApiQuery({ name: 'includeStatistics', required: false, description: 'Include template statistics' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'excludeId', required: false, description: 'Exclude template with specific ID' })
  async getManagementData(
    @Query() filters: TemplateFiltersDto,
    @GetCurrentUser() user: User,
  ): Promise<TemplateCompleteDataDto> {
    this.logger.log(`User ${user.email} requesting template management data`);

    // Handle comma-separated arrays in query parameters
    if (typeof filters.venueIds === 'string') {
      filters.venueIds = (filters.venueIds as string).split(',').filter(id => id.trim());
    }
    if (typeof filters.tags === 'string') {
      filters.tags = (filters.tags as string).split(',').filter(tag => tag.trim());
    }

    // Set myTemplatesOnly if not specified (default to user's templates)
    if (filters.myTemplatesOnly === undefined) {
      filters.myTemplatesOnly = true;
    }

    return this.templateConsolidatedService.getTemplateManagementData(filters, user);
  }

  /**
   * Get complete template detail data for editing/viewing
   * Includes template details, packages, calculations, and metadata
   */
  @Get(':id/detail')
  @ApiOperation({ 
    summary: 'Get complete template detail data',
    description: 'Consolidated endpoint that returns template details, packages, calculations, and categories in a single response for template editing/viewing.'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({ 
    description: 'Complete template detail data with metadata',
    schema: {
      type: 'object',
      properties: {
        template: { type: 'object', description: 'Template details' },
        packages: { type: 'array', description: 'Template packages' },
        calculation: { type: 'object', description: 'Template calculation data' },
        categories: { type: 'array', description: 'Available categories' },
        metadata: {
          type: 'object',
          properties: {
            loadTime: { type: 'number' },
            cacheVersion: { type: 'string' },
            userId: { type: 'string' },
            errors: { type: 'array', items: { type: 'string' } },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found or access denied.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to load template detail data.',
  })
  async getTemplateDetailData(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`User ${user.email} requesting template detail data for ID: ${id}`);

    return this.templateConsolidatedService.getTemplateDetailData(id, user);
  }

  /**
   * Get template management summary statistics
   * Provides overview metrics for template management
   */
  @Get('summary')
  @ApiOperation({ 
    summary: 'Get template management summary statistics',
    description: 'Returns summary statistics about template management including counts by status, event type, and usage metrics.'
  })
  @ApiOkResponse({ 
    description: 'Template management summary statistics',
    schema: {
      type: 'object',
      properties: {
        totalTemplates: { type: 'number', example: 25 },
        activeTemplates: { type: 'number', example: 20 },
        inactiveTemplates: { type: 'number', example: 5 },
        publicTemplates: { type: 'number', example: 15 },
        privateTemplates: { type: 'number', example: 10 },
        templatesByEventType: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              eventType: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        templatesByCategory: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              categoryId: { type: 'string' },
              categoryName: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        recentActivity: {
          type: 'object',
          properties: {
            templatesCreatedThisWeek: { type: 'number' },
            templatesCreatedThisMonth: { type: 'number' },
            mostUsedTemplate: { type: 'object' },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            loadTime: { type: 'number' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async getManagementSummary(
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(`User ${user.email} requesting template management summary`);

    const startTime = Date.now();

    // Get basic management data without filters
    const managementData = await this.templateConsolidatedService.getTemplateManagementData({}, user);

    // Calculate additional summary statistics
    const templates = managementData.templates.data;
    const totalTemplates = managementData.templates.totalCount;

    // Group templates by event type
    const templatesByEventType = managementData.eventTypes.map(eventType => ({
      eventType: eventType.name,
      count: templates.filter(template => template.event_type === eventType.name).length,
    }));

    // Group templates by category (based on packages)
    const templatesByCategory = managementData.categories.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      count: 0, // Would need to implement package-based counting
    }));

    // Calculate recent activity
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const templatesCreatedThisWeek = templates.filter(template => 
      new Date(template.created_at) >= oneWeekAgo
    ).length;

    const templatesCreatedThisMonth = templates.filter(template => 
      new Date(template.created_at) >= oneMonthAgo
    ).length;

    const loadTime = Date.now() - startTime;

    return {
      totalTemplates,
      activeTemplates: managementData.summary?.activeTemplates || 0,
      inactiveTemplates: managementData.summary?.inactiveTemplates || 0,
      publicTemplates: managementData.summary?.publicTemplates || 0,
      privateTemplates: managementData.summary?.privateTemplates || 0,
      templatesByEventType,
      templatesByCategory,
      recentActivity: {
        templatesCreatedThisWeek,
        templatesCreatedThisMonth,
        mostUsedTemplate: templates[0] || null, // Simplified - would need usage tracking
      },
      metadata: {
        loadTime,
        timestamp: new Date().toISOString(),
      },
    };
  }
}
