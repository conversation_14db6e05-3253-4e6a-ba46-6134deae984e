/**
 * Consolidated Template Management Service
 * 
 * This service implements the consolidated endpoint pattern for template management operations.
 * It replaces the need for multiple separate API calls with a single consolidated endpoint.
 */

import { getAuthenticatedApiClient } from '@/integrations/api/client';
import { API_ENDPOINTS } from '@/integrations/api/endpoints';
import { showError } from '@/lib/notifications';

/**
 * Types for the consolidated template management response
 */
export interface TemplateManagementData {
  templates: {
    data: Array<{
      id: string;
      name: string;
      description?: string;
      event_type_id?: string;
      city_id?: string;
      attendees?: number;
      template_start_date?: Date;
      template_end_date?: Date;
      category_id?: string;
      created_at: Date;
      updated_at: Date;
      created_by: string;
      is_public: boolean;
      is_deleted: boolean;
      venue_ids?: string[];
    }>;
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  categories: Array<{
    id: string;
    name: string;
    display_order: number;
    is_deleted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  eventTypes: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
  packages: Array<{
    package_id: string;
    name: string;
    description: string | null;
    category_id: string;
    price: number;
    quantity_basis: string;
    is_available_in_city: boolean;
    is_available_in_venue: boolean;
  }>;
  summary?: {
    totalTemplates: number;
    activeTemplates: number;
    inactiveTemplates: number;
    publicTemplates: number;
    privateTemplates: number;
  };
  filters: {
    applied: TemplateFilters;
    available: {
      categories: Array<{ id: string; name: string }>;
      eventTypes: Array<{ id: string; name: string }>;
    };
  };
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
    totalTemplates: number;
    appliedFilters: number;
  };
}

/**
 * Template detail data for editing/viewing
 */
export interface TemplateDetailData {
  template: {
    id: string;
    name: string;
    description?: string;
    event_type_id?: string;
    city_id?: string;
    attendees?: number;
    template_start_date?: Date;
    template_end_date?: Date;
    category_id?: string;
    created_at: Date;
    updated_at: Date;
    created_by: string;
    is_public: boolean;
    is_deleted: boolean;
    venue_ids?: string[];
    package_selections?: Array<{
      package_id: string;
      option_ids: string[];
    }>;
  };
  packages: Array<{
    id: string;
    package_id: string;
    name: string;
    description: string;
    category_id: string;
    price: number;
    quantity_basis: string;
  }>;
  calculation?: {
    packagesTotal: number;
    customItemsTotal: number;
    grandTotal: number;
    breakdown: Array<{
      packageId: string;
      packageName: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      currency: string;
      unitCost: number;
      totalCost: number;
    }>;
    currency: string;
    hasValidPrices: boolean;
    missingPrices: string[];
    totalCost: number;
    estimatedProfit: number;
  };
  categories: Array<{
    id: string;
    name: string;
    display_order: number;
    is_deleted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
  };
}

/**
 * Template management summary data
 */
export interface TemplateManagementSummary {
  totalTemplates: number;
  activeTemplates: number;
  inactiveTemplates: number;
  publicTemplates: number;
  privateTemplates: number;
  templatesByEventType: Array<{
    eventType: string;
    count: number;
  }>;
  templatesByCategory: Array<{
    categoryId: string;
    categoryName: string;
    count: number;
  }>;
  recentActivity: {
    templatesCreatedThisWeek: number;
    templatesCreatedThisMonth: number;
    mostUsedTemplate: any;
  };
  metadata: {
    loadTime: number;
    timestamp: string;
  };
}

/**
 * Template filters interface
 */
export interface TemplateFilters {
  search?: string;
  eventType?: string;
  categoryId?: string;
  cityId?: string;
  venueIds?: string[];
  minAttendees?: number;
  maxAttendees?: number;
  status?: 'all' | 'active' | 'inactive';
  visibility?: 'all' | 'public' | 'private';
  createdBy?: string;
  myTemplatesOnly?: boolean;
  hasPackagesFromCategory?: string;
  hasCalculations?: boolean;
  createdAfter?: string;
  createdBefore?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includePackages?: boolean;
  includeCalculations?: boolean;
  includeStatistics?: boolean;
  tags?: string[];
  excludeId?: string;
}

/**
 * Fetch complete template management data in a single API call
 * This replaces the need for multiple separate API calls:
 * 1. GET /admin/templates (templates)
 * 2. GET /admin/categories (categories)
 * 3. GET /admin/event-types (event types)
 * 4. GET /packages/variations (available packages)
 * 5. Template statistics
 * 
 * @param filters - Template filters
 * @returns Complete template management data with metadata
 */
export const getTemplateManagementData = async (
  filters: TemplateFilters = {},
): Promise<TemplateManagementData> => {
  try {
    console.log(`[API] Fetching template management data with filters:`, filters);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Build query parameters
    const queryParams = new URLSearchParams();
    
    // Map frontend filters to backend parameters
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.eventType) queryParams.append('eventType', filters.eventType);
    if (filters.categoryId && filters.categoryId !== 'all') {
      queryParams.append('categoryId', filters.categoryId);
    }
    if (filters.cityId && filters.cityId !== 'all') {
      queryParams.append('cityId', filters.cityId);
    }
    if (filters.venueIds && filters.venueIds.length > 0) {
      queryParams.append('venueIds', filters.venueIds.join(','));
    }
    if (filters.minAttendees) queryParams.append('minAttendees', filters.minAttendees.toString());
    if (filters.maxAttendees) queryParams.append('maxAttendees', filters.maxAttendees.toString());
    if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status);
    if (filters.visibility && filters.visibility !== 'all') queryParams.append('visibility', filters.visibility);
    if (filters.createdBy) queryParams.append('createdBy', filters.createdBy);
    if (filters.myTemplatesOnly !== undefined) {
      queryParams.append('myTemplatesOnly', filters.myTemplatesOnly.toString());
    }
    if (filters.hasPackagesFromCategory) {
      queryParams.append('hasPackagesFromCategory', filters.hasPackagesFromCategory);
    }
    if (filters.hasCalculations !== undefined) {
      queryParams.append('hasCalculations', filters.hasCalculations.toString());
    }
    if (filters.createdAfter) queryParams.append('createdAfter', filters.createdAfter);
    if (filters.createdBefore) queryParams.append('createdBefore', filters.createdBefore);
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);
    if (filters.includePackages !== undefined) {
      queryParams.append('includePackages', filters.includePackages.toString());
    }
    if (filters.includeCalculations !== undefined) {
      queryParams.append('includeCalculations', filters.includeCalculations.toString());
    }
    if (filters.includeStatistics !== undefined) {
      queryParams.append('includeStatistics', filters.includeStatistics.toString());
    }
    if (filters.tags && filters.tags.length > 0) {
      queryParams.append('tags', filters.tags.join(','));
    }
    if (filters.excludeId) queryParams.append('excludeId', filters.excludeId);

    // Make single API request to get all data
    const url = `${API_ENDPOINTS.TEMPLATES.MANAGEMENT.GET_ALL}${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    
    const response = await authClient.get(url);
    const data = response.data as TemplateManagementData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched template management data in ${loadTime}ms:`, {
      templates: data.templates.totalCount,
      categories: data.categories.length,
      eventTypes: data.eventTypes.length,
      packages: data.packages.length,
      errors: data.metadata.errors?.length || 0,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching template management data:`, error);
    showError('Failed to load template management data', {
      description: 'There was an error loading the template management data. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch complete template detail data for editing/viewing
 * Includes template details, packages, calculations, and metadata
 * 
 * @param templateId - Template ID
 * @returns Complete template detail data
 */
export const getTemplateDetailData = async (
  templateId: string,
): Promise<TemplateDetailData> => {
  try {
    console.log(`[API] Fetching template detail data for ID: ${templateId}`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to detail endpoint
    const response = await authClient.get(API_ENDPOINTS.TEMPLATES.MANAGEMENT.GET_DETAIL(templateId));
    const data = response.data as TemplateDetailData;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched template detail data in ${loadTime}ms:`, {
      templateId: data.template.id,
      templateName: data.template.name,
      packagesCount: data.packages.length,
      hasCalculation: !!data.calculation,
      categoriesCount: data.categories.length,
      errors: data.metadata.errors?.length || 0,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching template detail data:`, error);
    showError('Failed to load template detail data', {
      description: 'There was an error loading the template detail data. Please try again.',
    });
    throw error;
  }
};

/**
 * Fetch template management summary statistics
 * Provides overview metrics for template management
 * 
 * @returns Template management summary data
 */
export const getTemplateManagementSummary = async (): Promise<TemplateManagementSummary> => {
  try {
    console.log(`[API] Fetching template management summary`);
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to summary endpoint
    const response = await authClient.get(API_ENDPOINTS.TEMPLATES.MANAGEMENT.GET_SUMMARY);
    const data = response.data as TemplateManagementSummary;

    const loadTime = Date.now() - startTime;
    
    console.log(`[API] Fetched template management summary in ${loadTime}ms:`, {
      totalTemplates: data.totalTemplates,
      activeTemplates: data.activeTemplates,
      inactiveTemplates: data.inactiveTemplates,
      publicTemplates: data.publicTemplates,
      privateTemplates: data.privateTemplates,
    });

    return data;
  } catch (error) {
    console.error(`[API] Error fetching template management summary:`, error);
    showError('Failed to load template management summary', {
      description: 'There was an error loading the template management summary. Please try again.',
    });
    throw error;
  }
};
