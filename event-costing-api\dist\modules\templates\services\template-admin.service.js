"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateAdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateAdminService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_venue_service_1 = require("./template-venue.service");
const template_constants_1 = require("../constants/template.constants");
let TemplateAdminService = TemplateAdminService_1 = class TemplateAdminService {
    supabaseService;
    templateVenueService;
    logger = new common_1.Logger(TemplateAdminService_1.name);
    constructor(supabaseService, templateVenueService) {
        this.supabaseService = supabaseService;
        this.templateVenueService = templateVenueService;
    }
    async findAllAdmin(queryDto) {
        this.logger.log(`Admin finding templates with query: ${JSON.stringify(queryDto)}`);
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .select(template_constants_1.TemplateConstants.DETAIL_SELECT_FIELDS, { count: 'exact' });
        if (queryDto.name) {
            query = query.ilike('name', `%${queryDto.name}%`);
        }
        if (typeof queryDto.isPublic === 'boolean') {
            query = query.eq('is_public', queryDto.isPublic);
        }
        if (queryDto.cityId) {
            query = query.eq('city_id', queryDto.cityId);
        }
        if (queryDto.categoryId) {
            query = query.eq('category_id', queryDto.categoryId);
        }
        const sortBy = queryDto.sortBy || 'created_at';
        const sortOrder = queryDto.sortOrder || 'desc';
        const validSortColumns = [
            'name',
            'created_at',
            'updated_at',
            'template_start_date',
            'is_public',
            'is_deleted',
        ];
        const dbSortColumn = validSortColumns.includes(sortBy)
            ? sortBy
            : 'created_at';
        query = query.order(dbSortColumn, {
            ascending: sortOrder === 'asc',
            nullsFirst: false,
        });
        const limit = queryDto.limit || 20;
        const offset = queryDto.offset || 0;
        query = query.range(offset, offset + limit - 1);
        const { data, error, count } = await query;
        if (error) {
            this.logger.error(`Error admin fetching templates: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve templates.');
        }
        const templates = (data || []).map((raw) => ({
            ...raw,
            template_start_date: raw.template_start_date
                ? new Date(raw.template_start_date)
                : undefined,
            template_end_date: raw.template_end_date
                ? new Date(raw.template_end_date)
                : undefined,
            created_at: new Date(raw.created_at),
            updated_at: new Date(raw.updated_at),
            is_deleted: raw.is_deleted || false,
        }));
        await this.templateVenueService.addVenueIdsToTemplates(templates);
        return { data: templates, count: count || 0 };
    }
    async findOneAdmin(id) {
        this.logger.log(`Admin finding template details for ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .select(template_constants_1.TemplateConstants.DETAIL_SELECT_FIELDS)
            .eq('id', id)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Template with ID ${id} not found.`);
            }
            this.logger.error(`Error fetching admin template ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve template details.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Template with ID ${id} not found.`);
        }
        const template = {
            ...data,
            template_start_date: data.template_start_date
                ? new Date(data.template_start_date)
                : undefined,
            template_end_date: data.template_end_date
                ? new Date(data.template_end_date)
                : undefined,
            created_at: new Date(data.created_at),
            updated_at: new Date(data.updated_at),
            is_deleted: data.is_deleted || false,
        };
        await this.templateVenueService.addVenueIdsToTemplate(template);
        return template;
    }
    async updateTemplate(id, updateDto) {
        this.logger.log(`Updating template with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {
            name: updateDto.name,
            description: updateDto.description,
            event_type_id: updateDto.event_type_id,
            template_start_date: updateDto.template_start_date,
            template_end_date: updateDto.template_end_date,
            is_public: updateDto.is_public,
            updated_at: new Date().toISOString(),
        };
        const { data, error } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .update(updateData)
            .eq('id', id)
            .select(template_constants_1.TemplateConstants.DETAIL_SELECT_FIELDS)
            .single();
        if (error) {
            this.logger.error(`Error updating template: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update template.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Template with ID ${id} not found.`);
        }
        const template = {
            ...data,
            template_start_date: data.template_start_date
                ? new Date(data.template_start_date)
                : undefined,
            template_end_date: data.template_end_date
                ? new Date(data.template_end_date)
                : undefined,
            created_at: new Date(data.created_at),
            updated_at: new Date(data.updated_at),
            is_deleted: data.is_deleted || false,
        };
        await this.templateVenueService.addVenueIdsToTemplate(template);
        return template;
    }
    async updateTemplateStatus(id, isActive) {
        this.logger.log(`Updating template status for ID: ${id} to ${isActive ? 'active' : 'inactive'}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .update({
            is_deleted: !isActive,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select(template_constants_1.TemplateConstants.DETAIL_SELECT_FIELDS)
            .single();
        if (error) {
            this.logger.error(`Error updating template status: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update template status.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Template with ID ${id} not found.`);
        }
        const template = {
            ...data,
            template_start_date: data.template_start_date
                ? new Date(data.template_start_date)
                : undefined,
            template_end_date: data.template_end_date
                ? new Date(data.template_end_date)
                : undefined,
            created_at: new Date(data.created_at),
            updated_at: new Date(data.updated_at),
            is_deleted: data.is_deleted || false,
        };
        await this.templateVenueService.addVenueIdsToTemplate(template);
        return template;
    }
    async deleteTemplate(id) {
        this.logger.log(`Deleting template with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .delete()
            .eq('id', id);
        if (error) {
            this.logger.error(`Error deleting template: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not delete template.');
        }
    }
};
exports.TemplateAdminService = TemplateAdminService;
exports.TemplateAdminService = TemplateAdminService = TemplateAdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        template_venue_service_1.TemplateVenueService])
], TemplateAdminService);
//# sourceMappingURL=template-admin.service.js.map