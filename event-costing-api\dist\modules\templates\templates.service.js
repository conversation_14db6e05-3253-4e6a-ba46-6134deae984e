"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplatesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const services_1 = require("./services");
let TemplatesService = TemplatesService_1 = class TemplatesService {
    supabaseService;
    templateQueryService;
    templateCreationService;
    templateDetailService;
    templateAdminService;
    templateCalculationService;
    logger = new common_1.Logger(TemplatesService_1.name);
    constructor(supabaseService, templateQueryService, templateCreationService, templateDetailService, templateAdminService, templateCalculationService) {
        this.supabaseService = supabaseService;
        this.templateQueryService = templateQueryService;
        this.templateCreationService = templateCreationService;
        this.templateDetailService = templateDetailService;
        this.templateAdminService = templateAdminService;
        this.templateCalculationService = templateCalculationService;
    }
    async findUserTemplates(user, queryDto) {
        return this.templateQueryService.findUserTemplates(user, queryDto);
    }
    async createTemplate(createDto, user) {
        return this.templateCreationService.createTemplate(createDto, user);
    }
    async createTemplateFromCalculation(createDto, user) {
        return this.templateCreationService.createTemplateFromCalculation(createDto, user);
    }
    async findPublicTemplates(queryDto) {
        return this.templateQueryService.findPublicTemplates(queryDto);
    }
    async findOnePublic(id) {
        return this.templateDetailService.findOnePublic(id);
    }
    async findOnePublicEnhanced(id) {
        return this.templateDetailService.findOnePublicEnhanced(id);
    }
    async findAllAdmin(queryDto) {
        return this.templateAdminService.findAllAdmin(queryDto);
    }
    async findOneAdmin(id) {
        return this.templateAdminService.findOneAdmin(id);
    }
    async updateTemplate(id, updateDto) {
        return this.templateAdminService.updateTemplate(id, updateDto);
    }
    async updateTemplateStatus(id, isActive) {
        return this.templateAdminService.updateTemplateStatus(id, isActive);
    }
    async deleteTemplate(id) {
        return this.templateAdminService.deleteTemplate(id);
    }
    async calculateTemplateTotal(templateId) {
        return this.templateCalculationService.calculateTemplateTotal(templateId);
    }
    async getTemplateCalculationSummary(templateId) {
        return this.templateCalculationService.getCalculationSummary(templateId);
    }
};
exports.TemplatesService = TemplatesService;
exports.TemplatesService = TemplatesService = TemplatesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        services_1.TemplateQueryService,
        services_1.TemplateCreationService,
        services_1.TemplateDetailService,
        services_1.TemplateAdminService,
        services_1.TemplateCalculationService])
], TemplatesService);
//# sourceMappingURL=templates.service.js.map