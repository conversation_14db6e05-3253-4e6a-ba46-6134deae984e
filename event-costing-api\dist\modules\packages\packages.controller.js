"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackagesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesController = void 0;
const common_1 = require("@nestjs/common");
const packages_service_1 = require("./packages.service");
const list_package_variations_dto_1 = require("./dto/list-package-variations.dto");
const list_package_options_dto_1 = require("./dto/list-package-options.dto");
const package_option_detail_dto_1 = require("./dto/package-option-detail.dto");
const batch_package_options_response_dto_1 = require("./dto/batch-package-options-response.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const paginated_response_dto_1 = require("../../shared/dtos/paginated-response.dto");
const swagger_1 = require("@nestjs/swagger");
let PackagesController = PackagesController_1 = class PackagesController {
    packagesService;
    logger = new common_1.Logger(PackagesController_1.name);
    constructor(packagesService) {
        this.packagesService = packagesService;
    }
    async findVariations(queryDto) {
        this.logger.log(`Fetching package variations with query: ${JSON.stringify(queryDto)}`);
        return this.packagesService.findVariations(queryDto);
    }
    async findOptions(packageId, queryDto) {
        this.logger.log(`Fetching options for package ${packageId}, currency ${queryDto.currencyId}`);
        return this.packagesService.findOptions(packageId, queryDto.currencyId);
    }
    async findPackageOptions(packageId, currencyId, venueId) {
        this.logger.log(`Fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''} (direct endpoint)`);
        return this.packagesService.findOptions(packageId, currencyId, venueId);
    }
    async getBatchPackageOptions(packageIds, currencyId, venueId) {
        this.logger.log(`Getting batch options for ${packageIds.length} packages, currency ${currencyId}, venue ${venueId || 'any'}`);
        return this.packagesService.getBatchPackageOptions(packageIds, currencyId, venueId);
    }
};
exports.PackagesController = PackagesController;
__decorate([
    (0, common_1.Get)('variations'),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns a paginated list of package variations',
        type: () => paginated_response_dto_1.PaginatedResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'categoryId',
        required: false,
        type: String,
        description: 'Filter by category ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'cityId',
        required: false,
        type: String,
        description: 'Filter by city ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'venueId',
        required: false,
        type: String,
        description: 'Filter by venue ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'venueIds',
        required: false,
        type: [String],
        isArray: true,
        description: 'Filter by multiple venue IDs',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search term for package name',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        enum: ['name', 'price', 'category'],
        description: 'Field to sort by',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        enum: ['asc', 'desc'],
        description: 'Sort direction',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items to return',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'offset',
        required: false,
        type: Number,
        description: 'Number of items to skip',
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_package_variations_dto_1.ListPackageVariationsDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "findVariations", null);
__decorate([
    (0, common_1.Get)('variations/:packageId/options'),
    (0, swagger_1.ApiParam)({ name: 'packageId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: [package_option_detail_dto_1.PackageOptionDetailDto] }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, list_package_options_dto_1.ListPackageOptionsDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "findOptions", null);
__decorate([
    (0, common_1.Get)(':packageId/options'),
    (0, swagger_1.ApiParam)({ name: 'packageId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: [package_option_detail_dto_1.PackageOptionDetailDto] }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('currencyId')),
    __param(2, (0, common_1.Query)('venueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "findPackageOptions", null);
__decorate([
    (0, common_1.Get)('batch-options'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get options for multiple packages in a single request',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'packageIds',
        type: [String],
        isArray: true,
        description: 'Array of package IDs to get options for',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'currencyId',
        required: true,
        type: String,
        description: 'Currency ID for pricing',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'venueId',
        required: false,
        type: String,
        description: 'Optional venue ID for venue-specific options',
    }),
    (0, swagger_1.ApiOkResponse)({ type: batch_package_options_response_dto_1.BatchPackageOptionsResponseDto }),
    __param(0, (0, common_1.Query)('packageIds', new common_1.ParseArrayPipe({ items: String, separator: ',' }))),
    __param(1, (0, common_1.Query)('currencyId')),
    __param(2, (0, common_1.Query)('venueId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String, String]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "getBatchPackageOptions", null);
exports.PackagesController = PackagesController = PackagesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Packages Catalogue'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('packages'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [packages_service_1.PackagesService])
], PackagesController);
//# sourceMappingURL=packages.controller.js.map