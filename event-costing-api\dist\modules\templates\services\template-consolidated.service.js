"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateConsolidatedService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateConsolidatedService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_detail_service_1 = require("./template-detail.service");
const template_calculation_service_1 = require("./template-calculation.service");
const template_query_service_1 = require("./template-query.service");
const packages_service_1 = require("../../packages/packages.service");
const categories_service_1 = require("../../categories/categories.service");
let TemplateConsolidatedService = TemplateConsolidatedService_1 = class TemplateConsolidatedService {
    supabaseService;
    templateDetailService;
    templateCalculationService;
    templateQueryService;
    packagesService;
    categoriesService;
    logger = new common_1.Logger(TemplateConsolidatedService_1.name);
    constructor(supabaseService, templateDetailService, templateCalculationService, templateQueryService, packagesService, categoriesService) {
        this.supabaseService = supabaseService;
        this.templateDetailService = templateDetailService;
        this.templateCalculationService = templateCalculationService;
        this.templateQueryService = templateQueryService;
        this.packagesService = packagesService;
        this.categoriesService = categoriesService;
    }
    async getTemplateManagementData(filters = {}, user) {
        this.logger.log(`Fetching template management data for user: ${user.email}`);
        const startTime = Date.now();
        try {
            const [templatesResult, categoriesResult, eventTypesResult, packagesResult, summaryResult,] = await Promise.allSettled([
                this.getTemplates(filters, user),
                this.getCategories(),
                this.getEventTypes(),
                this.getAvailablePackages(user),
                this.getTemplateSummary(user),
            ]);
            const templates = this.extractResult(templatesResult, 'templates', {
                data: [],
                totalCount: 0,
                page: 1,
                pageSize: 10,
                totalPages: 0,
            });
            const categories = this.extractResult(categoriesResult, 'categories', []);
            const eventTypes = this.extractResult(eventTypesResult, 'eventTypes', []);
            const packages = this.extractResult(packagesResult, 'packages', []);
            const summaryData = this.extractResult(summaryResult, 'summary', null);
            const summary = summaryData || undefined;
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                templatesResult,
                categoriesResult,
                eventTypesResult,
                packagesResult,
                summaryResult,
            ]);
            const result = {
                templates,
                categories,
                eventTypes,
                packages,
                summary,
                filters: {
                    applied: filters,
                    available: {
                        categories: categories.map(cat => ({ id: cat.id, name: cat.name })),
                        eventTypes: eventTypes.map(et => ({ id: et.id, name: et.name })),
                    },
                },
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                    totalTemplates: templates.totalCount,
                    appliedFilters: Object.keys(filters).length,
                },
            };
            this.logger.log(`Successfully fetched template management data in ${loadTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to fetch template management data`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load template management data. Please try again.');
        }
    }
    async getTemplateDetailData(templateId, user) {
        this.logger.log(`Fetching complete template detail data for ID: ${templateId}`);
        const startTime = Date.now();
        try {
            const [templateResult, packagesResult, calculationResult, categoriesResult,] = await Promise.allSettled([
                this.getTemplateById(templateId, user),
                this.getTemplatePackages(templateId, user),
                this.getTemplateCalculation(templateId, user),
                this.getCategories(),
            ]);
            const template = this.extractResult(templateResult, 'template');
            const packages = this.extractResult(packagesResult, 'packages', []);
            const calculation = this.extractResult(calculationResult, 'calculation', null);
            const categories = this.extractResult(categoriesResult, 'categories', []);
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                templateResult,
                packagesResult,
                calculationResult,
                categoriesResult,
            ]);
            return {
                template,
                packages,
                calculation,
                categories,
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to fetch template detail data for ID: ${templateId}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load template detail data. Please try again.');
        }
    }
    async getTemplates(filters, user) {
        const queryDto = {
            search: filters.search,
            eventType: filters.eventType,
            cityId: filters.cityId,
            categoryId: filters.categoryId,
            dateStart: filters.createdAfter,
            dateEnd: filters.createdBefore,
            sortBy: filters.sortBy,
            sortOrder: filters.sortOrder,
            limit: filters.pageSize || 10,
            offset: ((filters.page || 1) - 1) * (filters.pageSize || 10),
        };
        const result = await this.templateQueryService.findUserTemplates(user, queryDto);
        return {
            data: result.data,
            totalCount: result.count,
            page: filters.page || 1,
            pageSize: filters.pageSize || 10,
            totalPages: Math.ceil(result.count / (filters.pageSize || 10)),
        };
    }
    async getTemplateById(templateId, user) {
        return this.templateDetailService.findOnePublic(templateId);
    }
    async getTemplatePackages(templateId, user) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('template_packages')
            .select('*')
            .eq('template_id', templateId);
        if (error) {
            throw error;
        }
        return data || [];
    }
    async getTemplateCalculation(templateId, user) {
        try {
            return await this.templateCalculationService.calculateTemplateTotal(templateId);
        }
        catch (error) {
            return null;
        }
    }
    async getCategories() {
        return this.categoriesService.findAll();
    }
    async getEventTypes() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('event_types')
            .select('id, name, description')
            .eq('is_deleted', false)
            .order('name');
        if (error) {
            throw error;
        }
        return data || [];
    }
    async getAvailablePackages(user) {
        const queryDto = {
            currencyId: 'default-currency-id',
            limit: 100,
            offset: 0,
        };
        const result = await this.packagesService.findVariations(queryDto);
        return result.data;
    }
    async getTemplateSummary(user) {
        const supabase = this.supabaseService.getClient();
        const { data: statusCounts, error: statusError } = await supabase
            .from('templates')
            .select('is_active')
            .eq('created_by', user.id);
        if (statusError) {
            this.logger.warn('Failed to fetch template status counts:', statusError);
            return null;
        }
        const activeCount = statusCounts?.filter(t => t.is_active).length || 0;
        const inactiveCount = statusCounts?.filter(t => !t.is_active).length || 0;
        return {
            totalTemplates: statusCounts?.length || 0,
            activeTemplates: activeCount,
            inactiveTemplates: inactiveCount,
            publicTemplates: 0,
            privateTemplates: statusCounts?.length || 0,
        };
    }
    extractResult(result, name, defaultValue) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        this.logger.warn(`Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`);
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        if (name === 'template') {
            throw result.reason;
        }
        return [];
    }
    collectErrors(results) {
        return results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason?.message || 'Unknown error');
    }
};
exports.TemplateConsolidatedService = TemplateConsolidatedService;
exports.TemplateConsolidatedService = TemplateConsolidatedService = TemplateConsolidatedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        template_detail_service_1.TemplateDetailService,
        template_calculation_service_1.TemplateCalculationService,
        template_query_service_1.TemplateQueryService,
        packages_service_1.PackagesService,
        categories_service_1.CategoriesService])
], TemplateConsolidatedService);
//# sourceMappingURL=template-consolidated.service.js.map