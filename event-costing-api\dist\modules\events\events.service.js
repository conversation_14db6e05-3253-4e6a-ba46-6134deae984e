"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let EventsService = EventsService_1 = class EventsService {
    supabaseService;
    logger = new common_1.Logger(EventsService_1.name);
    TABLE_NAME = 'events';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    handleSupabaseError(error, context) {
        this.logger.error(`${context}: ${error.message}`, error.stack);
        if (error.code === '23503') {
            throw new common_1.NotFoundException('Referenced client or primary contact not found.');
        }
        throw new common_1.InternalServerErrorException(`Could not ${context}.`);
    }
    async create(createEventDto) {
        this.logger.log(`Creating a new event: ${createEventDto.event_name}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .insert({
            event_name: createEventDto.event_name,
            client_id: createEventDto.client_id,
            event_start_datetime: createEventDto.event_start_datetime,
            event_end_datetime: createEventDto.event_end_datetime,
            status: createEventDto.status,
            venue_details: createEventDto.venue_details,
            primary_contact_id: createEventDto.primary_contact_id,
            notes: createEventDto.notes,
        })
            .select()
            .single();
        if (error) {
            this.handleSupabaseError(error, 'create event');
        }
        if (!data) {
            this.logger.error('Insert operation did not return event data.');
            throw new common_1.InternalServerErrorException('Failed to create event.');
        }
        this.logger.log(`Event created successfully with ID: ${data.id}`);
        return data;
    }
    async findAll(search, status, clientId, contactId) {
        this.logger.log(`Fetching all events ${search ? `matching '${search}'` : ''} ${status ? `with status ${status}` : ''} ${clientId ? `for client ${clientId}` : ''} ${contactId ? `for contact ${contactId}` : ''}`);
        const supabase = this.supabaseService.getClient();
        const selectFields = 'id, event_name, client_id, event_start_datetime, event_end_datetime, status, venue_details, primary_contact_id, notes, created_at, updated_at';
        let query = supabase
            .from(this.TABLE_NAME)
            .select(selectFields)
            .eq('is_deleted', false)
            .order('event_start_datetime', { ascending: true, nullsFirst: false });
        if (search) {
            query = query.or(`event_name.ilike.%${search}%,venue_details.ilike.%${search}%`);
        }
        if (status) {
            query = query.eq('status', status);
        }
        if (clientId) {
            query = query.eq('client_id', clientId);
        }
        if (contactId) {
            query = query.eq('primary_contact_id', contactId);
        }
        const { data, error } = await query.returns();
        if (error) {
            this.handleSupabaseError(error, 'find all events');
        }
        return data || [];
    }
    async findOne(id) {
        this.logger.log(`Fetching event with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const selectString = `
      *,
      clients ( id, client_name, contact_person ),
      profiles ( id, full_name, username )
    `;
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select(selectString)
            .eq('id', id)
            .eq('is_deleted', false)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Event with ID ${id} not found.`);
            }
            this.handleSupabaseError(error, `find event by ID ${id}`);
        }
        if (!data) {
            throw new common_1.NotFoundException(`Event with ID ${id} not found.`);
        }
        return data;
    }
    async update(id, updateEventDto) {
        this.logger.log(`Updating event with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        await this.findOne(id);
        const { error: updateError } = await supabase
            .from(this.TABLE_NAME)
            .update({
            event_name: updateEventDto.event_name,
            client_id: updateEventDto.client_id,
            event_start_datetime: updateEventDto.event_start_datetime,
            event_end_datetime: updateEventDto.event_end_datetime,
            status: updateEventDto.status,
            venue_details: updateEventDto.venue_details,
            primary_contact_id: updateEventDto.primary_contact_id,
            notes: updateEventDto.notes,
        })
            .eq('id', id)
            .eq('is_deleted', false);
        if (updateError) {
            this.handleSupabaseError(updateError, `update event ${id}`);
        }
        const selectString = `
      *,
      clients ( id, client_name, contact_person ),
      profiles ( id, full_name, username )
    `;
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select(selectString)
            .eq('id', id)
            .eq('is_deleted', false)
            .single();
        if (error) {
            this.handleSupabaseError(error, `update event ${id}`);
        }
        if (!data) {
            this.logger.error(`Update operation did not return data for event ${id}`);
            throw new common_1.InternalServerErrorException('Failed to update event.');
        }
        this.logger.log(`Event ${id} updated successfully.`);
        return data;
    }
    async remove(id) {
        this.logger.log(`Soft deleting event with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        await this.findOne(id);
        const { error, count } = await supabase
            .from(this.TABLE_NAME)
            .update({ is_deleted: true, deleted_at: new Date().toISOString() })
            .eq('id', id)
            .eq('is_deleted', false);
        if (error) {
            this.handleSupabaseError(error, `soft delete event ${id}`);
        }
        if (count === 0) {
            this.logger.warn(`Event ${id} not found or already deleted during soft delete attempt.`);
            throw new common_1.NotFoundException(`Event with ID ${id} not found or already deleted.`);
        }
        this.logger.log(`Event ${id} soft deleted successfully.`);
    }
};
exports.EventsService = EventsService;
exports.EventsService = EventsService = EventsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], EventsService);
//# sourceMappingURL=events.service.js.map