"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthEventLoggerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthEventLoggerService = exports.AuthEventSeverity = exports.AuthEventType = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const uuid_1 = require("uuid");
var AuthEventType;
(function (AuthEventType) {
    AuthEventType["LOGIN_ATTEMPT"] = "LOGIN_ATTEMPT";
    AuthEventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    AuthEventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    AuthEventType["LOGOUT"] = "LOGOUT";
    AuthEventType["REGISTRATION"] = "REGISTRATION";
    AuthEventType["TOKEN_REFRESH"] = "TOKEN_REFRESH";
    AuthEventType["TOKEN_REFRESH_FAILURE"] = "TOKEN_REFRESH_FAILURE";
    AuthEventType["PASSWORD_RESET_REQUEST"] = "PASSWORD_RESET_REQUEST";
    AuthEventType["PASSWORD_RESET"] = "PASSWORD_RESET";
    AuthEventType["EMAIL_VERIFICATION"] = "EMAIL_VERIFICATION";
    AuthEventType["SUSPICIOUS_ACTIVITY"] = "SUSPICIOUS_ACTIVITY";
})(AuthEventType || (exports.AuthEventType = AuthEventType = {}));
var AuthEventSeverity;
(function (AuthEventSeverity) {
    AuthEventSeverity["INFO"] = "INFO";
    AuthEventSeverity["WARNING"] = "WARNING";
    AuthEventSeverity["ERROR"] = "ERROR";
    AuthEventSeverity["CRITICAL"] = "CRITICAL";
})(AuthEventSeverity || (exports.AuthEventSeverity = AuthEventSeverity = {}));
let AuthEventLoggerService = AuthEventLoggerService_1 = class AuthEventLoggerService {
    configService;
    logger = new common_1.Logger(AuthEventLoggerService_1.name);
    constructor(configService) {
        this.configService = configService;
    }
    logEvent(eventType, severity, data, correlationId) {
        const eventCorrelationId = correlationId || (0, uuid_1.v4)();
        const event = {
            timestamp: new Date().toISOString(),
            eventType,
            severity,
            correlationId: eventCorrelationId,
            ...data,
        };
        switch (severity) {
            case AuthEventSeverity.INFO:
                this.logger.log(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
                break;
            case AuthEventSeverity.WARNING:
                this.logger.warn(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
                break;
            case AuthEventSeverity.ERROR:
            case AuthEventSeverity.CRITICAL:
                this.logger.error(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
                break;
            default:
                this.logger.log(`Auth Event [${eventType}]: ${JSON.stringify(event)}`);
        }
        this.storeEvent(event);
        return eventCorrelationId;
    }
    logLoginAttempt(data, correlationId) {
        return this.logEvent(AuthEventType.LOGIN_ATTEMPT, AuthEventSeverity.INFO, data, correlationId);
    }
    logLoginSuccess(data, correlationId) {
        return this.logEvent(AuthEventType.LOGIN_SUCCESS, AuthEventSeverity.INFO, {
            ...data,
            result: 'success',
        }, correlationId);
    }
    logLoginFailure(data, correlationId) {
        return this.logEvent(AuthEventType.LOGIN_FAILURE, AuthEventSeverity.WARNING, {
            ...data,
            result: 'failure',
        }, correlationId);
    }
    logLogout(data, correlationId) {
        return this.logEvent(AuthEventType.LOGOUT, AuthEventSeverity.INFO, data, correlationId);
    }
    logTokenRefresh(data, correlationId) {
        return this.logEvent(AuthEventType.TOKEN_REFRESH, AuthEventSeverity.INFO, {
            ...data,
            result: 'success',
        }, correlationId);
    }
    logTokenRefreshFailure(data, correlationId) {
        return this.logEvent(AuthEventType.TOKEN_REFRESH_FAILURE, AuthEventSeverity.WARNING, {
            ...data,
            result: 'failure',
        }, correlationId);
    }
    storeEvent(event) {
        if (this.configService.get('NODE_ENV') === 'production') {
        }
    }
};
exports.AuthEventLoggerService = AuthEventLoggerService;
exports.AuthEventLoggerService = AuthEventLoggerService = AuthEventLoggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AuthEventLoggerService);
//# sourceMappingURL=auth-event-logger.service.js.map