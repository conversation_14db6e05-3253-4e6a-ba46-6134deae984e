import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { AddCustomLineItemDto } from '../dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../dto/update-line-item.dto';
import { CustomItemDto } from '../dto/custom-item.dto';
export declare class CustomItemsService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    getCustomItems(calculationId: string): Promise<CustomItemDto[]>;
    getCustomItemById(calculationId: string, itemId: string): Promise<CustomItemDto>;
    addCustomItem(calcId: string, addDto: AddCustomLineItemDto, user: User): Promise<{
        id: string;
    }>;
    updateCustomItem(calcId: string, itemId: string, updateDto: UpdateLineItemDto, user: User): Promise<CustomItemDto>;
    deleteCustomItem(calcId: string, itemId: string, user: User): Promise<void>;
    private transformToCustomItemDto;
    private checkCalculationOwnership;
    private recalcCalculationViaRpc;
}
