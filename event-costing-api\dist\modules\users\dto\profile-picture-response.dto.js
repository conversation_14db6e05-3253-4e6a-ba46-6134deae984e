"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilePictureResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ProfilePictureResponseDto {
    success;
    message;
    profilePictureUrl;
}
exports.ProfilePictureResponseDto = ProfilePictureResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the operation was successful',
        example: true,
    }),
    __metadata("design:type", Boolean)
], ProfilePictureResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A message describing the result of the operation',
        example: 'Profile picture uploaded successfully',
    }),
    __metadata("design:type", String)
], ProfilePictureResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The URL of the uploaded profile picture',
        example: 'https://example.com/storage/v1/object/public/profiles/profile-pictures/user-id-123456789.jpg',
    }),
    __metadata("design:type", String)
], ProfilePictureResponseDto.prototype, "profilePictureUrl", void 0);
//# sourceMappingURL=profile-picture-response.dto.js.map