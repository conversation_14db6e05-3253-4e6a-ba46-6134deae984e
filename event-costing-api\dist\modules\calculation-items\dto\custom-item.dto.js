"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CustomItemDto {
    id;
    calculation_id;
    item_name;
    description;
    item_quantity;
    item_quantity_basis;
    quantity_basis;
    unit_price;
    unit_cost;
    calculated_total;
    category_id;
    currency_id;
    city_id;
    created_at;
    updated_at;
}
exports.CustomItemDto = CustomItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the custom item',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the calculation this custom item belongs to',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "calculation_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the custom item',
        type: String,
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "item_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Description of the custom item',
        type: String,
        nullable: true,
    }),
    __metadata("design:type", Object)
], CustomItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantity of the custom item',
        type: Number,
    }),
    __metadata("design:type", Number)
], CustomItemDto.prototype, "item_quantity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of days/units this item applies for',
        type: Number,
        nullable: true,
    }),
    __metadata("design:type", Object)
], CustomItemDto.prototype, "item_quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Quantity basis for calculation',
        type: String,
        enum: ['PER_EVENT', 'PER_DAY', 'PER_ATTENDEE', 'PER_ITEM', 'PER_ITEM_PER_DAY', 'PER_ATTENDEE_PER_DAY'],
        nullable: true,
    }),
    __metadata("design:type", Object)
], CustomItemDto.prototype, "quantity_basis", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unit price for the custom item',
        type: Number,
    }),
    __metadata("design:type", Number)
], CustomItemDto.prototype, "unit_price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unit cost for the custom item',
        type: Number,
    }),
    __metadata("design:type", Number)
], CustomItemDto.prototype, "unit_cost", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculated total price (quantity * unit_price)',
        type: Number,
    }),
    __metadata("design:type", Number)
], CustomItemDto.prototype, "calculated_total", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category ID',
        type: String,
        format: 'uuid',
        nullable: true,
    }),
    __metadata("design:type", Object)
], CustomItemDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City ID',
        type: String,
        format: 'uuid',
        nullable: true,
    }),
    __metadata("design:type", Object)
], CustomItemDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], CustomItemDto.prototype, "updated_at", void 0);
//# sourceMappingURL=custom-item.dto.js.map