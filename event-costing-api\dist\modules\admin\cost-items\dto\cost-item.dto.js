"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CostItemDto {
    id;
    item_code;
    name;
    description;
    unit_of_measure;
    default_price;
    currency_id;
    category_id;
    supplier_id;
    is_active;
    created_at;
    updated_at;
    is_deleted;
    deleted_at;
}
exports.CostItemDto = CostItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the cost item',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CostItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique code for the cost item' }),
    __metadata("design:type", String)
], CostItemDto.prototype, "item_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the cost item' }),
    __metadata("design:type", String)
], CostItemDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Detailed description', required: false }),
    __metadata("design:type", String)
], CostItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unit of measure' }),
    __metadata("design:type", String)
], CostItemDto.prototype, "unit_of_measure", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Default price/cost per unit' }),
    __metadata("design:type", Number)
], CostItemDto.prototype, "default_price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the currency for the default price',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CostItemDto.prototype, "currency_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the category this item belongs to',
        format: 'uuid',
        required: false,
    }),
    __metadata("design:type", String)
], CostItemDto.prototype, "category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the default supplier',
        format: 'uuid',
        required: false,
    }),
    __metadata("design:type", String)
], CostItemDto.prototype, "supplier_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the cost item is active' }),
    __metadata("design:type", Boolean)
], CostItemDto.prototype, "is_active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of creation' }),
    __metadata("design:type", Date)
], CostItemDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of last update' }),
    __metadata("design:type", Date)
], CostItemDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Flag indicating soft deletion',
        required: false,
    }),
    __metadata("design:type", Boolean)
], CostItemDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of soft deletion',
        required: false,
        nullable: true,
    }),
    __metadata("design:type", Object)
], CostItemDto.prototype, "deleted_at", void 0);
//# sourceMappingURL=cost-item.dto.js.map