"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageCitiesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCitiesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let PackageCitiesService = PackageCitiesService_1 = class PackageCitiesService {
    supabaseService;
    logger = new common_1.Logger(PackageCitiesService_1.name);
    PACKAGE_CITIES_TABLE = 'package_cities';
    CITIES_TABLE = 'cities';
    PACKAGES_TABLE = 'packages';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async addCityToPackage(packageId, cityId) {
        this.logger.log(`Attempting to add city ${cityId} to package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.PACKAGE_CITIES_TABLE)
            .insert({ package_id: packageId, city_id: cityId })
            .select('id');
        const typedData = data;
        if (error) {
            this.logger.error(`Error adding city ${cityId} to package ${packageId}: ${error.message}`);
            if (error.code === '23505') {
                throw new common_1.ConflictException('This city is already associated with the package.');
            }
            if (error.code === '23503') {
                throw new common_1.NotFoundException('Package or City not found.');
            }
            throw new common_1.InternalServerErrorException('Failed to add city to package.');
        }
        if (!typedData || typedData.length === 0) {
            this.logger.error(`Insert operation did not return the expected data for package ${packageId}, city ${cityId}`);
            throw new common_1.InternalServerErrorException('Failed to retrieve association ID after insert.');
        }
        this.logger.log(`Successfully added city ${cityId} to package ${packageId} with association ID: ${typedData[0].id}`);
        return { id: typedData[0].id };
    }
    async listCitiesForPackage(packageId) {
        this.logger.log(`Fetching cities for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.PACKAGE_CITIES_TABLE)
            .select(`
        city: ${this.CITIES_TABLE} (
          id,
          name
        )
      `)
            .eq('package_id', packageId)
            .returns();
        if (error) {
            this.logger.error(`Error fetching cities for package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Error fetching cities for package.');
        }
        const cities = data
            ?.map(item => item.city)
            .filter((city) => city !== null)
            .map(city => ({ id: city.id, name: city.name })) || [];
        if (cities.length === 0) {
            const { count: packageCount } = await supabase
                .from(this.PACKAGES_TABLE)
                .select('*', { count: 'exact', head: true })
                .eq('id', packageId);
            if (packageCount === 0) {
                throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
            }
        }
        return cities;
    }
    async removeCityFromPackage(packageId, cityId) {
        this.logger.log(`Attempting to remove city ${cityId} from package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.PACKAGE_CITIES_TABLE)
            .delete()
            .eq('package_id', packageId)
            .eq('city_id', cityId);
        if (error) {
            this.logger.error(`Error removing city ${cityId} from package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to remove city from package.');
        }
        if (count === 0) {
            this.logger.warn(`Association between package ${packageId} and city ${cityId} not found for deletion.`);
            throw new common_1.NotFoundException('Package/City association not found.');
        }
        this.logger.log(`Successfully removed city ${cityId} from package ${packageId}`);
    }
};
exports.PackageCitiesService = PackageCitiesService;
exports.PackageCitiesService = PackageCitiesService = PackageCitiesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageCitiesService);
//# sourceMappingURL=package-cities.service.js.map