import { SupabaseService } from '../../../core/supabase/supabase.service';
export declare class CalculationValidationService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    checkCalculationOwnership(calculationId: string, userId: string): Promise<void>;
    validateCalculationExists(calculationId: string): Promise<boolean>;
    canUserAccessCalculation(calculationId: string, userId: string): Promise<boolean>;
    validateCalculationStatus(calculationId: string, allowedStatuses: string[]): Promise<string>;
    getCalculationBasicInfo(calculationId: string): Promise<{
        id: string;
        created_by: string;
        status: string;
        is_deleted: boolean;
    }>;
    validateCalculationAccess(calculationId: string, userId: string, allowedStatuses?: string[]): Promise<{
        status: string;
        created_by: string;
    }>;
    hasLineItems(calculationId: string): Promise<boolean>;
    hasCustomItems(calculationId: string): Promise<boolean>;
    canDeleteCalculation(calculationId: string, userId: string): Promise<boolean>;
}
