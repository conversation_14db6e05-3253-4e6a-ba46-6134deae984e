"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ExportManagementService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportManagementService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const exports_service_1 = require("../exports.service");
const calculations_service_1 = require("../../calculations/calculations.service");
const export_format_enum_1 = require("../enums/export-format.enum");
const export_status_enum_1 = require("../enums/export-status.enum");
let ExportManagementService = ExportManagementService_1 = class ExportManagementService {
    supabaseService;
    exportsService;
    calculationsService;
    logger = new common_1.Logger(ExportManagementService_1.name);
    constructor(supabaseService, exportsService, calculationsService) {
        this.supabaseService = supabaseService;
        this.exportsService = exportsService;
        this.calculationsService = calculationsService;
    }
    async getExportManagementData(filters = {}, user) {
        this.logger.log(`Fetching export management data for user: ${user.email}`);
        const startTime = Date.now();
        try {
            const [exportsResult, calculationsResult, statisticsResult, recentActivityResult,] = await Promise.allSettled([
                this.getUserExports(filters, user),
                this.getUserCalculations(user),
                this.getExportStatistics(user),
                this.getRecentExportActivity(filters.activityDays || 7, user),
            ]);
            const exports = this.extractResult(exportsResult, 'exports', {
                data: [],
                totalCount: 0,
                page: 1,
                pageSize: 10,
                totalPages: 0,
            });
            const calculations = this.extractResult(calculationsResult, 'calculations', []);
            const statistics = this.extractResult(statisticsResult, 'statistics', {
                totalExports: 0,
                completedExports: 0,
                failedExports: 0,
                pendingExports: 0,
                exportsByFormat: {},
                exportsByStatus: {},
            });
            const recentActivity = this.extractResult(recentActivityResult, 'recentActivity', []);
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                exportsResult,
                calculationsResult,
                statisticsResult,
                recentActivityResult,
            ]);
            const result = {
                exports,
                calculations,
                statistics,
                recentActivity,
                supportedFormats: Object.values(export_format_enum_1.ExportFormat),
                filters: {
                    applied: filters,
                    available: {
                        formats: Object.values(export_format_enum_1.ExportFormat),
                        statuses: Object.values(export_status_enum_1.ExportStatus),
                        calculations: calculations.map(calc => ({
                            id: calc.id,
                            name: calc.name,
                        })),
                    },
                },
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                    totalExports: exports.totalCount,
                    appliedFilters: Object.keys(filters).length,
                },
            };
            this.logger.log(`Successfully fetched export management data in ${loadTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to fetch export management data`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load export management data. Please try again.');
        }
    }
    async initiateBatchExports(requests, user) {
        this.logger.log(`User ${user.email} initiating batch export for ${requests.length} items`);
        const results = [];
        for (const request of requests) {
            try {
                const createDto = {
                    calculationId: request.calculationId,
                    format: request.format,
                    recipient: request.recipient,
                };
                const result = await this.exportsService.initiateExport(createDto, user);
                results.push({
                    calculationId: request.calculationId,
                    exportId: result.exportId,
                    status: 'initiated',
                });
            }
            catch (error) {
                this.logger.error(`Failed to initiate export for calculation ${request.calculationId}:`, error);
                results.push({
                    calculationId: request.calculationId,
                    exportId: '',
                    status: 'failed',
                });
            }
        }
        return results;
    }
    async getUserExports(filters, user) {
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from('export_history')
            .select(`
        id,
        calculation_id,
        format,
        status,
        file_url,
        download_url,
        error_message,
        created_at,
        updated_at,
        calculations (
          id,
          name,
          status
        )
      `)
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });
        if (filters.calculationId) {
            query = query.eq('calculation_id', filters.calculationId);
        }
        if (filters.format) {
            query = query.eq('format', filters.format);
        }
        if (filters.status) {
            query = query.eq('status', filters.status);
        }
        if (filters.dateStart) {
            query = query.gte('created_at', filters.dateStart);
        }
        if (filters.dateEnd) {
            query = query.lte('created_at', filters.dateEnd);
        }
        const page = filters.page || 1;
        const pageSize = filters.pageSize || 10;
        const offset = (page - 1) * pageSize;
        query = query.range(offset, offset + pageSize - 1);
        const { data, error, count } = await query;
        if (error) {
            throw error;
        }
        return {
            data: data || [],
            totalCount: count || 0,
            page,
            pageSize,
            totalPages: Math.ceil((count || 0) / pageSize),
        };
    }
    async getUserCalculations(user) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculations')
            .select('id, name, status, created_at')
            .eq('created_by', user.id)
            .eq('status', 'finalized')
            .order('created_at', { ascending: false })
            .limit(100);
        if (error) {
            throw error;
        }
        return data || [];
    }
    async getExportStatistics(user) {
        const supabase = this.supabaseService.getClient();
        const { data: exports, error } = await supabase
            .from('export_history')
            .select('format, status')
            .eq('user_id', user.id);
        if (error) {
            throw error;
        }
        const totalExports = exports?.length || 0;
        const completedExports = exports?.filter(e => e.status === 'completed').length || 0;
        const failedExports = exports?.filter(e => e.status === 'failed').length || 0;
        const pendingExports = exports?.filter(e => ['pending', 'processing'].includes(e.status))
            .length || 0;
        const exportsByFormat = exports?.reduce((acc, exp) => {
            acc[exp.format] = (acc[exp.format] || 0) + 1;
            return acc;
        }, {}) || {};
        const exportsByStatus = exports?.reduce((acc, exp) => {
            acc[exp.status] = (acc[exp.status] || 0) + 1;
            return acc;
        }, {}) || {};
        return {
            totalExports,
            completedExports,
            failedExports,
            pendingExports,
            exportsByFormat,
            exportsByStatus,
        };
    }
    async getRecentExportActivity(days, user) {
        const supabase = this.supabaseService.getClient();
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const cutoffIso = cutoffDate.toISOString();
        const { data, error } = await supabase
            .from('export_history')
            .select(`
        id,
        calculation_id,
        format,
        status,
        created_at,
        calculations (
          name
        )
      `)
            .eq('user_id', user.id)
            .gte('created_at', cutoffIso)
            .order('created_at', { ascending: false })
            .limit(20);
        if (error) {
            throw error;
        }
        return (data || []).map(item => ({
            id: item.id,
            calculationId: item.calculation_id,
            calculationName: item.calculations?.name || 'Unknown',
            format: item.format,
            status: item.status,
            timestamp: item.created_at,
        }));
    }
    extractResult(result, name, defaultValue) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        this.logger.warn(`Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`);
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        return [];
    }
    collectErrors(results) {
        return results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason?.message || 'Unknown error');
    }
};
exports.ExportManagementService = ExportManagementService;
exports.ExportManagementService = ExportManagementService = ExportManagementService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        exports_service_1.ExportsService,
        calculations_service_1.CalculationsService])
], ExportManagementService);
//# sourceMappingURL=export-management.service.js.map