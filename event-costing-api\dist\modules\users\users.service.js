"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let UsersService = UsersService_1 = class UsersService {
    supabaseService;
    logger = new common_1.Logger(UsersService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findMyProfile(user) {
        if (!user || !user.id) {
            this.logger.error('findMyProfile called with invalid user object', user);
            throw new common_1.InternalServerErrorException('Invalid user data received.');
        }
        this.logger.log(`Fetching profile for user ID: ${user.id}`);
        this.logger.debug(`Querying profiles table with user ID: ${user.id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error: profileError } = await supabase
            .from('profiles')
            .select(`
        *,
        roles ( role_name )
      `)
            .eq('id', user.id)
            .maybeSingle();
        if (profileError) {
            this.logger.error(`Failed to fetch profile for user ${user.id}: ${profileError.message}`, profileError.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve user profile.');
        }
        if (!data) {
            this.logger.warn(`Profile not found for user ID: ${user.id}`);
            throw new common_1.NotFoundException(`Profile not found for user.`);
        }
        const profileData = data;
        this.logger.log(`Profile fetched successfully for user ID: ${user.id}`);
        return {
            id: user.id,
            email: user.email,
            lastSignInAt: user.last_sign_in_at,
            createdAt: user.created_at,
            username: profileData.username,
            fullName: profileData.full_name,
            phoneNumber: profileData.phone_number,
            address: profileData.address,
            profilePictureUrl: profileData.profile_picture_url,
            companyName: profileData.company_name,
            preferences: profileData.preferences,
            city: profileData.city,
            role: profileData.roles?.role_name || null,
        };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], UsersService);
//# sourceMappingURL=users.service.js.map