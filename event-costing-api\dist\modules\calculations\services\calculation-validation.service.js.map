{"version": 3, "file": "calculation-validation.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/calculations/services/calculation-validation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAuE;AACvE,8EAA0E;AAOnE,IAAM,4BAA4B,oCAAlC,MAAM,4BAA4B;IAGV;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,8BAA4B,CAAC,IAAI,CAAC,CAAC;IAExE,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAKjE,KAAK,CAAC,yBAAyB,CAC7B,aAAqB,EACrB,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,8CAA8C,aAAa,UAAU,MAAM,EAAE,CAC9E,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;aACxC,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;aACvB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;aACxB,WAAW,EAA0B,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,+CAA+C,QAAQ,eAAe,aAAa,EAAE,CACtF,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uDAAuD,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,EACxF,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAC/B,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,6BAA6B,CAClE,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2DAA2D,aAAa,UAAU,MAAM,EAAE,CAC3F,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,+BAA+B,CACpE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,4CAA4C,aAAa,UAAU,MAAM,EAAE,CAC5E,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,aAAqB;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,IAAI,CAAC;aACZ,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;aACvB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,WAAW,EAAE,CAAC;QAEjB,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAChF,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,aAAqB,EACrB,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,aAAqB,EACrB,eAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,QAAQ,CAAC;aAChB,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;aACvB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAC3E,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,aAAa,CAClD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CACb,uBAAuB,IAAI,CAAC,MAAM,0DAA0D,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACzH,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,aAAqB;QAMjD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,oCAAoC,CAAC;aAC5C,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;aACvB,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6CAA6C,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAC/E,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,aAAa,CAClD,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAC7B,aAAqB,EACrB,MAAc,EACd,eAA0B;QAE1B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QAGpE,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,aAAa,CAClD,CAAC;QACJ,CAAC;QAGD,IAAI,SAAS,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,QAAQ,MAAM,oCAAoC,aAAa,aAAa,SAAS,CAAC,UAAU,EAAE,CACnG,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,+BAA+B,CACpE,CAAC;QACJ,CAAC;QAGD,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CACb,uBAAuB,SAAS,CAAC,MAAM,0DAA0D,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9H,CAAC;QACJ,CAAC;QAED,OAAO;YACL,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,UAAU,EAAE,SAAS,CAAC,UAAU;SACjC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,aAAqB;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACpC,IAAI,CAAC,wBAAwB,CAAC;aAC9B,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;aAC3C,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEvC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6CAA6C,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAC/E,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,aAAqB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACpC,IAAI,CAAC,0BAA0B,CAAC;aAChC,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;aAC3C,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEvC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+CAA+C,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CACjF,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,aAAqB,EACrB,MAAc;QAEd,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAG5D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAGpE,IAAI,SAAS,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2CAA2C,aAAa,YAAY,MAAM,EAAE,CAC7E,CAAC;gBACF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6CAA6C,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAC/E,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAA;AA5QY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,4BAA4B,CA4QxC"}