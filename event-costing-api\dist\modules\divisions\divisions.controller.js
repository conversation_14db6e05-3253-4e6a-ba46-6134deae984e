"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DivisionsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DivisionsController = void 0;
const common_1 = require("@nestjs/common");
const divisions_service_1 = require("./divisions.service");
const division_dto_1 = require("./dto/division.dto");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let DivisionsController = DivisionsController_1 = class DivisionsController {
    divisionsService;
    logger = new common_1.Logger(DivisionsController_1.name);
    constructor(divisionsService) {
        this.divisionsService = divisionsService;
    }
    async getAllDivisions(active) {
        this.logger.log('Request to get all divisions');
        return await this.divisionsService.findAll(active);
    }
    async getDivisionById(id) {
        this.logger.log(`Request to get division with ID: ${id}`);
        return await this.divisionsService.findOneById(id);
    }
};
exports.DivisionsController = DivisionsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all divisions' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'List of all divisions',
        type: [division_dto_1.DivisionDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'active',
        required: false,
        type: Boolean,
        description: 'Filter by active status (true/false)',
    }),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], DivisionsController.prototype, "getAllDivisions", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a division by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Division found',
        type: division_dto_1.DivisionDto,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DivisionsController.prototype, "getDivisionById", null);
exports.DivisionsController = DivisionsController = DivisionsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Divisions'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('divisions'),
    __metadata("design:paramtypes", [divisions_service_1.DivisionsService])
], DivisionsController);
//# sourceMappingURL=divisions.controller.js.map