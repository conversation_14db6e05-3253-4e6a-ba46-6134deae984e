"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminCurrenciesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminCurrenciesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const currencies_service_1 = require("./currencies.service");
const create_currency_dto_1 = require("./dto/create-currency.dto");
const update_currency_dto_1 = require("./dto/update-currency.dto");
const currency_dto_1 = require("./dto/currency.dto");
let AdminCurrenciesController = AdminCurrenciesController_1 = class AdminCurrenciesController {
    currenciesService;
    logger = new common_1.Logger(AdminCurrenciesController_1.name);
    constructor(currenciesService) {
        this.currenciesService = currenciesService;
    }
    async createCurrency(createDto) {
        this.logger.log(`Admin request to create currency: ${createDto.code}`);
        return await this.currenciesService.createCurrency(createDto);
    }
    async findOne(id) {
        this.logger.log(`Admin request to get currency ID: ${id}`);
        return await this.currenciesService.findOneById(id);
    }
    async updateCurrency(id, updateDto) {
        this.logger.log(`Admin request to update currency ID: ${id}`);
        return await this.currenciesService.updateCurrency(id, updateDto);
    }
    async deleteCurrency(id) {
        this.logger.log(`Admin request to delete currency ID: ${id}`);
        await this.currenciesService.deleteCurrency(id);
    }
};
exports.AdminCurrenciesController = AdminCurrenciesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new currency' }),
    (0, swagger_1.ApiBody)({ type: create_currency_dto_1.CreateCurrencyDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Currency created',
        type: currency_dto_1.CurrencyDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (Code exists)' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_currency_dto_1.CreateCurrencyDto]),
    __metadata("design:returntype", Promise)
], AdminCurrenciesController.prototype, "createCurrency", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific currency by ID (Admin)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Currency found',
        type: currency_dto_1.CurrencyDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminCurrenciesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a currency' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: update_currency_dto_1.UpdateCurrencyDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Currency updated',
        type: currency_dto_1.CurrencyDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_currency_dto_1.UpdateCurrencyDto]),
    __metadata("design:returntype", Promise)
], AdminCurrenciesController.prototype, "updateCurrency", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a currency' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Currency deleted' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (Currency in use)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminCurrenciesController.prototype, "deleteCurrency", null);
exports.AdminCurrenciesController = AdminCurrenciesController = AdminCurrenciesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Currencies'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/currencies'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __metadata("design:paramtypes", [currencies_service_1.CurrenciesService])
], AdminCurrenciesController);
//# sourceMappingURL=admin-currencies.controller.js.map