"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StorageController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageController = void 0;
const common_1 = require("@nestjs/common");
const storage_service_1 = require("./storage.service");
const jwt_auth_guard_1 = require("../../modules/auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../../modules/auth/decorators/get-current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const supabase_service_1 = require("../supabase/supabase.service");
let StorageController = StorageController_1 = class StorageController {
    storageService;
    supabaseService;
    logger = new common_1.Logger(StorageController_1.name);
    constructor(storageService, supabaseService) {
        this.storageService = storageService;
        this.supabaseService = supabaseService;
    }
    async getFileMetadata(bucket, filePath, user) {
        this.logger.log(`User ${user.id} requesting file metadata for ${bucket}/${filePath}`);
        try {
            await this.checkFileAccess(user, bucket, filePath);
            const metadata = await this.storageService.getFileMetadata(bucket, filePath);
            if (!metadata) {
                throw new common_1.NotFoundException('File not found or inaccessible');
            }
            return metadata;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Unexpected error getting file metadata for ${bucket}/${filePath}: ${errorMessage}`, errorStack);
            throw new common_1.InternalServerErrorException('Failed to get file metadata');
        }
    }
    async listFiles(bucket, path = '', user) {
        this.logger.log(`User ${user.id} requesting file listing for ${bucket}/${path}`);
        try {
            await this.checkFileAccess(user, bucket, path);
            return await this.storageService.listFiles(bucket, path);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Unexpected error listing files in ${bucket}/${path}: ${errorMessage}`, errorStack);
            throw new common_1.InternalServerErrorException('Failed to list files');
        }
    }
    async downloadFile(bucket, filePath, download, user, res) {
        this.logger.log(`User ${user.id} requesting file download from ${bucket}/${filePath}`);
        try {
            await this.checkFileAccess(user, bucket, filePath);
            const supabase = this.supabaseService.getClient();
            const { data, error } = await supabase.storage
                .from(bucket)
                .download(filePath);
            if (error || !data) {
                this.logger.error(`Error downloading file ${bucket}/${filePath}: ${error?.message}`);
                throw new common_1.NotFoundException('File not found or inaccessible');
            }
            const contentType = data.type || 'application/octet-stream';
            res.setHeader('Content-Type', contentType);
            if (download) {
                const fileName = filePath.split('/').pop() || 'download';
                res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
            }
            const arrayBuffer = await data.arrayBuffer();
            const buffer = Buffer.from(arrayBuffer);
            res.send(buffer);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            this.logger.error(`Unexpected error downloading file ${bucket}/${filePath}: ${errorMessage}`, errorStack);
            throw new common_1.InternalServerErrorException('Failed to download file');
        }
    }
    async checkFileAccess(user, bucket, filePath) {
        const publicBuckets = ['public', 'templates'];
        if (publicBuckets.includes(bucket)) {
            return;
        }
        if (bucket === 'profiles') {
            if (filePath.startsWith(`${user.id}/`) || filePath === user.id) {
                return;
            }
            throw new common_1.BadRequestException('You do not have access to this file');
        }
        return;
    }
};
exports.StorageController = StorageController;
__decorate([
    (0, common_1.Get)('download/:bucket/*filePath'),
    (0, swagger_1.ApiOperation)({ summary: 'Download a file from storage' }),
    (0, swagger_1.ApiParam)({
        name: 'bucket',
        description: 'Storage bucket name',
        required: true,
    }),
    (0, swagger_1.ApiParam)({
        name: 'filePath',
        description: 'File path within the bucket',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'download',
        description: 'Whether to download the file or view it in browser',
        required: false,
        type: Boolean,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File downloaded successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'File not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Unauthorized access',
    }),
    (0, common_1.Get)('metadata/:bucket/*filePath'),
    (0, swagger_1.ApiOperation)({ summary: 'Get metadata for a file' }),
    (0, swagger_1.ApiParam)({
        name: 'bucket',
        description: 'Storage bucket name',
        required: true,
    }),
    (0, swagger_1.ApiParam)({
        name: 'filePath',
        description: 'File path within the bucket',
        required: true,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File metadata retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'File not found',
    }),
    __param(0, (0, common_1.Param)('bucket')),
    __param(1, (0, common_1.Param)('filePath')),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], StorageController.prototype, "getFileMetadata", null);
__decorate([
    (0, common_1.Get)('list/:bucket'),
    (0, swagger_1.ApiOperation)({ summary: 'List files in a bucket or folder' }),
    (0, swagger_1.ApiParam)({
        name: 'bucket',
        description: 'Storage bucket name',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'path',
        description: 'Folder path within the bucket',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Files listed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Unauthorized access',
    }),
    __param(0, (0, common_1.Param)('bucket')),
    __param(1, (0, common_1.Query)('path')),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], StorageController.prototype, "listFiles", null);
__decorate([
    __param(0, (0, common_1.Param)('bucket')),
    __param(1, (0, common_1.Param)('filePath')),
    __param(2, (0, common_1.Query)('download')),
    __param(3, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __param(4, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean, Object, Object]),
    __metadata("design:returntype", Promise)
], StorageController.prototype, "downloadFile", null);
exports.StorageController = StorageController = StorageController_1 = __decorate([
    (0, swagger_1.ApiTags)('Storage'),
    (0, common_1.Controller)('storage'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [storage_service_1.StorageService,
        supabase_service_1.SupabaseService])
], StorageController);
//# sourceMappingURL=storage.controller.js.map