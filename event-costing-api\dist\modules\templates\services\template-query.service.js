"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateQueryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateQueryService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_venue_service_1 = require("./template-venue.service");
const template_constants_1 = require("../constants/template.constants");
let TemplateQueryService = TemplateQueryService_1 = class TemplateQueryService {
    supabaseService;
    templateVenueService;
    logger = new common_1.Logger(TemplateQueryService_1.name);
    constructor(supabaseService, templateVenueService) {
        this.supabaseService = supabaseService;
        this.templateVenueService = templateVenueService;
    }
    async findUserTemplates(user, queryDto) {
        this.logger.log(`Finding templates for user ${user.id} with query: ${JSON.stringify(queryDto)}`);
        const supabase = this.supabaseService.getClient();
        const rpcParams = {
            p_user_id: user.id,
            p_search: queryDto.search,
            p_event_type_id: queryDto.eventType,
            p_city_id: queryDto.cityId,
            p_category_id: queryDto.categoryId,
            p_date_start: queryDto.dateStart,
            p_date_end: queryDto.dateEnd,
            p_sort_by: queryDto.sortBy,
            p_sort_order: queryDto.sortOrder,
            p_limit: queryDto.limit,
            p_offset: queryDto.offset,
        };
        const result = await supabase.rpc('get_user_accessible_templates', rpcParams);
        const data = result.data;
        const error = result.error;
        if (error) {
            this.logger.error(`Error calling get_user_accessible_templates for user ${user.id}: ${error.message}`, error.stack);
            return { data: [], count: 0 };
        }
        if (!data) {
            this.logger.warn(`get_user_accessible_templates for user ${user.id} returned null or undefined data.`);
            return { data: [], count: 0 };
        }
        const totalCount = data.length > 0 ? data[0].total_count || 0 : 0;
        const templates = data.map((raw) => {
            return {
                id: raw.id,
                name: raw.name,
                description: raw.description ?? undefined,
                event_type_id: raw.event_type_id ?? undefined,
                city_id: raw.city_id ?? undefined,
                attendees: raw.attendees ?? undefined,
                template_start_date: raw.template_start_date
                    ? new Date(raw.template_start_date)
                    : undefined,
                template_end_date: raw.template_end_date
                    ? new Date(raw.template_end_date)
                    : undefined,
                category_id: raw.category_id ?? undefined,
                created_at: new Date(raw.created_at),
                updated_at: new Date(raw.updated_at),
                created_by: raw.created_by,
                is_public: raw.is_public,
                is_deleted: raw.is_deleted || false,
            };
        });
        await this.templateVenueService.addVenueIdsToTemplates(templates);
        return { data: templates, count: totalCount };
    }
    async findPublicTemplates(queryDto) {
        this.logger.log(`Finding public templates with query: ${JSON.stringify(queryDto)}`);
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .select(template_constants_1.TemplateConstants.SUMMARY_SELECT_FIELDS, { count: 'exact' })
            .eq('is_public', true)
            .eq('is_deleted', false);
        if (queryDto.search) {
            query = query.ilike('name', `%${queryDto.search}%`);
        }
        if (queryDto.eventType) {
            query = query.eq('event_type_id', queryDto.eventType);
        }
        if (queryDto.cityId) {
            query = query.eq('city_id', queryDto.cityId);
        }
        if (queryDto.categoryId) {
            query = query.eq('category_id', queryDto.categoryId);
        }
        if (queryDto.dateStart) {
            query = query.gte('template_start_date', queryDto.dateStart);
        }
        if (queryDto.dateEnd) {
            query = query.lte('template_start_date', queryDto.dateEnd);
        }
        const sortBy = queryDto.sortBy || 'created_at';
        const sortOrder = queryDto.sortOrder || 'desc';
        const validSortColumns = [
            'name',
            'created_at',
            'updated_at',
            'template_start_date',
        ];
        const dbSortColumn = validSortColumns.includes(sortBy)
            ? sortBy
            : 'created_at';
        query = query.order(dbSortColumn, {
            ascending: sortOrder === 'asc',
            nullsFirst: false,
        });
        const limit = queryDto.limit || 20;
        const offset = queryDto.offset || 0;
        query = query.range(offset, offset + limit - 1);
        const { data, error, count } = await query;
        if (error) {
            this.logger.error(`Error fetching public templates: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve public templates.');
        }
        const templates = (data || []).map((raw) => ({
            ...raw,
            template_start_date: raw.template_start_date
                ? new Date(raw.template_start_date)
                : undefined,
            template_end_date: raw.template_end_date
                ? new Date(raw.template_end_date)
                : undefined,
            created_at: new Date(raw.created_at),
            updated_at: new Date(raw.updated_at),
            is_deleted: raw.is_deleted || false,
        }));
        await this.templateVenueService.addVenueIdsToTemplates(templates);
        return { data: templates, count: count || 0 };
    }
};
exports.TemplateQueryService = TemplateQueryService;
exports.TemplateQueryService = TemplateQueryService = TemplateQueryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        template_venue_service_1.TemplateVenueService])
], TemplateQueryService);
//# sourceMappingURL=template-query.service.js.map