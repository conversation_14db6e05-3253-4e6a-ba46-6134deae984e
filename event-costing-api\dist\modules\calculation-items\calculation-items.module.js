"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationItemsModule = void 0;
const common_1 = require("@nestjs/common");
const calculation_items_service_1 = require("./calculation-items.service");
const calculation_items_controller_1 = require("./calculation-items.controller");
const custom_items_controller_1 = require("./controllers/custom-items.controller");
const custom_items_service_1 = require("./services/custom-items.service");
const calculations_module_1 = require("../calculations/calculations.module");
const auth_module_1 = require("../auth/auth.module");
let CalculationItemsModule = class CalculationItemsModule {
};
exports.CalculationItemsModule = CalculationItemsModule;
exports.CalculationItemsModule = CalculationItemsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            (0, common_1.forwardRef)(() => calculations_module_1.CalculationsModule),
        ],
        controllers: [calculation_items_controller_1.CalculationItemsController, custom_items_controller_1.CustomItemsController],
        providers: [calculation_items_service_1.CalculationItemsService, custom_items_service_1.CustomItemsService],
        exports: [calculation_items_service_1.CalculationItemsService, custom_items_service_1.CustomItemsService],
    })
], CalculationItemsModule);
//# sourceMappingURL=calculation-items.module.js.map