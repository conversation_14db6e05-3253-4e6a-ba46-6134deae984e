export declare class LineItemDto {
    id: string;
    calculation_id: string;
    package_id: string | null;
    item_name: string;
    item_name_snapshot: string | null;
    description: string | null;
    notes: string | null;
    quantity: number;
    item_quantity: number | null;
    item_quantity_basis: number | null;
    duration_days: number | null;
    quantity_basis: string | null;
    unit_price: number | null;
    unit_base_price: number | null;
    calculated_line_total: number | null;
    category_id: string | null;
    is_custom: boolean;
    options?: {
        option_id: string;
        option_name?: string;
    }[] | null;
    created_at: string;
    updated_at: string;
}
