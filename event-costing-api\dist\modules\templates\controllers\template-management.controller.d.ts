import { TemplateConsolidatedService } from '../services/template-consolidated.service';
import { TemplateCompleteDataDto } from '../dto/template-complete-data.dto';
import { TemplateFiltersDto } from '../dto/template-filters.dto';
import { User } from '@supabase/supabase-js';
export declare class TemplateManagementController {
    private readonly templateConsolidatedService;
    private readonly logger;
    constructor(templateConsolidatedService: TemplateConsolidatedService);
    getManagementData(filters: TemplateFiltersDto, user: User): Promise<TemplateCompleteDataDto>;
    getTemplateDetailData(id: string, user: User): Promise<any>;
    getManagementSummary(user: User): Promise<any>;
}
