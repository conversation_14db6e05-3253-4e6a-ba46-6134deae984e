import { CalculationDetailDto } from '../../calculations/dto/calculation-detail.dto';
import { TransformedExportData } from '../interfaces/transformed-export-data.interface';
export declare class ExportGenerationService {
    private readonly logger;
    constructor();
    transformCalculationData(calculationData: CalculationDetailDto): TransformedExportData;
    generateXlsxBuffer(data: TransformedExportData): Promise<Buffer>;
    generateCsvBuffer(data: TransformedExportData): Promise<Buffer>;
    generatePdfToFile(data: TransformedExportData, filePath: string): Promise<void>;
}
