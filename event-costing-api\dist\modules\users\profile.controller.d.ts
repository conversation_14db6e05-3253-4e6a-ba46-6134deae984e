import { User } from '@supabase/supabase-js';
import { StorageService } from '../../core/storage/storage.service';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { ProfilePictureResponseDto } from './dto/profile-picture-response.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
export declare class ProfileController {
    private readonly storageService;
    private readonly supabaseService;
    private readonly logger;
    private readonly BUCKET_NAME;
    constructor(storageService: StorageService, supabaseService: SupabaseService);
    uploadProfilePicture(file: Express.Multer.File, user: User): Promise<ProfilePictureResponseDto>;
    updateProfile(updateProfileDto: UpdateProfileDto, user: User): Promise<{
        success: boolean;
        message: string;
        updatedFields?: string[];
        profile?: any;
    }>;
}
