import { CalculationDetailDto } from '../dto/calculation-detail.dto';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';
import { CalculationHistoryRaw } from '../interfaces/calculation-internal.interfaces';
export declare class CalculationTransformationService {
    private readonly logger;
    mapRawToDetailDto(raw: CalculationHistoryRaw, venues?: VenueReferenceDto[]): Promise<CalculationDetailDto>;
    private mapLineItems;
    private mapLineItemOptions;
    private mapCustomItems;
    mapCalculationSummary(calculation: any): any;
    transformVenueData(venues: any[]): VenueReferenceDto[];
    validateRawData(raw: any): boolean;
    sanitizeRawData(raw: any): CalculationHistoryRaw;
    transformForExport(calculation: CalculationDetailDto): any;
}
