import { User } from '@supabase/supabase-js';
import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListTemplatesDto } from './dto/list-templates.dto';
import { PaginatedTemplatesResponse, TemplateSummaryDto, PaginatedAdminTemplatesResponse, TemplateDetailDto, EnhancedTemplateDetailDto } from './dto/template-summary.dto';
import { CreateTemplateFromCalculationDto } from './dto/create-template-from-calculation.dto';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { TemplateCalculationResultDto, TemplateCalculationSummaryDto } from './dto/template-calculation.dto';
import { ListAdminTemplatesQueryDto } from './dto/list-admin-templates.dto';
import { TemplateQueryService, TemplateCreationService, TemplateDetailService, TemplateAdminService, TemplateCalculationService } from './services';
export declare class TemplatesService {
    private readonly supabaseService;
    private readonly templateQueryService;
    private readonly templateCreationService;
    private readonly templateDetailService;
    private readonly templateAdminService;
    private readonly templateCalculationService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateQueryService: TemplateQueryService, templateCreationService: TemplateCreationService, templateDetailService: TemplateDetailService, templateAdminService: TemplateAdminService, templateCalculationService: TemplateCalculationService);
    findUserTemplates(user: User, queryDto: ListTemplatesDto): Promise<PaginatedTemplatesResponse>;
    createTemplate(createDto: CreateTemplateDto, user: User): Promise<TemplateSummaryDto>;
    createTemplateFromCalculation(createDto: CreateTemplateFromCalculationDto, user: User): Promise<TemplateSummaryDto>;
    findPublicTemplates(queryDto: ListTemplatesDto): Promise<PaginatedTemplatesResponse>;
    findOnePublic(id: string): Promise<TemplateDetailDto>;
    findOnePublicEnhanced(id: string): Promise<EnhancedTemplateDetailDto>;
    findAllAdmin(queryDto: ListAdminTemplatesQueryDto): Promise<PaginatedAdminTemplatesResponse>;
    findOneAdmin(id: string): Promise<TemplateDetailDto>;
    updateTemplate(id: string, updateDto: UpdateTemplateDto): Promise<TemplateDetailDto>;
    updateTemplateStatus(id: string, isActive: boolean): Promise<TemplateDetailDto>;
    deleteTemplate(id: string): Promise<void>;
    calculateTemplateTotal(templateId: string): Promise<TemplateCalculationResultDto>;
    getTemplateCalculationSummary(templateId: string): Promise<TemplateCalculationSummaryDto>;
}
