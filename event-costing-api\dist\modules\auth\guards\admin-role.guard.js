"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AdminRoleGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminRoleGuard = void 0;
const common_1 = require("@nestjs/common");
let AdminRoleGuard = AdminRoleGuard_1 = class AdminRoleGuard {
    logger = new common_1.Logger(AdminRoleGuard_1.name);
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            this.logger.warn('AdminRoleGuard requires a user object on the request. Ensure JwtAuthGuard runs first.');
            throw new common_1.ForbiddenException('Access Denied: User context not available.');
        }
        const roleName = user.profile?.roles?.role_name;
        this.logger.debug(`Checking admin role for user ${user.id}. Role found: ${roleName ?? '[No Role Found]'}`);
        if (roleName === 'admin') {
            return true;
        }
        this.logger.warn(`User ${user.id} denied access. Role: ${roleName ?? '[No Role Found]'}`);
        throw new common_1.ForbiddenException('Access Denied: Requires admin privileges.');
    }
};
exports.AdminRoleGuard = AdminRoleGuard;
exports.AdminRoleGuard = AdminRoleGuard = AdminRoleGuard_1 = __decorate([
    (0, common_1.Injectable)()
], AdminRoleGuard);
//# sourceMappingURL=admin-role.guard.js.map