export declare class AdminVenueDto {
    id: string;
    name: string;
    description: string | null;
    address: string | null;
    city_id: string | null;
    city_name?: string;
    classification?: string | null;
    capacity?: number | null;
    image_url?: string | null;
    features?: string[] | null;
    is_active: boolean;
    is_deleted: boolean;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
}
export declare class CreateVenueDto {
    name: string;
    description?: string | null;
    address?: string | null;
    city_id?: string | null;
    is_active?: boolean;
    classification?: string | null;
    capacity?: number | null;
    image_url?: string | null;
    features?: string[] | null;
}
export declare class UpdateVenueDto {
    name?: string;
    description?: string | null;
    address?: string | null;
    city_id?: string | null;
    is_active?: boolean;
    classification?: string | null;
    capacity?: number | null;
    image_url?: string | null;
    features?: string[] | null;
}
export declare class PaginatedVenuesResponse {
    data: AdminVenueDto[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
export declare class ListVenuesQueryDto {
    search?: string;
    cityId?: string;
    showDeleted?: boolean;
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    classification?: string;
    minCapacity?: number;
    maxCapacity?: number;
}
