import { SupabaseService } from 'src/core/supabase/supabase.service';
import { TemplateSummaryDto, TemplateDetailDto } from '../dto/template-summary.dto';
export declare class TemplateVenueService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    addVenueIdsToTemplates(templates: TemplateSummaryDto[] | TemplateDetailDto[]): Promise<void>;
    addVenueIdsToTemplate(template: TemplateSummaryDto | TemplateDetailDto): Promise<void>;
    createTemplateVenueAssociations(templateId: string, venueIds: string[]): Promise<void>;
}
