import { PackagesService } from './packages.service';
import { ListPackageVariationsDto } from './dto/list-package-variations.dto';
import { PackageVariationDto } from './dto/package-variation.dto';
import { ListPackageOptionsDto } from './dto/list-package-options.dto';
import { PackageOptionDetailDto } from './dto/package-option-detail.dto';
import { BatchPackageOptionsResponseDto } from './dto/batch-package-options-response.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
export declare class PackagesController {
    private readonly packagesService;
    private readonly logger;
    constructor(packagesService: PackagesService);
    findVariations(queryDto: ListPackageVariationsDto): Promise<PaginatedResponseDto<PackageVariationDto>>;
    findOptions(packageId: string, queryDto: ListPackageOptionsDto): Promise<PackageOptionDetailDto[]>;
    findPackageOptions(packageId: string, currencyId?: string, venueId?: string): Promise<PackageOptionDetailDto[]>;
    getBatchPackageOptions(packageIds: string[], currencyId: string, venueId?: string): Promise<BatchPackageOptionsResponseDto>;
}
