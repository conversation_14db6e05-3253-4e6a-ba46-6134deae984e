"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateCalculationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateCalculationService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_constants_1 = require("../constants/template.constants");
let TemplateCalculationService = TemplateCalculationService_1 = class TemplateCalculationService {
    supabaseService;
    logger = new common_1.Logger(TemplateCalculationService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async calculateTemplateTotal(templateId) {
        this.logger.log(`Calculating template total for ID: ${templateId}`);
        const supabase = this.supabaseService.getClient();
        const { data: template, error: templateError } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .select('id, name, attendees, package_selections')
            .eq('id', templateId)
            .eq('is_deleted', false)
            .single();
        if (templateError) {
            this.logger.error(`Error fetching template ${templateId}: ${templateError.message}`, templateError.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch template details.');
        }
        if (!template) {
            throw new common_1.NotFoundException(`Template with ID ${templateId} not found.`);
        }
        const result = {
            packagesTotal: 0,
            customItemsTotal: 0,
            grandTotal: 0,
            breakdown: [],
            currency: 'IDR',
            hasValidPrices: true,
            missingPrices: [],
            totalCost: 0,
            estimatedProfit: 0,
        };
        const packageSelections = template.package_selections || [];
        if (packageSelections.length === 0) {
            this.logger.log('Template has no package selections');
            return result;
        }
        const packageIds = packageSelections.map(selection => selection.package_id);
        if (packageIds.length === 0) {
            this.logger.log('No package IDs found in selections');
            return result;
        }
        const { data: packages, error: packagesError } = await supabase
            .rpc('get_packages_with_prices', { package_ids: packageIds });
        if (packagesError) {
            this.logger.error('Error fetching packages:', packagesError);
            throw new common_1.InternalServerErrorException('Failed to fetch package details');
        }
        if (!packages || packages.length === 0) {
            this.logger.log('No packages found for the given IDs');
            return result;
        }
        this.logger.log(`Fetched ${packages.length} packages`);
        for (const selection of packageSelections) {
            const rpcPackageData = packages.find(pkg => pkg.id === selection.package_id);
            if (!rpcPackageData) {
                this.logger.warn(`Package not found: ${selection.package_id}`);
                result.missingPrices.push(`Package ${selection.package_id} not found`);
                result.hasValidPrices = false;
                continue;
            }
            const packagePrices = Array.isArray(rpcPackageData.package_prices)
                ? rpcPackageData.package_prices
                : [];
            const packageData = {
                ...rpcPackageData,
                package_prices: packagePrices,
            };
            let bestPrice = undefined;
            if (packageData.package_prices && packageData.package_prices.length > 0) {
                bestPrice = packageData.package_prices.find(price => price.currencies?.code === 'IDR');
                if (!bestPrice) {
                    bestPrice = packageData.package_prices[0];
                }
            }
            if (!bestPrice || !bestPrice.price) {
                this.logger.warn(`No price found for package: ${packageData.name}`);
                result.missingPrices.push(`No price for ${packageData.name}`);
                result.hasValidPrices = false;
                continue;
            }
            let quantity = 1;
            if (template.attendees && packageData.quantity_basis) {
                switch (packageData.quantity_basis.toLowerCase()) {
                    case 'per person':
                    case 'per attendee':
                        quantity = template.attendees;
                        break;
                    case 'per 10 people':
                        quantity = Math.ceil(template.attendees / 10);
                        break;
                    case 'per 100 people':
                        quantity = Math.ceil(template.attendees / 100);
                        break;
                    case 'per event':
                    case 'fixed':
                    default:
                        quantity = 1;
                        break;
                }
            }
            const unitPrice = bestPrice.price;
            const totalPrice = unitPrice * quantity;
            const unitCost = bestPrice.unit_base_cost || 0;
            const totalCost = unitCost * quantity;
            const breakdownItem = {
                packageId: packageData.id,
                packageName: packageData.name,
                quantity,
                unitPrice,
                totalPrice,
                currency: bestPrice.currencies?.code || 'IDR',
                unitCost,
                totalCost,
            };
            result.breakdown.push(breakdownItem);
            result.packagesTotal += totalPrice;
            result.totalCost = (result.totalCost || 0) + totalCost;
            if (result.breakdown.length === 1 || bestPrice.currencies?.code === 'IDR') {
                result.currency = bestPrice.currencies?.code || 'IDR';
            }
        }
        result.grandTotal = result.packagesTotal + result.customItemsTotal;
        result.estimatedProfit = result.grandTotal - (result.totalCost || 0);
        this.logger.log(`Template calculation result: ${JSON.stringify({
            templateId,
            packagesTotal: result.packagesTotal,
            grandTotal: result.grandTotal,
            totalCost: result.totalCost,
            estimatedProfit: result.estimatedProfit,
            currency: result.currency,
            hasValidPrices: result.hasValidPrices,
            missingPricesCount: result.missingPrices.length,
        })}`);
        return result;
    }
    async getCalculationSummary(templateId) {
        const result = await this.calculateTemplateTotal(templateId);
        const summary = {
            totalPackages: result.breakdown.length,
            totalValue: result.grandTotal,
            currency: result.currency,
            hasValidPrices: result.hasValidPrices,
            missingPricesCount: result.missingPrices.length,
            averagePackageValue: result.breakdown.length > 0
                ? result.packagesTotal / result.breakdown.length
                : 0,
            totalCost: result.totalCost,
            profitMarginPercentage: result.grandTotal > 0 && result.estimatedProfit !== undefined
                ? Math.round((result.estimatedProfit / result.grandTotal) * 100)
                : 0,
        };
        return summary;
    }
};
exports.TemplateCalculationService = TemplateCalculationService;
exports.TemplateCalculationService = TemplateCalculationService = TemplateCalculationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], TemplateCalculationService);
//# sourceMappingURL=template-calculation.service.js.map