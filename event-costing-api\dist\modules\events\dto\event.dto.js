"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class EventDto {
    id;
    event_name;
    event_start_datetime;
    event_end_datetime;
    location;
    notes;
    created_at;
}
exports.EventDto = EventDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'uuid' }),
    __metadata("design:type", String)
], EventDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], EventDto.prototype, "event_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true, format: 'date-time' }),
    __metadata("design:type", Object)
], EventDto.prototype, "event_start_datetime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true, format: 'date-time' }),
    __metadata("design:type", Object)
], EventDto.prototype, "event_end_datetime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Object)
], EventDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ nullable: true }),
    __metadata("design:type", Object)
], EventDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, format: 'date-time' }),
    __metadata("design:type", String)
], EventDto.prototype, "created_at", void 0);
//# sourceMappingURL=event.dto.js.map