"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageOptionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOptionsService = exports.UpdatePackageOptionDto = exports.PackageOptionListQueryDto = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const supabase_js_1 = require("@supabase/supabase-js");
const create_package_option_dto_js_1 = require("./dto/create-package-option.dto.js");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class PackageOptionListQueryDto {
    limit = 10;
    offset = 0;
}
exports.PackageOptionListQueryDto = PackageOptionListQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], PackageOptionListQueryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], PackageOptionListQueryDto.prototype, "offset", void 0);
class UpdatePackageOptionDto extends (0, swagger_1.PartialType)(create_package_option_dto_js_1.CreatePackageOptionDto) {
}
exports.UpdatePackageOptionDto = UpdatePackageOptionDto;
let PackageOptionsService = PackageOptionsService_1 = class PackageOptionsService {
    supabaseService;
    logger = new common_1.Logger(PackageOptionsService_1.name);
    TABLE_NAME = 'package_options';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async _checkPackageExists(packageId) {
        const supabase = this.supabaseService.getClient();
        const { error: pkgError, count } = await supabase
            .from('packages')
            .select('id', { count: 'exact', head: true })
            .eq('id', packageId)
            .eq('is_deleted', false);
        if (pkgError) {
            this.logger.error(`Error checking package existence for ID ${packageId}: ${pkgError.message}`, pkgError.stack);
            throw new common_1.InternalServerErrorException('Failed to verify package existence.');
        }
        if (count === 0) {
            throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
        }
    }
    async _checkOptionUniqueness(packageId, optionCode, currencyId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select('id')
            .eq('applicable_package_id', packageId)
            .eq('option_code', optionCode)
            .eq('currency_id', currencyId)
            .single();
        if (data) {
            throw new common_1.ConflictException(`Option with code '${optionCode}' already exists for this package and currency combination.`);
        }
        if (error && error.code !== 'PGRST116') {
            this.logger.error(`Error checking option uniqueness: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to check option uniqueness.');
        }
    }
    async create(packageId, createDto) {
        this.logger.log(`Attempting to create option '${createDto.option_code}' for package ${packageId}`);
        await this._checkPackageExists(packageId);
        await this._checkOptionUniqueness(packageId, createDto.option_code, createDto.currency_id);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .insert({
            applicable_package_id: packageId,
            ...createDto,
        })
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error creating option for package ${packageId}: ${error.message}`, error.stack);
            if (error instanceof supabase_js_1.PostgrestError) {
                if (error.code === '23505') {
                    throw new common_1.ConflictException(`Option with code '${createDto.option_code}' already exists for this package and currency combination.`);
                }
                if (error.code === '23503') {
                    if (error.details?.includes('currency_id')) {
                        throw new common_1.NotFoundException(`Currency with ID ${createDto.currency_id} not found.`);
                    }
                    if (error.details?.includes('applicable_package_id')) {
                        throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
                    }
                }
            }
            throw new common_1.InternalServerErrorException(`Failed to create package option: ${error.message}`);
        }
        if (!data) {
            this.logger.error('Option creation did not return data unexpectedly for package ' +
                packageId);
            throw new common_1.InternalServerErrorException('Failed to create package option.');
        }
        this.logger.log(`Option '${data.option_code}' (ID: ${data.id}) created successfully for package ${packageId}`);
        return data;
    }
    async findAll(packageId, queryDto) {
        this.logger.log(`Fetching options for package ${packageId} with query: ${JSON.stringify(queryDto)}`);
        await this._checkPackageExists(packageId);
        const { limit = 10, offset = 0 } = queryDto;
        const supabase = this.supabaseService.getClient();
        const query = supabase
            .from(this.TABLE_NAME)
            .select('*', { count: 'exact' })
            .eq('applicable_package_id', packageId)
            .range(offset, offset + limit - 1)
            .returns();
        const { data, error, count } = await query;
        if (error) {
            this.logger.error(`Error fetching package options for package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to fetch package options: ${error.message}`);
        }
        return {
            data: data || [],
            count: count || 0,
            limit,
            offset,
        };
    }
    async findOne(packageId, optionId) {
        this.logger.log(`Attempting to find option ${optionId} for package ${packageId}`);
        await this._checkPackageExists(packageId);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select('*')
            .eq('id', optionId)
            .eq('applicable_package_id', packageId)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error fetching option ${optionId} for package ${packageId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to fetch package option: ${error.message}`);
        }
        if (!data) {
            this.logger.warn(`Option ${optionId} not found for package ${packageId}`);
            throw new common_1.NotFoundException(`Package option with ID ${optionId} not found for package ${packageId}.`);
        }
        this.logger.log(`Option ${optionId} for package ${packageId} found successfully.`);
        return data;
    }
    async update(packageId, optionId, updateDto) {
        this.logger.log(`Attempting to update option ${optionId} for package ${packageId} with data: ${JSON.stringify(updateDto)}`);
        const restUpdateDto = {};
        if (updateDto.option_name !== undefined)
            restUpdateDto.option_name = updateDto.option_name;
        if (updateDto.currency_id !== undefined)
            restUpdateDto.currency_id = updateDto.currency_id;
        if (updateDto.price_adjustment !== undefined)
            restUpdateDto.price_adjustment = updateDto.price_adjustment;
        if (updateDto.cost_adjustment !== undefined)
            restUpdateDto.cost_adjustment = updateDto.cost_adjustment;
        if (updateDto.description !== undefined)
            restUpdateDto.description = updateDto.description;
        if (updateDto.option_group !== undefined)
            restUpdateDto.option_group = updateDto.option_group;
        if (updateDto.is_default_for_package !== undefined)
            restUpdateDto.is_default_for_package = updateDto.is_default_for_package;
        if (updateDto.is_required !== undefined)
            restUpdateDto.is_required = updateDto.is_required;
        if (Object.keys(restUpdateDto).length === 0) {
            this.logger.warn(`Update called for option ${optionId} but no valid fields were provided.`);
            return this.findOne(packageId, optionId);
        }
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .update(restUpdateDto)
            .eq('id', optionId)
            .eq('applicable_package_id', packageId)
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error updating option ${optionId} for package ${packageId}: ${error.message}\nStack: ${error.stack}`);
            if (error instanceof supabase_js_1.PostgrestError && error.code === '23503') {
                throw new common_1.NotFoundException(`Update failed due to invalid reference (e.g., currency_id): ${error.details}`);
            }
            throw new common_1.InternalServerErrorException(`Failed to update package option: ${error.message}`);
        }
        if (!data) {
            this.logger.warn(`Update failed for option ${optionId} - not found for package ${packageId} or already deleted.`);
            throw new common_1.NotFoundException(`Package option with ID ${optionId} not found for package ${packageId}.`);
        }
        this.logger.log(`Option ${optionId} updated successfully for package ${packageId}.`);
        return data;
    }
    async remove(packageId, optionId) {
        this.logger.log(`Attempting delete option ${optionId} for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.TABLE_NAME)
            .delete()
            .match({ id: optionId, applicable_package_id: packageId });
        if (error) {
            this.logger.error(`Error deleting option ${optionId} for package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to delete package option: ${error.message}`);
        }
        if (count === 0) {
            this.logger.warn(`Delete failed: Option ${optionId} not found for package ${packageId}.`);
            throw new common_1.NotFoundException(`Package option with ID ${optionId} not found for package ${packageId}.`);
        }
        this.logger.log(`Option ${optionId} deleted successfully for package ${packageId}.`);
    }
};
exports.PackageOptionsService = PackageOptionsService;
exports.PackageOptionsService = PackageOptionsService = PackageOptionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageOptionsService);
//# sourceMappingURL=package-options.service.js.map