"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const bullmq_1 = require("@nestjs/bullmq");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const supabase_module_1 = require("./core/supabase/supabase.module");
const core_module_1 = require("./core/core.module");
const cache_module_1 = require("./core/cache/cache.module");
const auth_module_1 = require("./modules/auth/auth.module");
const users_module_1 = require("./modules/users/users.module");
const calculations_module_1 = require("./modules/calculations/calculations.module");
const templates_module_1 = require("./modules/templates/templates.module");
const calculation_items_module_1 = require("./modules/calculation-items/calculation-items.module");
const cities_module_1 = require("./modules/cities/cities.module");
const currencies_module_1 = require("./modules/currencies/currencies.module");
const categories_module_1 = require("./modules/categories/categories.module");
const clients_module_1 = require("./modules/clients/clients.module");
const events_module_1 = require("./modules/events/events.module");
const packages_module_1 = require("./modules/packages/packages.module");
const exports_module_1 = require("./modules/exports/exports.module");
const settings_module_1 = require("./modules/settings/settings.module");
const admin_module_1 = require("./modules/admin/admin.module");
const admin_packages_module_1 = require("./modules/admin/packages/admin-packages.module");
const package_prices_module_1 = require("./modules/admin/package-prices/package-prices.module");
const package_dependencies_module_1 = require("./modules/admin/package-dependencies/package-dependencies.module");
const package_cities_module_1 = require("./modules/admin/package-cities/package-cities.module");
const package_venues_module_1 = require("./modules/admin/package-venues/package-venues.module");
const package_options_module_1 = require("./modules/admin/package-options/package-options.module");
const cost_items_module_1 = require("./modules/admin/cost-items/cost-items.module");
const service_categories_module_1 = require("./modules/admin/service-categories/service-categories.module");
const divisions_module_1 = require("./modules/divisions/divisions.module");
const venues_module_1 = require("./modules/venues/venues.module");
const event_types_module_1 = require("./modules/event-types/event-types.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: `.env`,
            }),
            bullmq_1.BullModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    connection: {
                        username: configService.get('REDIS_USERNAME', 'default'),
                        password: configService.get('REDIS_PASSWORD'),
                        host: configService.get('REDIS_HOST', 'localhost'),
                        port: configService.get('REDIS_PORT', 6379),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            cache_module_1.CacheModule,
            supabase_module_1.SupabaseModule,
            core_module_1.CoreModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            calculations_module_1.CalculationsModule,
            templates_module_1.TemplatesModule,
            calculation_items_module_1.CalculationItemsModule,
            cities_module_1.CitiesModule,
            currencies_module_1.CurrenciesModule,
            categories_module_1.CategoriesModule,
            admin_module_1.AdminModule,
            admin_packages_module_1.AdminPackagesModule,
            packages_module_1.PackagesModule,
            package_prices_module_1.PackagePricesModule,
            package_dependencies_module_1.PackageDependenciesModule,
            package_cities_module_1.PackageCitiesModule,
            package_venues_module_1.PackageVenuesModule,
            package_options_module_1.PackageOptionsModule,
            cost_items_module_1.CostItemsModule,
            service_categories_module_1.ServiceCategoriesModule,
            clients_module_1.ClientsModule,
            events_module_1.EventsModule,
            divisions_module_1.DivisionsModule,
            exports_module_1.ExportsModule,
            settings_module_1.SettingsModule,
            venues_module_1.VenuesModule,
            event_types_module_1.EventTypesModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map