"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const passport_1 = require("@nestjs/passport");
const jwt_strategy_1 = require("./strategies/jwt.strategy");
const auth_event_logger_service_1 = require("./services/auth-event-logger.service");
const jwt_validation_service_1 = require("./services/jwt-validation.service");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
        ],
        controllers: [],
        providers: [jwt_validation_service_1.JwtValidationService, jwt_strategy_1.JwtStrategy, auth_event_logger_service_1.AuthEventLoggerService],
        exports: [jwt_validation_service_1.JwtValidationService, jwt_strategy_1.JwtStrategy, auth_event_logger_service_1.AuthEventLoggerService],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map