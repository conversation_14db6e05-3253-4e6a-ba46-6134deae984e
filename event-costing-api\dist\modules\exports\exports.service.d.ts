import { SupabaseService } from '../../core/supabase/supabase.service';
import { CalculationsService } from '../calculations/calculations.service';
import { User } from '@supabase/supabase-js';
import { CreateExportDto } from './dto/create-export.dto';
import { ExportResponseDto } from './dto/export-response.dto';
import { Queue } from 'bullmq';
import { ExportStatusResponseDto } from './dto/export-status-response.dto';
import { ExportStatus } from './enums/export-status.enum';
import { ExportUpdateDetails } from './interfaces/export-update-details.interface';
import { ExportStorageService } from './services/export-storage.service';
import { CsvExportJobData } from './processors/csv-export.processor';
import { PdfExportJobData } from './processors/pdf-export.processor';
import { XlsxExportJobData } from './processors/xlsx-export.processor';
export declare class ExportsService {
    private readonly supabaseService;
    private readonly calculationsService;
    private readonly storageService;
    private readonly csvExportsQueue;
    private readonly pdfExportsQueue;
    private readonly xlsxExportsQueue;
    private readonly logger;
    private readonly EXPORT_HISTORY_TABLE;
    private mapStatusToFrontend;
    constructor(supabaseService: SupabaseService, calculationsService: CalculationsService, storageService: ExportStorageService, csvExportsQueue: Queue<CsvExportJobData>, pdfExportsQueue: Queue<PdfExportJobData>, xlsxExportsQueue: Queue<XlsxExportJobData>);
    initiateExport(createDto: CreateExportDto, user: User): Promise<ExportResponseDto>;
    updateExportHistoryStatus(historyId: string, status: ExportStatus, details?: ExportUpdateDetails): Promise<void>;
    getExportStatus(exportId: string, userId: string): Promise<ExportStatusResponseDto>;
    getExportsByCalculation(calculationId: string, userId: string): Promise<ExportStatusResponseDto[]>;
    private checkExportOwnership;
}
