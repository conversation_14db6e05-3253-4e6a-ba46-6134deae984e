"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesService = void 0;
const common_1 = require("@nestjs/common");
const package_crud_service_1 = require("./services/package-crud.service");
const package_query_service_1 = require("./services/package-query.service");
const package_relations_service_1 = require("./services/package-relations.service");
const package_batch_service_1 = require("./services/package-batch.service");
const package_transformer_util_1 = require("./utils/package-transformer.util");
let PackagesService = PackagesService_1 = class PackagesService {
    packageCrudService;
    packageQueryService;
    packageRelationsService;
    packageBatchService;
    logger = new common_1.Logger(PackagesService_1.name);
    constructor(packageCrudService, packageQueryService, packageRelationsService, packageBatchService) {
        this.packageCrudService = packageCrudService;
        this.packageQueryService = packageQueryService;
        this.packageRelationsService = packageRelationsService;
        this.packageBatchService = packageBatchService;
    }
    async create(createPackageDto) {
        this.logger.log(`Attempting to create package: ${JSON.stringify(createPackageDto)}`);
        try {
            const packageId = await this.packageCrudService.createPackage(createPackageDto);
            await this.packageRelationsService.createCityAssociations(packageId, createPackageDto.city_ids || []);
            await this.packageRelationsService.createVenueAssociations(packageId, createPackageDto.enable_venues || false, createPackageDto.venue_ids);
            await this.packageRelationsService.createPriceInformation(packageId, createPackageDto);
            return this.findOne(packageId);
        }
        catch (error) {
            this.logger.error(`Transaction failed during package creation: ${error.message}`);
            throw error;
        }
    }
    async findAll(queryDto) {
        this.logger.log(`Finding all packages with query: ${JSON.stringify(queryDto)}`);
        const paginatedResult = await this.packageQueryService.findAllPackages(queryDto);
        if (!paginatedResult.data || paginatedResult.data.length === 0) {
            return paginatedResult;
        }
        const packageIds = this.packageQueryService.getPackageIds(paginatedResult.data);
        const [categoriesMap, divisionsMap, pricesMap, citiesMap] = await Promise.all([
            this.packageQueryService.fetchCategoriesMap(package_transformer_util_1.PackageTransformerUtil.extractUniqueIds(paginatedResult.data, 'category_id')),
            this.packageQueryService.fetchDivisionsMap(package_transformer_util_1.PackageTransformerUtil.extractUniqueIds(paginatedResult.data, 'division_id')),
            this.packageQueryService.fetchPackagePricesMap(packageIds),
            this.packageQueryService.fetchPackageCitiesMap(packageIds),
        ]);
        const transformedData = package_transformer_util_1.PackageTransformerUtil.transformPackagesWithRelations(paginatedResult.data, categoriesMap, divisionsMap, pricesMap, citiesMap);
        return {
            data: transformedData,
            count: paginatedResult.count,
            limit: paginatedResult.limit,
            offset: paginatedResult.offset,
        };
    }
    async findOne(id) {
        this.logger.log(`Attempting to fetch package with ID: ${id}`);
        const packageData = await this.packageQueryService.findPackageById(id);
        const [categoriesMap, divisionsMap, pricesMap, citiesMap] = await Promise.all([
            packageData.category_id
                ? this.packageQueryService.fetchCategoriesMap([packageData.category_id])
                : Promise.resolve(new Map()),
            packageData.division_id
                ? this.packageQueryService.fetchDivisionsMap([packageData.division_id])
                : Promise.resolve(new Map()),
            this.packageQueryService.fetchPackagePricesMap([id]),
            this.packageQueryService.fetchPackageCitiesMap([id]),
        ]);
        const transformedPackage = package_transformer_util_1.PackageTransformerUtil.transformSinglePackageWithRelations(packageData, packageData.category_id ? categoriesMap.get(packageData.category_id) : undefined, packageData.division_id ? divisionsMap.get(packageData.division_id) : undefined, pricesMap.get(id), citiesMap.get(id));
        this.logger.log(`Package with ID ${id} fetched successfully with related data.`);
        return transformedPackage;
    }
    async update(id, updatePackageDto) {
        this.logger.log(`Attempting to update package ID ${id} with data: ${JSON.stringify(updatePackageDto)}`);
        try {
            await this.packageCrudService.updatePackage(id, updatePackageDto);
            await this.packageRelationsService.updateCityAssociations(id, updatePackageDto.city_ids);
            await this.packageRelationsService.updateVenueAssociations(id, updatePackageDto.enable_venues, updatePackageDto.venue_ids);
            await this.packageRelationsService.updatePriceInformation(id, updatePackageDto);
            return this.findOne(id);
        }
        catch (error) {
            this.logger.error(`Transaction failed during package update: ${error.message}`);
            throw error;
        }
    }
    async remove(id) {
        this.logger.log(`Attempting to soft delete package with ID: ${id}`);
        await this.packageCrudService.deletePackage(id);
    }
    async updateStatus(id, isActive) {
        this.logger.log(`Attempting to update status for package ID ${id} to ${isActive ? 'active' : 'inactive'}`);
        const updatedPackage = await this.packageCrudService.updatePackageStatus(id, isActive);
        return updatedPackage;
    }
    async batchUpdate(batchUpdateDto) {
        this.logger.log(`Attempting to batch update ${batchUpdateDto.packages.length} packages`);
        return this.packageBatchService.batchUpdatePackages(batchUpdateDto);
    }
};
exports.PackagesService = PackagesService;
exports.PackagesService = PackagesService = PackagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [package_crud_service_1.PackageCrudService,
        package_query_service_1.PackageQueryService,
        package_relations_service_1.PackageRelationsService,
        package_batch_service_1.PackageBatchService])
], PackagesService);
//# sourceMappingURL=packages.service.js.map