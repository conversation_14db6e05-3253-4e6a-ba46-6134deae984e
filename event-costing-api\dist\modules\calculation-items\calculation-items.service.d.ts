import { User } from '@supabase/supabase-js';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { AddPackageLineItemDto } from './dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from './dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { LineItemDto } from './dto/line-item.dto';
interface PackageSelectionItem {
    package_id: string;
    option_ids: string[];
    item_quantity: number | null;
    item_quantity_basis: number | null;
}
export declare class CalculationItemsService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    getCalculationItems(calculationId: string): Promise<LineItemDto[]>;
    getLineItemById(calculationId: string, itemId: string): Promise<LineItemDto>;
    populateItemsFromTemplateBlueprint(calculationId: string, currencyId: string, packageSelections: PackageSelectionItem[], user: User): Promise<void>;
    private fetchPackageDetails;
    private fetchPackagePrice;
    private fetchOptionDetails;
    addPackageLineItem(calcId: string, addDto: AddPackageLineItemDto, user: User): Promise<{
        id: string;
    }>;
    addCustomLineItem(calcId: string, addDto: AddCustomLineItemDto, user: User): Promise<{
        id: string;
    }>;
    updateLineItem(calcId: string, itemId: string, updateDto: UpdateLineItemDto, user: User): Promise<LineItemDto>;
    deletePackageLineItem(calcId: string, itemId: string, user: User): Promise<void>;
    deleteCustomLineItem(calcId: string, itemId: string, user: User): Promise<void>;
    private checkCalculationOwnership;
    private recalcCalculationViaRpc;
    deleteLineItem(calculationId: string, lineItemId: string, user: User): Promise<void>;
}
export {};
