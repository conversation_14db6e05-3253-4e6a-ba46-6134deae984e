import { ClientsService } from './clients.service';
import { ClientDto } from './dto/client.dto';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
export declare class ClientsController {
    private readonly clientsService;
    private readonly logger;
    constructor(clientsService: ClientsService);
    create(createClientDto: CreateClientDto): Promise<ClientDto>;
    findAll(search?: string): Promise<ClientDto[]>;
    findOne(id: string): Promise<ClientDto>;
    update(id: string, updateClientDto: UpdateClientDto): Promise<ClientDto>;
    remove(id: string): Promise<void>;
}
