"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ClientsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let ClientsService = ClientsService_1 = class ClientsService {
    supabaseService;
    logger = new common_1.Logger(ClientsService_1.name);
    TABLE_NAME = 'clients';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    handleSupabaseError(error, context) {
        this.logger.error(`${context}: ${error.message}`, error.stack);
        if (error.code === '23505') {
            throw new common_1.ConflictException('Client with this email already exists.');
        }
        throw new common_1.InternalServerErrorException(`Could not ${context}.`);
    }
    async create(createClientDto) {
        this.logger.log(`Creating a new client: ${createClientDto.client_name}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .insert({
            client_name: createClientDto.client_name,
            contact_person: createClientDto.contact_person,
            email: createClientDto.email,
            phone: createClientDto.phone,
            address: createClientDto.address,
            company_name: createClientDto.company_name,
        })
            .select()
            .single();
        if (error) {
            this.handleSupabaseError(error, 'create client');
        }
        if (!data) {
            this.logger.error('Insert operation did not return data unexpectedly.');
            throw new common_1.InternalServerErrorException('Failed to create client.');
        }
        this.logger.log(`Client created successfully with ID: ${data.id}`);
        return data;
    }
    async findAll(search) {
        this.logger.log(`Fetching all clients ${search ? `matching '${search}'` : ''}`);
        const supabase = this.supabaseService.getClient();
        const selectFields = 'id, client_name, contact_person, email, phone, company_name, created_at, updated_at';
        let query = supabase
            .from(this.TABLE_NAME)
            .select(selectFields)
            .order('client_name', { ascending: true });
        if (search) {
            query = query.or(`client_name.ilike.%${search}%,company_name.ilike.%${search}%,email.ilike.%${search}%`);
        }
        const { data, error } = await query.returns();
        if (error) {
            this.handleSupabaseError(error, 'find all clients');
        }
        return data || [];
    }
    async findOne(id) {
        this.logger.log(`Fetching client with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select('*')
            .eq('id', id)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Client with ID ${id} not found.`);
            }
            this.handleSupabaseError(error, `find client by ID ${id}`);
        }
        if (!data) {
            throw new common_1.NotFoundException(`Client with ID ${id} not found.`);
        }
        return data;
    }
    async update(id, updateClientDto) {
        this.logger.log(`Updating client with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        await this.findOne(id);
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .update(updateClientDto)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            this.handleSupabaseError(error, `update client ${id}`);
        }
        if (!data) {
            this.logger.error(`Update operation did not return data for client ${id}`);
            throw new common_1.InternalServerErrorException('Failed to update client.');
        }
        this.logger.log(`Client ${id} updated successfully.`);
        return data;
    }
    async remove(id) {
        this.logger.log(`Removing client with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        await this.findOne(id);
        const { error, count } = await supabase
            .from(this.TABLE_NAME)
            .delete()
            .eq('id', id);
        if (error) {
            this.handleSupabaseError(error, `remove client ${id}`);
        }
        if (count === 0) {
            this.logger.warn(`Client with ID ${id} was not found for deletion, despite earlier check.`);
            throw new common_1.NotFoundException(`Client with ID ${id} not found.`);
        }
        this.logger.log(`Client ${id} removed successfully.`);
    }
};
exports.ClientsService = ClientsService;
exports.ClientsService = ClientsService = ClientsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], ClientsService);
//# sourceMappingURL=clients.service.js.map