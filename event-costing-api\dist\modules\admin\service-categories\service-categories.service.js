"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ServiceCategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let ServiceCategoriesService = ServiceCategoriesService_1 = class ServiceCategoriesService {
    supabaseService;
    logger = new common_1.Logger(ServiceCategoriesService_1.name);
    tableName = 'service_categories';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(createDto) {
        this.logger.log(`Attempting to create service category: ${JSON.stringify(createDto)}`);
        const { data, error } = await this.supabaseService
            .getClient()
            .from(this.tableName)
            .insert([
            {
                name: createDto.name,
                description: createDto.description,
                parent_category_id: createDto.parent_category_id,
            },
        ])
            .select('*')
            .single();
        if (error) {
            this.handleSupabaseError(error, 'create', createDto);
        }
        if (!data) {
            this.logger.error('Service category creation failed: No data returned.');
            throw new common_1.InternalServerErrorException('Service category creation failed unexpectedly.');
        }
        this.logger.log(`Service category created successfully with ID: ${data.id}`);
        return data;
    }
    async findAll() {
        this.logger.log('Attempting to fetch all service categories');
        const { data, error } = await this.supabaseService
            .getClient()
            .from(this.tableName)
            .select('*')
            .is('deleted_at', null)
            .order('name', { ascending: true });
        if (error) {
            this.handleSupabaseError(error, 'findAll');
        }
        this.logger.log(`Fetched ${data?.length || 0} service categories.`);
        return data || [];
    }
    async findOne(id) {
        this.logger.log(`Attempting to fetch service category with ID: ${id}`);
        const { data, error } = await this.supabaseService
            .getClient()
            .from(this.tableName)
            .select('*')
            .eq('id', id)
            .is('deleted_at', null)
            .maybeSingle();
        if (error) {
            this.handleSupabaseError(error, 'findOne', { id });
        }
        if (!data) {
            this.logger.warn(`Service category with ID ${id} not found.`);
            throw new common_1.NotFoundException(`Service category with ID ${id} not found.`);
        }
        this.logger.log(`Fetched service category with ID: ${id}`);
        return data;
    }
    async update(id, updateDto) {
        this.logger.log(`Attempting to update service category ${id}: ${JSON.stringify(updateDto)}`);
        await this.findOne(id);
        if (Object.keys(updateDto).length === 0) {
            this.logger.warn(`Update called for service category ${id} with no data.`);
            return this.findOne(id);
        }
        const { data, error } = await this.supabaseService
            .getClient()
            .from(this.tableName)
            .update({
            ...updateDto,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select('*')
            .single();
        if (error) {
            this.handleSupabaseError(error, 'update', { id, ...updateDto });
        }
        if (!data) {
            this.logger.error(`Service category with ID ${id} not found after update attempt.`);
            throw new common_1.NotFoundException(`Service category with ID ${id} not found after update attempt.`);
        }
        this.logger.log(`Service category ${id} updated successfully.`);
        return data;
    }
    async remove(id) {
        this.logger.log(`Attempting to soft-delete service category with ID: ${id}`);
        await this.findOne(id);
        const { error } = await this.supabaseService
            .getClient()
            .from(this.tableName)
            .update({ deleted_at: new Date().toISOString() })
            .eq('id', id);
        if (error) {
            this.handleSupabaseError(error, 'remove', { id });
        }
        this.logger.log(`Service category ${id} soft-deleted successfully.`);
    }
    handleSupabaseError(error, operation, context) {
        this.logger.error(`Supabase error during ${operation}: ${error.message}`, {
            details: error.details,
            code: error.code,
            context,
        });
        if (error.code === '23505') {
            throw new common_1.ConflictException(`Operation '${operation}' failed due to a unique constraint violation.`);
        }
        if (error.code === '23503') {
            throw new common_1.ConflictException(`Operation '${operation}' failed due to a foreign key constraint.`);
        }
        if (error.code === 'PGRST116') {
            throw new common_1.NotFoundException(`Operation '${operation}' failed: The record was not found.`);
        }
        throw new common_1.InternalServerErrorException(`An unexpected error occurred during ${operation}: ${error.message}`);
    }
};
exports.ServiceCategoriesService = ServiceCategoriesService;
exports.ServiceCategoriesService = ServiceCategoriesService = ServiceCategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], ServiceCategoriesService);
//# sourceMappingURL=service-categories.service.js.map