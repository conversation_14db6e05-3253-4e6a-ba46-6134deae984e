export declare class EventTypeDto {
    id: string;
    name: string;
    code: string;
    description?: string;
    icon?: string;
    color: string;
    display_order: number;
    is_active: boolean;
}
export declare class CreateEventTypeDto {
    name: string;
    code: string;
    description?: string;
    icon?: string;
    color?: string;
    display_order?: number;
}
export declare class UpdateEventTypeDto {
    name?: string;
    code?: string;
    description?: string;
    icon?: string;
    color?: string;
    display_order?: number;
    is_active?: boolean;
}
