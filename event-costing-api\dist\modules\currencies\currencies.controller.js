"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CurrenciesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrenciesController = void 0;
const common_1 = require("@nestjs/common");
const currencies_service_1 = require("./currencies.service");
const currency_dto_1 = require("./dto/currency.dto");
const swagger_1 = require("@nestjs/swagger");
let CurrenciesController = CurrenciesController_1 = class CurrenciesController {
    currenciesService;
    logger = new common_1.Logger(CurrenciesController_1.name);
    constructor(currenciesService) {
        this.currenciesService = currenciesService;
    }
    async getCurrencies() {
        this.logger.log('Fetching all currencies');
        return this.currenciesService.findAll();
    }
};
exports.CurrenciesController = CurrenciesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOkResponse)({ type: [currency_dto_1.CurrencyDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "getCurrencies", null);
exports.CurrenciesController = CurrenciesController = CurrenciesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Currencies'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('currencies'),
    __metadata("design:paramtypes", [currencies_service_1.CurrenciesService])
], CurrenciesController);
//# sourceMappingURL=currencies.controller.js.map