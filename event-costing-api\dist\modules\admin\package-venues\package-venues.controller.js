"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackageVenuesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageVenuesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
const package_venues_service_1 = require("./package-venues.service");
const package_venue_dto_1 = require("./dto/package-venue.dto");
const add_package_venue_dto_1 = require("./dto/add-package-venue.dto");
let PackageVenuesController = PackageVenuesController_1 = class PackageVenuesController {
    packageVenuesService;
    logger = new common_1.Logger(PackageVenuesController_1.name);
    constructor(packageVenuesService) {
        this.packageVenuesService = packageVenuesService;
    }
    async addVenue(packageId, addDto) {
        this.logger.log(`Adding venue ${addDto.venue_id} to package ${packageId}`);
        const newAssociation = await this.packageVenuesService.addVenueToPackage(packageId, addDto.venue_id);
        return { id: newAssociation.id };
    }
    async listVenues(packageId) {
        this.logger.log(`Listing venues for package ${packageId}`);
        return this.packageVenuesService.listVenuesForPackage(packageId);
    }
    async removeVenue(packageId, venueId) {
        this.logger.log(`Removing venue ${venueId} from package ${packageId}`);
        await this.packageVenuesService.removeVenueFromPackage(packageId, venueId);
    }
};
exports.PackageVenuesController = PackageVenuesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Add a venue to a package' }),
    (0, swagger_1.ApiBody)({ type: add_package_venue_dto_1.AddPackageVenueDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Venue successfully added to package.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', format: 'uuid' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Venue already associated' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Venue not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_package_venue_dto_1.AddPackageVenueDto]),
    __metadata("design:returntype", Promise)
], PackageVenuesController.prototype, "addVenue", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List venues associated with a package' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of associated venues.',
        type: [package_venue_dto_1.PackageVenueDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageVenuesController.prototype, "listVenues", null);
__decorate([
    (0, common_1.Delete)(':venueId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Remove a venue from a package' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Venue successfully removed from package.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or Venue not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    (0, swagger_1.ApiParam)({
        name: 'venueId',
        type: 'string',
        format: 'uuid',
        description: 'Venue UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('venueId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackageVenuesController.prototype, "removeVenue", null);
exports.PackageVenuesController = PackageVenuesController = PackageVenuesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Package Venues'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/packages/:packageId/venues'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __metadata("design:paramtypes", [package_venues_service_1.PackageVenuesService])
], PackageVenuesController);
//# sourceMappingURL=package-venues.controller.js.map