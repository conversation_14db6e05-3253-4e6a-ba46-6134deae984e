import { PackagesService } from './packages.service.js';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { UpdatePackageStatusDto } from './dto/update-package-status.dto';
import { PackageDto } from './dto/package.dto';
import { PackageListQueryDto } from './dto/package-list-query.dto';
import { BatchUpdatePackagesDto } from './dto/batch-update-packages.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
export declare class PackagesController {
    private readonly packagesService;
    constructor(packagesService: PackagesService);
    create(createPackageDto: CreatePackageDto): Promise<PackageDto>;
    findAll(queryDto: PackageListQueryDto): Promise<PaginatedResponseDto<PackageDto>>;
    findOne(id: string): Promise<PackageDto>;
    update(id: string, updatePackageDto: UpdatePackageDto): Promise<PackageDto>;
    remove(id: string): Promise<void>;
    batchUpdate(batchUpdateDto: BatchUpdatePackagesDto): Promise<{
        updatedCount: number;
        errors: any[];
    }>;
    updateStatus(id: string, updateStatusDto: UpdatePackageStatusDto): Promise<PackageDto>;
}
