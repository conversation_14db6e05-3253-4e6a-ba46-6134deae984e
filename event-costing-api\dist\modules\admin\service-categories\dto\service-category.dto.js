"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCategoryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ServiceCategoryDto {
    id;
    name;
    description;
    parent_category_id;
    created_at;
    updated_at;
}
exports.ServiceCategoryDto = ServiceCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the category',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], ServiceCategoryDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the service category' }),
    __metadata("design:type", String)
], ServiceCategoryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Description of the service category' }),
    __metadata("design:type", String)
], ServiceCategoryDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID of the parent category, if applicable',
        format: 'uuid',
        nullable: true,
    }),
    __metadata("design:type", Object)
], ServiceCategoryDto.prototype, "parent_category_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the category was created' }),
    __metadata("design:type", Date)
], ServiceCategoryDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp when the category was last updated' }),
    __metadata("design:type", Date)
], ServiceCategoryDto.prototype, "updated_at", void 0);
//# sourceMappingURL=service-category.dto.js.map