"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackageCitiesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
const package_cities_service_1 = require("./package-cities.service");
const package_city_dto_1 = require("./dto/package-city.dto");
const add_package_city_dto_1 = require("./dto/add-package-city.dto");
const common_2 = require("@nestjs/common");
let PackageCitiesController = PackageCitiesController_1 = class PackageCitiesController {
    packageCitiesService;
    logger = new common_1.Logger(PackageCitiesController_1.name);
    constructor(packageCitiesService) {
        this.packageCitiesService = packageCitiesService;
    }
    async addCity(packageId, addDto) {
        this.logger.log(`Adding city ${addDto.city_id} to package ${packageId}`);
        const newAssociation = await this.packageCitiesService.addCityToPackage(packageId, addDto.city_id);
        return { id: newAssociation.id };
    }
    async listCities(packageId) {
        this.logger.log(`Listing cities for package ${packageId}`);
        return this.packageCitiesService.listCitiesForPackage(packageId);
    }
    async removeCity(packageId, cityId) {
        this.logger.log(`Removing city ${cityId} from package ${packageId}`);
        await this.packageCitiesService.removeCityFromPackage(packageId, cityId);
    }
};
exports.PackageCitiesController = PackageCitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Add a city association to a package',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'City added successfully.',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request (validation errors, city already added)',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package or City not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_2.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, add_package_city_dto_1.AddPackageCityDto]),
    __metadata("design:returntype", Promise)
], PackageCitiesController.prototype, "addCity", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List cities associated with a package' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of associated cities.',
        type: [package_city_dto_1.PackageCityDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found' }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_2.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageCitiesController.prototype, "listCities", null);
__decorate([
    (0, common_1.Delete)(':cityId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Remove a city association from a package' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'City removed successfully.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Package, City, or Association not found',
    }),
    (0, swagger_1.ApiParam)({
        name: 'packageId',
        type: 'string',
        format: 'uuid',
        description: 'Package UUID',
    }),
    (0, swagger_1.ApiParam)({
        name: 'cityId',
        type: 'string',
        format: 'uuid',
        description: 'City UUID',
    }),
    __param(0, (0, common_1.Param)('packageId', common_2.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('cityId', common_2.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackageCitiesController.prototype, "removeCity", null);
exports.PackageCitiesController = PackageCitiesController = PackageCitiesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Packages - Cities'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/packages/:packageId/cities'),
    __metadata("design:paramtypes", [package_cities_service_1.PackageCitiesService])
], PackageCitiesController);
//# sourceMappingURL=package-cities.controller.js.map