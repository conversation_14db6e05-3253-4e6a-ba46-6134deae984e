import { SupabaseService } from '../../core/supabase/supabase.service';
import { CityDto } from './dto/city.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
export declare class CitiesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<CityDto[]>;
    createCity(createCityDto: CreateCityDto): Promise<CityDto>;
    updateCity(id: string, updateCityDto: UpdateCityDto): Promise<CityDto>;
    deleteCity(id: string): Promise<void>;
    findOne(id: string): Promise<CityDto>;
}
