"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageCatalogDto = exports.PackageCatalogMetadataDto = exports.FilterInfoDto = exports.AvailableFiltersDto = exports.CurrencyInfoDto = exports.DivisionInfoDto = exports.CityInfoDto = exports.PaginatedPackagesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const package_variation_dto_1 = require("./package-variation.dto");
const category_dto_1 = require("../../categories/dto/category.dto");
const package_filters_dto_1 = require("./package-filters.dto");
class PaginatedPackagesDto {
    data;
    totalCount;
    page;
    pageSize;
    totalPages;
}
exports.PaginatedPackagesDto = PaginatedPackagesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of packages',
        type: [package_variation_dto_1.PackageVariationDto],
    }),
    __metadata("design:type", Array)
], PaginatedPackagesDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of packages',
        example: 150,
    }),
    __metadata("design:type", Number)
], PaginatedPackagesDto.prototype, "totalCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current page number',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedPackagesDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items per page',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedPackagesDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of pages',
        example: 15,
    }),
    __metadata("design:type", Number)
], PaginatedPackagesDto.prototype, "totalPages", void 0);
class CityInfoDto {
    id;
    name;
    code;
}
exports.CityInfoDto = CityInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CityInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City name',
        example: 'Jakarta',
    }),
    __metadata("design:type", String)
], CityInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City code',
        example: 'JKT',
    }),
    __metadata("design:type", String)
], CityInfoDto.prototype, "code", void 0);
class DivisionInfoDto {
    id;
    name;
    code;
}
exports.DivisionInfoDto = DivisionInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Division ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], DivisionInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Division name',
        example: 'Audio Visual',
    }),
    __metadata("design:type", String)
], DivisionInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Division code',
        example: 'AV',
    }),
    __metadata("design:type", String)
], DivisionInfoDto.prototype, "code", void 0);
class CurrencyInfoDto {
    id;
    code;
    symbol;
    name;
}
exports.CurrencyInfoDto = CurrencyInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency ID',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CurrencyInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'IDR',
    }),
    __metadata("design:type", String)
], CurrencyInfoDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency symbol',
        example: 'Rp',
    }),
    __metadata("design:type", String)
], CurrencyInfoDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency name',
        example: 'Indonesian Rupiah',
    }),
    __metadata("design:type", String)
], CurrencyInfoDto.prototype, "name", void 0);
class AvailableFiltersDto {
    categories;
    cities;
    divisions;
}
exports.AvailableFiltersDto = AvailableFiltersDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available categories for filtering',
        type: [Object],
    }),
    __metadata("design:type", Array)
], AvailableFiltersDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available cities for filtering',
        type: [Object],
    }),
    __metadata("design:type", Array)
], AvailableFiltersDto.prototype, "cities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available divisions for filtering',
        type: [Object],
    }),
    __metadata("design:type", Array)
], AvailableFiltersDto.prototype, "divisions", void 0);
class FilterInfoDto {
    applied;
    available;
}
exports.FilterInfoDto = FilterInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Applied filters',
        type: package_filters_dto_1.PackageFiltersDto,
    }),
    __metadata("design:type", package_filters_dto_1.PackageFiltersDto)
], FilterInfoDto.prototype, "applied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available filter options',
        type: AvailableFiltersDto,
    }),
    __metadata("design:type", AvailableFiltersDto)
], FilterInfoDto.prototype, "available", void 0);
class PackageCatalogMetadataDto {
    loadTime;
    cacheVersion;
    userId;
    errors;
    timestamp;
    totalPackages;
    appliedFilters;
}
exports.PackageCatalogMetadataDto = PackageCatalogMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time taken to load the data in milliseconds',
        example: 350,
    }),
    __metadata("design:type", Number)
], PackageCatalogMetadataDto.prototype, "loadTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache version for the response',
        example: '1.0',
    }),
    __metadata("design:type", String)
], PackageCatalogMetadataDto.prototype, "cacheVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who requested the data',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], PackageCatalogMetadataDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Any errors encountered during data loading',
        type: [String],
        example: [],
    }),
    __metadata("design:type", Array)
], PackageCatalogMetadataDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the data was loaded',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], PackageCatalogMetadataDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of packages in the catalog',
        example: 150,
    }),
    __metadata("design:type", Number)
], PackageCatalogMetadataDto.prototype, "totalPackages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of filters applied',
        example: 2,
    }),
    __metadata("design:type", Number)
], PackageCatalogMetadataDto.prototype, "appliedFilters", void 0);
class PackageCatalogDto {
    packages;
    categories;
    cities;
    divisions;
    currencies;
    filters;
    metadata;
}
exports.PackageCatalogDto = PackageCatalogDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Paginated package results',
        type: PaginatedPackagesDto,
    }),
    __metadata("design:type", PaginatedPackagesDto)
], PackageCatalogDto.prototype, "packages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available categories',
        type: [category_dto_1.CategoryDto],
    }),
    __metadata("design:type", Array)
], PackageCatalogDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available cities',
        type: [CityInfoDto],
    }),
    __metadata("design:type", Array)
], PackageCatalogDto.prototype, "cities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available divisions',
        type: [DivisionInfoDto],
    }),
    __metadata("design:type", Array)
], PackageCatalogDto.prototype, "divisions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available currencies',
        type: [CurrencyInfoDto],
    }),
    __metadata("design:type", Array)
], PackageCatalogDto.prototype, "currencies", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter information',
        type: FilterInfoDto,
    }),
    __metadata("design:type", FilterInfoDto)
], PackageCatalogDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata about the response',
        type: PackageCatalogMetadataDto,
    }),
    __metadata("design:type", PackageCatalogMetadataDto)
], PackageCatalogDto.prototype, "metadata", void 0);
//# sourceMappingURL=package-catalog.dto.js.map