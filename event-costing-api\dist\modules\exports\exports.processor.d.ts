import { WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { CalculationsService } from '../calculations/calculations.service';
import { ExportsService } from './exports.service';
import { ExportGenerationService } from './services/export-generation.service';
import { ExportStorageService } from './services/export-storage.service';
import { ExportJobData } from './interfaces/export-job-data.interface';
export declare class ExportsProcessor extends WorkerHost {
    private readonly calculationsService;
    private readonly exportsService;
    private readonly generationService;
    private readonly storageService;
    private readonly logger;
    constructor(calculationsService: CalculationsService, exportsService: ExportsService, generationService: ExportGenerationService, storageService: ExportStorageService);
    process(job: Job<ExportJobData>): Promise<void>;
    onCompleted(job: Job, result: any): void;
    onFailed(job: Job, err: Error): void;
    onActive(job: Job): void;
}
