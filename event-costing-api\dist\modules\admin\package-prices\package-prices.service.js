"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackagePricesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let PackagePricesService = PackagePricesService_1 = class PackagePricesService {
    supabaseService;
    logger = new common_1.Logger(PackagePricesService_1.name);
    TABLE_NAME = 'package_prices';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(packageId, createDto) {
        this.logger.log(`Attempting to add price for package ${packageId}: ${JSON.stringify(createDto)}`);
        const supabase = this.supabaseService.getClient();
        await this.checkPackageExists(packageId);
        const insertData = {
            package_id: packageId,
            currency_id: createDto.currency_id,
            price: createDto.price,
            unit_base_cost: createDto.unit_base_cost,
            description: createDto.description,
        };
        const { data, error } = await supabase
            .from('package_prices')
            .insert(insertData)
            .select('*')
            .single();
        if (error) {
            this.handlePackagePriceError(error, packageId, createDto.currency_id);
        }
        if (!data) {
            this.logger.error('Package price creation did not return data unexpectedly.');
            throw new common_1.InternalServerErrorException('Failed to create package price after insertion.');
        }
        this.logger.log(`Price created successfully for package ${packageId} with ID: ${data.id}`);
        return data;
    }
    async findAll(packageId) {
        this.logger.log(`Fetching prices for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkPackageExists(packageId);
        const { data, error } = await supabase
            .from('package_prices')
            .select('*')
            .eq('package_id', packageId)
            .returns();
        if (error) {
            this.logger.error(`Error fetching prices for package ${packageId}: ${error.message}`, error.details);
            throw new common_1.InternalServerErrorException(`Failed to fetch prices: ${error.message}`);
        }
        this.logger.log(`Found ${data?.length ?? 0} prices for package ${packageId}`);
        return data ?? [];
    }
    async findOne(packageId, packagePriceId) {
        this.logger.log(`Fetching price ${packagePriceId} for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkPackageExists(packageId);
        const { data, error } = await supabase
            .from('package_prices')
            .select('*')
            .match({ id: packagePriceId, package_id: packageId })
            .single();
        if (error) {
            this.logger.error(`Error fetching price ${packagePriceId} for package ${packageId}: ${error.message}`);
            this.handlePackagePriceError(error, packageId, 'unknown');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Price record with ID ${packagePriceId} not found for package ${packageId}.`);
        }
        return data;
    }
    async update(packageId, packagePriceId, updateDto) {
        this.logger.log(`Attempting to update price ${packagePriceId} for package ${packageId}: ${JSON.stringify(updateDto)}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {};
        if (updateDto.price !== undefined) {
            updateData.price = updateDto.price;
        }
        if (updateDto.unit_base_cost !== undefined) {
            updateData.unit_base_cost = updateDto.unit_base_cost;
        }
        if (updateDto.description !== undefined) {
            updateData.description = updateDto.description;
        }
        if (Object.keys(updateData).length === 0) {
            this.logger.log(`Update called for price ${packagePriceId} package ${packageId} with no changes. Fetching current data.`);
            return this.findOne(packageId, packagePriceId);
        }
        const { data, error } = await supabase
            .from('package_prices')
            .update(updateData)
            .match({ id: packagePriceId, package_id: packageId })
            .select('*')
            .single();
        if (error) {
            this.handlePackagePriceError(error, packageId, 'unknown');
        }
        if (!data) {
            this.logger.error(`Price record with ID ${packagePriceId} not found for package ${packageId} during update.`);
            throw new common_1.NotFoundException(`Price record with ID ${packagePriceId} not found for package ${packageId}.`);
        }
        this.logger.log(`Price ${packagePriceId} for package ${packageId} updated successfully.`);
        return data;
    }
    async remove(packageId, packagePriceId) {
        this.logger.log(`Attempting to delete price ${packagePriceId} for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        await this.checkPackageExists(packageId);
        const { error, count } = await supabase
            .from('package_prices')
            .delete()
            .match({ id: packagePriceId, package_id: packageId });
        if (error) {
            this.logger.error(`Error deleting price ${packagePriceId} for package ${packageId}: ${error.message}`, error.details);
            throw new common_1.InternalServerErrorException(`Failed to delete price record: ${error.message}`);
        }
        if (count === 0) {
            this.logger.warn(`Price record with ID ${packagePriceId} not found for package ${packageId} during delete.`);
            throw new common_1.NotFoundException(`Price record with ID ${packagePriceId} not found for package ${packageId}.`);
        }
        this.logger.log(`Price ${packagePriceId} for package ${packageId} deleted successfully.`);
    }
    handlePackagePriceError(error, packageId, currencyId) {
        this.logger.error(`Database error managing price for package ${packageId}, currency ${currencyId}: ${error.message}`, error.stack);
        if (error.code === '23505' &&
            error.details?.includes('package_prices_package_id_currency_id_effective_from_key')) {
            throw new common_1.ConflictException(`A price for package ${packageId} and currency ${currencyId} with the same effective date already exists.`);
        }
        if (error.code === '23503') {
            if (error.details?.includes('package_prices_currency_id_fkey')) {
                throw new common_1.BadRequestException(`Invalid currency ID: ${currencyId}.`);
            }
            if (error.details?.includes('package_prices_package_id_fkey')) {
                throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
            }
        }
        if (error.message.includes('Results contain 0 rows')) {
            throw new common_1.NotFoundException(`Price record not found for package ${packageId} (currency: ${currencyId}).`);
        }
        throw new common_1.InternalServerErrorException(`Failed to manage package price: ${error.message}`);
    }
    async checkPackageExists(packageId) {
        this.logger.debug(`Checking existence of package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from('packages')
            .select('id', { count: 'exact', head: true })
            .eq('id', packageId)
            .eq('is_deleted', false);
        if (error) {
            this.logger.error(`Error checking package existence ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to verify package ${packageId}: ${error.message}`);
        }
        if (count === 0) {
            this.logger.warn(`Package with ID ${packageId} not found or is deleted.`);
            throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
        }
        this.logger.debug(`Package ${packageId} confirmed to exist.`);
    }
};
exports.PackagePricesService = PackagePricesService;
exports.PackagePricesService = PackagePricesService = PackagePricesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackagePricesService);
//# sourceMappingURL=package-prices.service.js.map