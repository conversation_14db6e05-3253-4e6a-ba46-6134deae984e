"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationTemplateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationTemplateService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const calculation_items_service_1 = require("../calculation-items/calculation-items.service");
const calculation_status_enum_1 = require("./enums/calculation-status.enum");
let CalculationTemplateService = CalculationTemplateService_1 = class CalculationTemplateService {
    supabaseService;
    calculationItemsService;
    logger = new common_1.Logger(CalculationTemplateService_1.name);
    constructor(supabaseService, calculationItemsService) {
        this.supabaseService = supabaseService;
        this.calculationItemsService = calculationItemsService;
    }
    async createFromTemplate(templateId, customization, user) {
        this.logger.log(`User ${user.id} starting calculation creation from template ${templateId} with customization: ${JSON.stringify(customization)}`);
        const supabase = this.supabaseService.getClient();
        const { data: templateData, error: templateError } = await supabase
            .from('templates')
            .select('*')
            .eq('id', templateId)
            .or(`is_public.eq.true,created_by.eq.${user.id}`)
            .maybeSingle();
        if (templateError) {
            this.logger.error(`Error fetching template ${templateId} for user ${user.id}: ${templateError.message}`, templateError.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch template details.');
        }
        if (!templateData) {
            this.logger.warn(`Template ${templateId} not found or user ${user.id} lacks access.`);
            throw new common_1.NotFoundException(`Template with ID ${templateId} not found or access denied.`);
        }
        const packageSelections = templateData.package_selections || [];
        const calculationData = {
            name: customization.name,
            currency_id: templateData.currency_id,
            city_id: customization.cityId || templateData.city_id,
            client_id: customization.clientId || null,
            event_id: customization.eventId || null,
            event_start_date: customization.eventStartDate || templateData.template_start_date,
            event_end_date: customization.eventEndDate || templateData.template_end_date,
            attendees: customization.attendees || templateData.attendees,
            event_type: templateData.event_type,
            notes: customization.notes || `Based on template: ${templateData.template_name}`,
            created_by: user.id,
            status: calculation_status_enum_1.CalculationStatus.DRAFT,
            subtotal: 0,
            taxes: templateData.taxes || null,
            discount: templateData.discount || null,
            total: 0,
            total_cost: 0,
            estimated_profit: 0,
        };
        const { data: newCalc, error: createError } = await supabase
            .from('calculation_history')
            .insert(calculationData)
            .select('id')
            .single();
        if (createError || !newCalc) {
            this.logger.error(`Failed to create calculation history from template ${templateId}: ${createError?.message}`, createError?.stack);
            throw new common_1.InternalServerErrorException('Could not create calculation record.');
        }
        const newCalculationId = newCalc.id;
        this.logger.log(`Created calculation ${newCalculationId} from template ${templateId}`);
        let venueIds = [];
        if (customization.venueIds && customization.venueIds.length > 0) {
            venueIds = customization.venueIds;
            this.logger.log(`Using ${venueIds.length} venue IDs from customization for calculation ${newCalculationId}`);
        }
        else {
            const { data: templateVenues, error: venueError } = await supabase
                .from('template_venues')
                .select('venue_id')
                .eq('template_id', templateId);
            if (venueError) {
                this.logger.error(`Error fetching venues for template ${templateId}: ${venueError.message}`, venueError.stack);
            }
            else if (templateVenues && templateVenues.length > 0) {
                venueIds = templateVenues.map(tv => tv.venue_id);
                this.logger.log(`Using ${venueIds.length} venue IDs from template for calculation ${newCalculationId}`);
            }
        }
        if (venueIds.length > 0) {
            const venuePayloads = venueIds.map(venueId => ({
                calculation_id: newCalculationId,
                venue_id: venueId,
            }));
            const { error: venueInsertError } = await supabase
                .from('calculation_venues')
                .insert(venuePayloads);
            if (venueInsertError) {
                this.logger.error(`Error adding venues to calculation ${newCalculationId}: ${venueInsertError.message}`, venueInsertError.stack);
            }
            else {
                this.logger.log(`Successfully added ${venueIds.length} venues to calculation ${newCalculationId}`);
            }
        }
        try {
            await this.calculationItemsService.populateItemsFromTemplateBlueprint(newCalculationId, templateData.currency_id, packageSelections, user);
            this.logger.log(`Successfully populated items for calculation ${newCalculationId}.`);
        }
        catch (itemError) {
            const errorMessage = itemError instanceof Error ? itemError.message : String(itemError);
            const errorStack = itemError instanceof Error ? itemError.stack : undefined;
            this.logger.error(`Failed to populate line items for calculation ${newCalculationId} from template ${templateId}. Error originating from CalculationItemsService: ${errorMessage}`, errorStack);
        }
        try {
            this.logger.log(`Attempting to trigger recalculation for calculation ID: ${newCalculationId}`);
            const { error: rpcError } = await supabase.rpc('recalculate_calculation_totals', { p_calculation_id: newCalculationId });
            if (rpcError) {
                this.logger.error(`Error calling recalculate_calculation_totals RPC for calculation ${newCalculationId}: ${rpcError.message}`, rpcError.stack);
            }
            else {
                this.logger.log(`Successfully triggered recalculation for calculation ID: ${newCalculationId}`);
            }
        }
        catch (recalcError) {
            const errorMessage = recalcError instanceof Error
                ? recalcError.message
                : String(recalcError);
            const errorStack = recalcError instanceof Error ? recalcError.stack : undefined;
            this.logger.error(`Recalculation RPC call failed unexpectedly for calculation ID: ${newCalculationId} - ${errorMessage}`, errorStack);
        }
        return newCalculationId;
    }
};
exports.CalculationTemplateService = CalculationTemplateService;
exports.CalculationTemplateService = CalculationTemplateService = CalculationTemplateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        calculation_items_service_1.CalculationItemsService])
], CalculationTemplateService);
//# sourceMappingURL=calculation-template.service.js.map