import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CategoriesService } from '../../categories/categories.service';
import { CitiesService } from '../../cities/cities.service';
import { DivisionsService } from '../../divisions/divisions.service';
import { AdminDashboardDataDto } from '../dto/admin-dashboard-data.dto';
import { AdminDashboardFiltersDto } from '../dto/admin-dashboard-filters.dto';
export declare class AdminDashboardService {
    private readonly supabaseService;
    private readonly categoriesService;
    private readonly citiesService;
    private readonly divisionsService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, categoriesService: CategoriesService, citiesService: CitiesService, divisionsService: DivisionsService);
    getAdminDashboardData(filters: AdminDashboardFiltersDto | undefined, user: User): Promise<AdminDashboardDataDto>;
    private getSystemOverview;
    private getCategories;
    private getCities;
    private getDivisions;
    private getPackagesOverview;
    private getTemplatesOverview;
    private getCalculationsOverview;
    private getUsersOverview;
    private getRecentActivity;
    private extractCount;
    private extractResult;
    private collectErrors;
}
