"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationValidationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationValidationService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let CalculationValidationService = CalculationValidationService_1 = class CalculationValidationService {
    supabaseService;
    logger = new common_1.Logger(CalculationValidationService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async checkCalculationOwnership(calculationId, userId) {
        this.logger.log(`[EXPORT] Starting ownership check for calc ${calculationId}, user ${userId}`);
        const supabase = this.supabaseService.getClient();
        const startTime = Date.now();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('created_by', { count: 'exact' })
            .eq('id', calculationId)
            .eq('is_deleted', false)
            .eq('created_by', userId)
            .maybeSingle();
        const duration = Date.now() - startTime;
        this.logger.log(`[EXPORT] Ownership check query completed in ${duration}ms for calc ${calculationId}`);
        if (error) {
            this.logger.error(`[EXPORT] Supabase error in ownership check for calc ${calculationId}: ${error.message}`, JSON.stringify(error, null, 2));
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found or check failed.`);
        }
        if (!data) {
            this.logger.warn(`[EXPORT] No data returned from ownership check for calc ${calculationId}, user ${userId}`);
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found or not accessible.`);
        }
        this.logger.log(`[EXPORT] Ownership check passed for calc ${calculationId}, user ${userId}`);
    }
    async validateCalculationExists(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('id')
            .eq('id', calculationId)
            .eq('is_deleted', false)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error validating calculation existence for ${calculationId}: ${error.message}`);
            return false;
        }
        return !!data;
    }
    async canUserAccessCalculation(calculationId, userId) {
        try {
            await this.checkCalculationOwnership(calculationId, userId);
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async validateCalculationStatus(calculationId, allowedStatuses) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('status')
            .eq('id', calculationId)
            .eq('is_deleted', false)
            .single();
        if (error) {
            this.logger.error(`Error fetching calculation status for ${calculationId}: ${error.message}`);
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found.`);
        }
        if (!allowedStatuses.includes(data.status)) {
            throw new Error(`Calculation status '${data.status}' is not allowed for this operation. Allowed statuses: ${allowedStatuses.join(', ')}`);
        }
        return data.status;
    }
    async getCalculationBasicInfo(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('id, created_by, status, is_deleted')
            .eq('id', calculationId)
            .single();
        if (error) {
            this.logger.error(`Error fetching calculation basic info for ${calculationId}: ${error.message}`);
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found.`);
        }
        return data;
    }
    async validateCalculationAccess(calculationId, userId, allowedStatuses) {
        const basicInfo = await this.getCalculationBasicInfo(calculationId);
        if (basicInfo.is_deleted) {
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found.`);
        }
        if (basicInfo.created_by !== userId) {
            this.logger.warn(`User ${userId} attempted to access calculation ${calculationId} owned by ${basicInfo.created_by}`);
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found or not accessible.`);
        }
        if (allowedStatuses && !allowedStatuses.includes(basicInfo.status)) {
            throw new Error(`Calculation status '${basicInfo.status}' is not allowed for this operation. Allowed statuses: ${allowedStatuses.join(', ')}`);
        }
        return {
            status: basicInfo.status,
            created_by: basicInfo.created_by,
        };
    }
    async hasLineItems(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { count, error } = await supabase
            .from('calculation_line_items')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Error checking line items for calculation ${calculationId}: ${error.message}`);
            return false;
        }
        return (count ?? 0) > 0;
    }
    async hasCustomItems(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { count, error } = await supabase
            .from('calculation_custom_items')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Error checking custom items for calculation ${calculationId}: ${error.message}`);
            return false;
        }
        return (count ?? 0) > 0;
    }
    async canDeleteCalculation(calculationId, userId) {
        try {
            await this.checkCalculationOwnership(calculationId, userId);
            const basicInfo = await this.getCalculationBasicInfo(calculationId);
            if (basicInfo.status === 'completed') {
                this.logger.warn(`Attempt to delete completed calculation ${calculationId} by user ${userId}`);
                return false;
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Error validating deletion for calculation ${calculationId}: ${error.message}`);
            return false;
        }
    }
};
exports.CalculationValidationService = CalculationValidationService;
exports.CalculationValidationService = CalculationValidationService = CalculationValidationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationValidationService);
//# sourceMappingURL=calculation-validation.service.js.map