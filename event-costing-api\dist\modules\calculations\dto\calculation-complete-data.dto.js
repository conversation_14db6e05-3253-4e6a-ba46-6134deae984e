"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationCompleteDataDto = exports.CalculationCompleteDataMetadataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const calculation_detail_dto_1 = require("./calculation-detail.dto");
const line_item_dto_1 = require("../../calculation-items/dto/line-item.dto");
const packages_by_category_response_dto_1 = require("../../packages/dto/packages-by-category-response.dto");
const category_dto_1 = require("../../categories/dto/category.dto");
class CalculationCompleteDataMetadataDto {
    loadTime;
    cacheVersion;
    userId;
    errors;
    timestamp;
}
exports.CalculationCompleteDataMetadataDto = CalculationCompleteDataMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time taken to load the data in milliseconds',
        example: 250,
    }),
    __metadata("design:type", Number)
], CalculationCompleteDataMetadataDto.prototype, "loadTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache version for the response',
        example: '1.0',
    }),
    __metadata("design:type", String)
], CalculationCompleteDataMetadataDto.prototype, "cacheVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who requested the data',
        type: String,
        format: 'uuid',
    }),
    __metadata("design:type", String)
], CalculationCompleteDataMetadataDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Any errors encountered during data loading',
        type: [String],
        example: [],
    }),
    __metadata("design:type", Array)
], CalculationCompleteDataMetadataDto.prototype, "errors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the data was loaded',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", String)
], CalculationCompleteDataMetadataDto.prototype, "timestamp", void 0);
class CalculationCompleteDataDto {
    calculation;
    lineItems;
    packages;
    categories;
    metadata;
}
exports.CalculationCompleteDataDto = CalculationCompleteDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Calculation details',
        type: calculation_detail_dto_1.CalculationDetailDto,
    }),
    __metadata("design:type", calculation_detail_dto_1.CalculationDetailDto)
], CalculationCompleteDataDto.prototype, "calculation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All line items for the calculation',
        type: [line_item_dto_1.LineItemDto],
    }),
    __metadata("design:type", Array)
], CalculationCompleteDataDto.prototype, "lineItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available packages organized by category',
        type: [packages_by_category_response_dto_1.CategoryWithPackagesDto],
    }),
    __metadata("design:type", Array)
], CalculationCompleteDataDto.prototype, "packages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'All available categories',
        type: [category_dto_1.CategoryDto],
    }),
    __metadata("design:type", Array)
], CalculationCompleteDataDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata about the response',
        type: CalculationCompleteDataMetadataDto,
    }),
    __metadata("design:type", CalculationCompleteDataMetadataDto)
], CalculationCompleteDataDto.prototype, "metadata", void 0);
//# sourceMappingURL=calculation-complete-data.dto.js.map