import { SupabaseService } from 'src/core/supabase/supabase.service';
import { ListAdminTemplatesQueryDto } from '../dto/list-admin-templates.dto';
import { PaginatedAdminTemplatesResponse, TemplateDetailDto } from '../dto/template-summary.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';
import { TemplateVenueService } from './template-venue.service';
export declare class TemplateAdminService {
    private readonly supabaseService;
    private readonly templateVenueService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, templateVenueService: TemplateVenueService);
    findAllAdmin(queryDto: ListAdminTemplatesQueryDto): Promise<PaginatedAdminTemplatesResponse>;
    findOneAdmin(id: string): Promise<TemplateDetailDto>;
    updateTemplate(id: string, updateDto: UpdateTemplateDto): Promise<TemplateDetailDto>;
    updateTemplateStatus(id: string, isActive: boolean): Promise<TemplateDetailDto>;
    deleteTemplate(id: string): Promise<void>;
}
