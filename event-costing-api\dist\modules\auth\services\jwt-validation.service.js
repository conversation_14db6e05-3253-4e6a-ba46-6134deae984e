"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtValidationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtValidationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const auth_event_logger_service_1 = require("./auth-event-logger.service");
let JwtValidationService = JwtValidationService_1 = class JwtValidationService {
    supabaseService;
    configService;
    authEventLogger;
    logger = new common_1.Logger(JwtValidationService_1.name);
    constructor(supabaseService, configService, authEventLogger) {
        this.supabaseService = supabaseService;
        this.configService = configService;
        this.authEventLogger = authEventLogger;
    }
    async validateUserFromJwtPayload(payload) {
        try {
            this.logger.verbose(`Validating user from JWT payload: ${payload.email}`);
            if (!payload.sub) {
                this.logger.warn('JWT payload missing subject (user ID)');
                throw new common_1.UnauthorizedException('Invalid token: missing user ID');
            }
            const adminClient = this.supabaseService.getClient();
            const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(payload.sub);
            if (userError || !userData || !userData.user) {
                this.logger.warn(`User not found for ID: ${payload.sub}`);
                throw new common_1.UnauthorizedException('User not found');
            }
            const { data: profile, error: profileError } = await adminClient
                .from('profiles')
                .select(`
          id,
          role_id,
          roles ( role_name )
        `)
                .eq('id', payload.sub)
                .maybeSingle();
            if (profileError) {
                this.logger.error(`Error fetching profile: ${profileError.message}`);
            }
            const user = {
                id: payload.sub,
                email: payload.email,
                app_metadata: userData.user.app_metadata || {},
                user_metadata: userData.user.user_metadata || {},
                aud: payload.aud,
                created_at: userData.user.created_at ||
                    (payload.iat
                        ? new Date(payload.iat * 1000).toISOString()
                        : new Date().toISOString()),
                profile: profile || null,
            };
            this.logger.verbose(`JWT validation successful for user: ${user.email}`);
            return user;
        }
        catch (error) {
            const message = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`JWT validation error: ${message}`, error instanceof Error ? error.stack : undefined);
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Invalid token or authentication failure');
        }
    }
};
exports.JwtValidationService = JwtValidationService;
exports.JwtValidationService = JwtValidationService = JwtValidationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        config_1.ConfigService,
        auth_event_logger_service_1.AuthEventLoggerService])
], JwtValidationService);
//# sourceMappingURL=jwt-validation.service.js.map