"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminDivisionsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDivisionsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const divisions_service_1 = require("./divisions.service");
const create_division_dto_1 = require("./dto/create-division.dto");
const update_division_dto_1 = require("./dto/update-division.dto");
const division_dto_1 = require("./dto/division.dto");
let AdminDivisionsController = AdminDivisionsController_1 = class AdminDivisionsController {
    divisionsService;
    logger = new common_1.Logger(AdminDivisionsController_1.name);
    constructor(divisionsService) {
        this.divisionsService = divisionsService;
    }
    async createDivision(createDto) {
        this.logger.log(`Admin request to create division: ${createDto.name}`);
        return await this.divisionsService.createDivision(createDto);
    }
    async findOne(id) {
        this.logger.log(`Admin request to get division ID: ${id}`);
        return await this.divisionsService.findOneById(id);
    }
    async updateDivision(id, updateDto) {
        this.logger.log(`Admin request to update division ID: ${id}`);
        return await this.divisionsService.updateDivision(id, updateDto);
    }
    async deleteDivision(id) {
        this.logger.log(`Admin request to delete division ID: ${id}`);
        await this.divisionsService.deleteDivision(id);
    }
};
exports.AdminDivisionsController = AdminDivisionsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(admin_role_guard_1.AdminRoleGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new division' }),
    (0, swagger_1.ApiBody)({ type: create_division_dto_1.CreateDivisionDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Division created',
        type: division_dto_1.DivisionDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (Name exists)' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_division_dto_1.CreateDivisionDto]),
    __metadata("design:returntype", Promise)
], AdminDivisionsController.prototype, "createDivision", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific division by ID (Admin)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Division found',
        type: division_dto_1.DivisionDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Division not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminDivisionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(admin_role_guard_1.AdminRoleGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Update a division' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: update_division_dto_1.UpdateDivisionDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Division updated',
        type: division_dto_1.DivisionDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Division not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (Name exists)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_division_dto_1.UpdateDivisionDto]),
    __metadata("design:returntype", Promise)
], AdminDivisionsController.prototype, "updateDivision", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(admin_role_guard_1.AdminRoleGuard),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a division' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Division deleted' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Division not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden (Admin Role)' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict (Division in use)' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminDivisionsController.prototype, "deleteDivision", null);
exports.AdminDivisionsController = AdminDivisionsController = AdminDivisionsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Divisions'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/divisions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [divisions_service_1.DivisionsService])
], AdminDivisionsController);
//# sourceMappingURL=admin-divisions.controller.js.map