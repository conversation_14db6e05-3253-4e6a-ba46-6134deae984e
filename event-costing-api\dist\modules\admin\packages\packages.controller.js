"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const packages_service_js_1 = require("./packages.service.js");
const create_package_dto_1 = require("./dto/create-package.dto");
const update_package_dto_1 = require("./dto/update-package.dto");
const update_package_status_dto_1 = require("./dto/update-package-status.dto");
const package_dto_1 = require("./dto/package.dto");
const package_list_query_dto_1 = require("./dto/package-list-query.dto");
const batch_update_packages_dto_1 = require("./dto/batch-update-packages.dto");
const paginated_response_dto_1 = require("../../../shared/dtos/paginated-response.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
let PackagesController = class PackagesController {
    packagesService;
    constructor(packagesService) {
        this.packagesService = packagesService;
    }
    create(createPackageDto) {
        return this.packagesService.create(createPackageDto);
    }
    findAll(queryDto) {
        return this.packagesService.findAll(queryDto);
    }
    findOne(id) {
        return this.packagesService.findOne(id);
    }
    update(id, updatePackageDto) {
        return this.packagesService.update(id, updatePackageDto);
    }
    remove(id) {
        return this.packagesService.remove(id);
    }
    batchUpdate(batchUpdateDto) {
        return this.packagesService.batchUpdate(batchUpdateDto);
    }
    updateStatus(id, updateStatusDto) {
        return this.packagesService.updateStatus(id, updateStatusDto.isActive);
    }
};
exports.PackagesController = PackagesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new package' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The package has been successfully created.',
        type: package_dto_1.PackageDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_package_dto_1.CreatePackageDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all packages with filtering and pagination' }),
    (0, swagger_1.ApiQuery)({ type: package_list_query_dto_1.PackageListQueryDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of packages.',
        type: () => paginated_response_dto_1.PaginatedResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [package_list_query_dto_1.PackageListQueryDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a package by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Package UUID', type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The package details.',
        type: package_dto_1.PackageDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a package by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Package UUID', type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The package has been successfully updated.',
        type: package_dto_1.PackageDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_package_dto_1.UpdatePackageDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a package by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Package UUID', type: String }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'The package has been successfully deleted.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('batch-update'),
    (0, swagger_1.ApiOperation)({ summary: 'Batch update multiple packages' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The packages have been successfully updated.',
        schema: {
            type: 'object',
            properties: {
                updatedCount: {
                    type: 'number',
                    description: 'Number of packages updated',
                },
                errors: {
                    type: 'array',
                    description: 'Errors encountered during update',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string', description: 'Package ID' },
                            error: { type: 'string', description: 'Error message' },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [batch_update_packages_dto_1.BatchUpdatePackagesDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "batchUpdate", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update package status (active/inactive)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Package UUID', type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The package status has been successfully updated.',
        type: package_dto_1.PackageDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Package not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_package_status_dto_1.UpdatePackageStatusDto]),
    __metadata("design:returntype", Promise)
], PackagesController.prototype, "updateStatus", null);
exports.PackagesController = PackagesController = __decorate([
    (0, swagger_1.ApiTags)('Admin | Packages'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/packages'),
    __metadata("design:paramtypes", [packages_service_js_1.PackagesService])
], PackagesController);
//# sourceMappingURL=packages.controller.js.map