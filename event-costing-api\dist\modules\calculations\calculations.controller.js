"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CalculationsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationsController = void 0;
const common_1 = require("@nestjs/common");
const calculations_service_1 = require("./calculations.service");
const calculation_template_service_1 = require("./calculation-template.service");
const calculation_logic_service_1 = require("./calculation-logic.service");
const create_calculation_dto_1 = require("./dto/create-calculation.dto");
const create_calculation_from_template_dto_1 = require("./dto/create-calculation-from-template.dto");
const list_calculations_dto_1 = require("./dto/list-calculations.dto");
const calculation_detail_dto_1 = require("./dto/calculation-detail.dto");
const update_calculation_dto_1 = require("./dto/update-calculation.dto");
const paginated_response_dto_1 = require("../../shared/dtos/paginated-response.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const calculation_totals_dto_1 = require("./dto/calculation-totals.dto");
const update_calculation_status_dto_1 = require("./dto/update-calculation-status.dto");
const calculation_id_response_dto_1 = require("./dto/calculation-id-response.dto");
const calculation_complete_data_dto_1 = require("./dto/calculation-complete-data.dto");
const calculation_complete_data_service_1 = require("./services/calculation-complete-data.service");
let CalculationsController = CalculationsController_1 = class CalculationsController {
    calculationsService;
    calculationTemplateService;
    calculationLogicService;
    calculationCompleteDataService;
    logger = new common_1.Logger(CalculationsController_1.name);
    constructor(calculationsService, calculationTemplateService, calculationLogicService, calculationCompleteDataService) {
        this.calculationsService = calculationsService;
        this.calculationTemplateService = calculationTemplateService;
        this.calculationLogicService = calculationLogicService;
        this.calculationCompleteDataService = calculationCompleteDataService;
    }
    async createCalculation(createCalculationDto, user) {
        this.logger.log(`User ${user.email} creating calculation: ${JSON.stringify({
            ...createCalculationDto,
            venue_ids: createCalculationDto.venue_ids?.length
                ? `[${createCalculationDto.venue_ids.length} venues]`
                : 'none',
        })}`);
        const calculationId = await this.calculationsService.createCalculation(createCalculationDto, user);
        return { id: calculationId };
    }
    async createFromTemplate(templateId, customizationDto, user) {
        this.logger.log(`User ${user.email} creating calculation from template ID: ${templateId} with customization: ${JSON.stringify({
            ...customizationDto,
            venueIds: customizationDto.venueIds?.length
                ? `[${customizationDto.venueIds.length} venues]`
                : 'none',
        })}`);
        const calculationId = await this.calculationTemplateService.createFromTemplate(templateId, customizationDto, user);
        return { id: calculationId };
    }
    async findUserCalculations(queryDto, user) {
        this.logger.log(`User ${user.email} listing calculations with query: ${JSON.stringify(queryDto)}`);
        return this.calculationsService.findUserCalculations(user, queryDto);
    }
    async findCalculationById(id, user) {
        this.logger.log(`User ${user.email} fetching calculation ID: ${id}`);
        return this.calculationsService.findCalculationById(id, user);
    }
    async getCompleteCalculationData(id, user) {
        this.logger.log(`User ${user.email} fetching complete calculation data for ID: ${id}`);
        return this.calculationCompleteDataService.getCompleteData(id, user);
    }
    async getTotals(id, user) {
        await this.calculationsService.checkCalculationOwnership(id, user.id);
        const totals = await this.calculationLogicService.fetchAndCalculateTotals(id);
        if (!totals) {
            this.logger.warn(`Could not fetch/calculate totals for calc ID: ${id} after ownership check`);
            throw new common_1.NotFoundException(`Calculation totals could not be determined for ID: ${id}`);
        }
        return totals;
    }
    async getCalculationSummary(id, user) {
        this.logger.log(`User ${user.email} fetching calculation summary for ID: ${id}`);
        return this.calculationsService.getCalculationSummary(id, user);
    }
    async updateCalculation(id, updateCalculationDto, user) {
        this.logger.log(`User ${user.email} updating calculation ID: ${id} with data: ${JSON.stringify({
            ...updateCalculationDto,
            venue_ids: updateCalculationDto.venue_ids
                ? Array.isArray(updateCalculationDto.venue_ids)
                    ? `[${updateCalculationDto.venue_ids.length} venues]`
                    : updateCalculationDto.venue_ids
                : 'unchanged',
        })}`);
        return this.calculationsService.updateCalculation(id, updateCalculationDto, user);
    }
    async updateStatus(id, updateStatusDto, user) {
        this.logger.log(`User ${user.email} attempting update status for calc ${id} to ${updateStatusDto.status}`);
        await this.calculationsService.updateStatus(id, updateStatusDto, user.id);
    }
    async remove(id, user) {
        this.logger.log(`User ${user.email} deleting calculation ID: ${id}`);
        return this.calculationsService.deleteCalculation(id, user);
    }
    async recalculateTotals(id, user) {
        this.logger.log(`User ${user.email} recalculating totals for calculation ID: ${id}`);
        await this.calculationsService.checkCalculationOwnership(id, user.id);
        await this.calculationLogicService.recalculateTotals(id);
    }
};
exports.CalculationsController = CalculationsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new blank calculation' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Calculation created',
        type: calculation_id_response_dto_1.CalculationIdResponse,
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_calculation_dto_1.CreateCalculationDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "createCalculation", null);
__decorate([
    (0, common_1.Post)('from-template/:templateId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new calculation from a template with customization',
    }),
    (0, swagger_1.ApiParam)({ name: 'templateId', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Calculation created from template',
        type: calculation_id_response_dto_1.CalculationIdResponse,
    }),
    __param(0, (0, common_1.Param)('templateId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_calculation_from_template_dto_1.CreateCalculationFromTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "createFromTemplate", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List calculations for the current user' }),
    (0, swagger_1.ApiOkResponse)({ type: (paginated_response_dto_1.PaginatedResponseDto) }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_calculations_dto_1.ListCalculationsDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "findUserCalculations", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get calculation details by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: calculation_detail_dto_1.CalculationDetailDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "findCalculationById", null);
__decorate([
    (0, common_1.Get)(':id/complete-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get complete calculation data in a single API call',
        description: 'Consolidated endpoint that returns calculation details, line items, packages by category, and categories in a single response. Replaces the need for 4 separate API calls from the frontend.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Complete calculation data with metadata',
        type: calculation_complete_data_dto_1.CalculationCompleteDataDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Failed to load calculation data.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "getCompleteCalculationData", null);
__decorate([
    (0, common_1.Get)(':id/totals'),
    (0, swagger_1.ApiOperation)({ summary: 'Get calculated totals for a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: calculation_totals_dto_1.CalculationTotalsDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "getTotals", null);
__decorate([
    (0, common_1.Get)(':id/summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get calculation summary for template creation' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Calculation summary with package and custom item counts',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', format: 'uuid' },
                name: { type: 'string' },
                total: { type: 'number' },
                standardPackagesCount: { type: 'number' },
                customItemsCount: { type: 'number' },
                currency: {
                    type: 'object',
                    properties: { id: { type: 'string' }, code: { type: 'string' } },
                },
                attendees: { type: 'number', nullable: true },
                event_type: { type: 'string', nullable: true },
                city: {
                    type: 'object',
                    nullable: true,
                    properties: { id: { type: 'string' }, name: { type: 'string' } },
                },
                venues: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: { id: { type: 'string' }, name: { type: 'string' } },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "getCalculationSummary", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update calculation details (excluding status, taxes, discount)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiOkResponse)({ type: calculation_detail_dto_1.CalculationDetailDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_calculation_dto_1.UpdateCalculationDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "updateCalculation", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Update the status of a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiNoContentResponse)({
        description: 'Calculation status updated successfully.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Invalid status transition requested.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_calculation_status_dto_1.UpdateCalculationStatusDto, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Calculation soft deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or user does not have permission.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'An error occurred during deletion.',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/recalculate'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Recalculate totals for a calculation' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiNoContentResponse)({
        description: 'Calculation totals recalculated successfully.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Calculation not found or access denied.',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'An error occurred during recalculation.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CalculationsController.prototype, "recalculateTotals", null);
exports.CalculationsController = CalculationsController = CalculationsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Calculations'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('calculations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [calculations_service_1.CalculationsService,
        calculation_template_service_1.CalculationTemplateService,
        calculation_logic_service_1.CalculationLogicService,
        calculation_complete_data_service_1.CalculationCompleteDataService])
], CalculationsController);
//# sourceMappingURL=calculations.controller.js.map