import { User } from '@supabase/supabase-js';
import { CustomItemsService } from '../services/custom-items.service';
import { AddCustomLineItemDto } from '../dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from '../dto/update-line-item.dto';
import { CustomItemDto } from '../dto/custom-item.dto';
import { ItemIdResponse } from '../dto/item-id-response.dto';
export declare class CustomItemsController {
    private readonly customItemsService;
    private readonly logger;
    constructor(customItemsService: CustomItemsService);
    getCustomItems(calcId: string): Promise<CustomItemDto[]>;
    getCustomItemById(calcId: string, itemId: string): Promise<CustomItemDto>;
    addCustomItem(calcId: string, addDto: AddCustomLineItemDto, user: User): Promise<ItemIdResponse>;
    updateCustomItem(calcId: string, itemId: string, updateDto: UpdateLineItemDto, user: User): Promise<CustomItemDto>;
    deleteCustomItem(calcId: string, itemId: string, user: User): Promise<void>;
}
