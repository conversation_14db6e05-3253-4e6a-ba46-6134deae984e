{"version": 3, "file": "calculation-status.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/calculations/services/calculation-status.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAOwB;AACxB,8EAA0E;AAE1E,8EAAqE;AAO9D,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGN;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAEpE,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAKjE,KAAK,CAAC,YAAY,CAChB,EAAU,EACV,eAA2C,EAC3C,MAAc;QAEd,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC;QACzC,MAAM,eAAe,GAAG;YACtB,2CAAiB,CAAC,KAAK;YACvB,2CAAiB,CAAC,SAAS;YAC3B,2CAAiB,CAAC,QAAQ;SAC3B,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,kBAAkB,SAAS,8BAA8B,EAAE,EAAE,CAC9D,CAAC;YACF,MAAM,IAAI,4BAAmB,CAAC,mBAAmB,SAAS,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,QAAQ,MAAM,+CAA+C,EAAE,OAAO,SAAS,EAAE,CAClF,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;aAC5D,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,oBAAoB,CAAC;aAC5B,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;aACZ,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,MAAM,EAAE,CAAC;QAEZ,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8BAA8B,EAAE,uBAAuB,UAAU,CAAC,OAAO,EAAE,CAC5E,CAAC;YACF,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACnC,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;YACtE,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,WAAW,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,QAAQ,MAAM,+CAA+C,EAAE,EAAE,CAClE,CAAC;YACF,MAAM,IAAI,2BAAkB,CAAC,oCAAoC,CAAC,CAAC;QACrE,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,4BAAmB,CAC3B,kCAAkC,WAAW,CAAC,MAAM,OAAO,SAAS,EAAE,CACvE,CAAC;QACJ,CAAC;QAGD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aAC1C,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC7B,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAEhB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,EAAE,KAAK,WAAW,CAAC,OAAO,EAAE,EACvE,WAAW,CAAC,KAAK,CAClB,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,sCAAsC,CACvC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,8CAA8C,EAAE,OAAO,SAAS,EAAE,CACnE,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,aAAqB;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,QAAQ,CAAC;aAChB,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;aACvB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,MAAM,EAAE,CAAC;QAEZ,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,aAAa,KAAK,KAAK,CAAC,OAAO,EAAE,CAC3E,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,uBAAuB,aAAa,aAAa,CAClD,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAKO,uBAAuB,CAC7B,aAAqB,EACrB,SAAiB;QAGjB,MAAM,gBAAgB,GAA6B;YACjD,CAAC,2CAAiB,CAAC,KAAK,CAAC,EAAE;gBACzB,2CAAiB,CAAC,SAAS;gBAC3B,2CAAiB,CAAC,QAAQ;gBAC1B,2CAAiB,CAAC,KAAK;aACxB;YACD,CAAC,2CAAiB,CAAC,SAAS,CAAC,EAAE;gBAC7B,2CAAiB,CAAC,KAAK;gBACvB,2CAAiB,CAAC,QAAQ;aAC3B;YACD,CAAC,2CAAiB,CAAC,QAAQ,CAAC,EAAE;gBAC5B,2CAAiB,CAAC,KAAK;aACxB;SACF,CAAC;QAEF,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QACjE,OAAO,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,aAAqB;QACjD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAEjE,MAAM,gBAAgB,GAA6B;YACjD,CAAC,2CAAiB,CAAC,KAAK,CAAC,EAAE;gBACzB,2CAAiB,CAAC,SAAS;gBAC3B,2CAAiB,CAAC,QAAQ;aAC3B;YACD,CAAC,2CAAiB,CAAC,SAAS,CAAC,EAAE;gBAC7B,2CAAiB,CAAC,KAAK;gBACvB,2CAAiB,CAAC,QAAQ;aAC3B;YACD,CAAC,2CAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,2CAAiB,CAAC,KAAK,CAAC;SACxD,CAAC;QAEF,OAAO,gBAAgB,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,cAAwB,EACxB,SAA4B,EAC5B,MAAc;QAEd,MAAM,OAAO,GAAG;YACd,OAAO,EAAE,EAAc;YACvB,MAAM,EAAE,EAAc;SACvB,CAAC;QAEF,KAAK,MAAM,EAAE,IAAI,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;gBAC3D,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,CAClE,CAAC;gBACF,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,MAAc,EACd,MAAc,EACd,QAAgB,EAAE,EAClB,SAAiB,CAAC;QAElB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,iDAAiD,CAAC;aACzD,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;aACxB,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;aACpB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;aACvB,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;aACzC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAErC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,MAAM,aAAa,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE,CACvF,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,4CAA4C,CAC7C,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAElD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;aACnC,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,QAAQ,CAAC;aAChB,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;aACxB,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6CAA6C,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE,CACxE,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,uCAAuC,CACxC,CAAC;QACJ,CAAC;QAGD,MAAM,UAAU,GAA2B,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAClB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAKD,oBAAoB,CAAC,MAAc;QAEjC,OAAO,MAAM,KAAK,2CAAiB,CAAC,KAAK,CAAC;IAC5C,CAAC;IAKD,oBAAoB,CAAC,MAAc;QAEjC,OAAO,CAAC,2CAAiB,CAAC,KAAK,EAAE,2CAAiB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CACnE,MAA2B,CAC5B,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,aAAqB;QAI9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAEjE,IAAI,aAAa,KAAK,2CAAiB,CAAC,KAAK,EAAE,CAAC;QAIhD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AA/RY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,wBAAwB,CA+RpC"}