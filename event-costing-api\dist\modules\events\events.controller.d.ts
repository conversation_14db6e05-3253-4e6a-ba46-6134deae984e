import { EventsService } from './events.service';
import { EventDto } from './dto/event.dto';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { EventStatus } from './dto/event-status.enum';
export declare class EventsController {
    private readonly eventsService;
    private readonly logger;
    constructor(eventsService: EventsService);
    create(createEventDto: CreateEventDto): Promise<EventDto>;
    findAll(search?: string, status?: EventStatus, clientId?: string, contactId?: string): Promise<EventDto[]>;
    findOne(id: string): Promise<EventDto>;
    update(id: string, updateEventDto: UpdateEventDto): Promise<EventDto>;
    remove(id: string): Promise<void>;
}
