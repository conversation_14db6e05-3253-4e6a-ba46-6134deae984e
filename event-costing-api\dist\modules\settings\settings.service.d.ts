import { SupabaseService } from '../../core/supabase/supabase.service';
import { SettingDto } from './dto/setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
export declare class SettingsService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    constructor(supabaseService: SupabaseService);
    getSetting(key: string): Promise<SettingDto>;
    updateSetting(key: string, updateDto: UpdateSettingDto): Promise<SettingDto>;
}
