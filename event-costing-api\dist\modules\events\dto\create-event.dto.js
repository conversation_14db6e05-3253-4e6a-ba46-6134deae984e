"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEventDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const event_status_enum_1 = require("./event-status.enum");
class CreateEventDto {
    event_name;
    client_id;
    event_start_datetime;
    event_end_datetime;
    status = event_status_enum_1.EventStatus.PLANNING;
    venue_details;
    primary_contact_id;
    notes;
}
exports.CreateEventDto = CreateEventDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the event',
        example: 'Gathering of MKC',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateEventDto.prototype, "event_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Associated client ID', format: 'uuid' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "client_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event start date and time (ISO 8601)',
        example: '2025-01-01T00:00:00.000Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "event_start_datetime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event end date and time (ISO 8601)',
        example: '2025-01-01T00:00:00.000Z',
        format: 'date-time',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "event_end_datetime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the event',
        enum: event_status_enum_1.EventStatus,
        default: event_status_enum_1.EventStatus.PLANNING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(event_status_enum_1.EventStatus),
    __metadata("design:type", String)
], CreateEventDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details about the event venue' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "venue_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Internal primary contact user ID',
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "primary_contact_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'General notes about the event' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEventDto.prototype, "notes", void 0);
//# sourceMappingURL=create-event.dto.js.map