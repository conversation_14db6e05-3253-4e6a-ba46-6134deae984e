"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let CategoriesService = CategoriesService_1 = class CategoriesService {
    supabaseService;
    logger = new common_1.Logger(CategoriesService_1.name);
    tableName = 'categories';
    selectFields = 'id, code, name, description, icon, display_order, created_at, updated_at';
    uniqueConstraint = 'categories_code_key';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(this.selectFields)
            .order('display_order', { ascending: true });
        if (error) {
            this.logger.error(`Failed to fetch categories: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve categories.');
        }
        return data || [];
    }
    async findOneById(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select(this.selectFields)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found.`);
        }
        return data;
    }
    async createCategory(createDto) {
        this.logger.debug(`Creating category: ${createDto.code} - ${createDto.name}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .insert({
            code: createDto.code,
            name: createDto.name,
            description: createDto.description,
            icon: createDto.icon,
        })
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === '23505' &&
                error.message.includes(this.uniqueConstraint)) {
                this.logger.warn(`Attempted to create duplicate category code: ${createDto.code}`);
                throw new common_1.ConflictException(`A category with the code "${createDto.code}" already exists.`);
            }
            this.logger.error(`Failed to create category: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not create category.');
        }
        if (!data) {
            this.logger.error('Category insert succeeded but returned no data.');
            throw new common_1.InternalServerErrorException('Failed to retrieve newly created category.');
        }
        this.logger.log(`Successfully created category ID: ${data.id}, Code: ${data.code}`);
        return data;
    }
    async updateCategory(id, updateDto) {
        this.logger.debug(`Updating category ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {};
        if (updateDto.name !== undefined) {
            updateData.name = updateDto.name;
        }
        if (updateDto.description !== undefined) {
            updateData.description = updateDto.description;
        }
        if (updateDto.icon !== undefined) {
            updateData.icon = updateDto.icon;
        }
        if (Object.keys(updateData).length === 0) {
            return this.findOneById(id);
        }
        const { data, error } = await supabase
            .from(this.tableName)
            .update(updateData)
            .eq('id', id)
            .select(this.selectFields)
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                this.logger.warn(`Category not found for update: ID ${id}`);
                throw new common_1.NotFoundException(`Category with ID ${id} not found.`);
            }
            this.logger.error(`Failed to update category ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update category.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found.`);
        }
        this.logger.log(`Successfully updated category ID: ${id}`);
        return data;
    }
    async deleteCategory(id) {
        this.logger.debug(`Deleting category ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.tableName)
            .delete()
            .eq('id', id);
        if (error) {
            this.logger.error(`Failed to delete category ${id}: ${error.message}`, error.stack);
            if (error.code === '23503') {
                this.logger.warn(`Attempted to delete category ${id} which is still referenced.`);
                throw new common_1.ConflictException(`Cannot delete category because it is referenced by other records (e.g., packages).`);
            }
            throw new common_1.InternalServerErrorException('Could not delete category.');
        }
        if (count === 0) {
            this.logger.warn(`Category not found for deletion: ID ${id}`);
            throw new common_1.NotFoundException(`Category with ID ${id} not found.`);
        }
        this.logger.log(`Successfully deleted category ID: ${id}`);
    }
    async updateCategoryOrder(categories) {
        this.logger.log(`Updating display order for ${categories.length} categories`);
        const supabase = this.supabaseService.getClient();
        try {
            const { error: txError } = await supabase.rpc('begin_transaction');
            if (txError) {
                this.logger.error(`Failed to start transaction: ${txError.message}`, txError.stack);
                throw txError;
            }
            for (const category of categories) {
                const { error } = await supabase
                    .from(this.tableName)
                    .update({
                    display_order: category.display_order,
                    updated_at: new Date().toISOString(),
                })
                    .eq('id', category.id);
                if (error) {
                    await supabase.rpc('rollback_transaction');
                    this.logger.error(`Failed to update category ${category.id}: ${error.message}`, error.stack);
                    throw error;
                }
            }
            const { error: commitError } = await supabase.rpc('commit_transaction');
            if (commitError) {
                this.logger.error(`Failed to commit transaction: ${commitError.message}`, commitError.stack);
                throw commitError;
            }
            const { data, error } = await supabase
                .from(this.tableName)
                .select(this.selectFields)
                .order('display_order', { ascending: true });
            if (error) {
                this.logger.error(`Failed to fetch updated categories: ${error.message}`, error.stack);
                throw error;
            }
            this.logger.log('Category order updated successfully');
            return {
                success: true,
                message: 'Category order updated successfully',
                categories: data,
            };
        }
        catch (error) {
            this.logger.error(`Failed to update category order: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to update category order');
        }
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = CategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CategoriesService);
//# sourceMappingURL=categories.service.js.map