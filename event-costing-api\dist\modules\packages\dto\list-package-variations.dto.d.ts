export declare enum PackageSortField {
    NAME = "name",
    PRICE = "price",
    CATEGORY = "category"
}
export declare enum SortDirection {
    ASC = "asc",
    DESC = "desc"
}
export declare class ListPackageVariationsDto {
    categoryId?: string;
    cityId?: string;
    venueId?: string;
    venueIds?: string[];
    currencyId: string;
    currentSelectionIds?: string[];
    search?: string;
    sortBy?: PackageSortField;
    sortOrder?: SortDirection;
    limit?: number;
    offset?: number;
}
