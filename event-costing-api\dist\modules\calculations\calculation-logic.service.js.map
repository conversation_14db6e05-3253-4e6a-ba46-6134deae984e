{"version": 3, "file": "calculation-logic.service.js", "sourceRoot": "", "sources": ["../../../src/modules/calculations/calculation-logic.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2EAAuE;AA+BhE,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGL;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnE,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEjE,KAAK,CAAC,uBAAuB,CAC3B,MAAc;QAEd,MAAM,EACJ,IAAI,EACJ,KAAK,GACN,GACC,MAAM,IAAI,CAAC,eAAe;aACvB,SAAS,EAAE;aACX,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CACL;;;;;;;;SAQD,CACA;aACA,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;aAChB,WAAW,EAAyB,CAAC;QAE1C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,OAAe,CAAC;YACpB,IAAI,KAAK,GAAuB,SAAS,CAAC;YAE1C,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBACxB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACtB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAEvD,IAAI,CAAC;oBACH,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAClC,CAAC;gBAAC,MAAM,CAAC;oBAEP,OAAO,GAAG,gCAAgC,CAAC;gBAC7C,CAAC;YACH,CAAC;iBAAM,CAAC;gBAEN,OAAO;oBACL,OAAO,KAAK,KAAK,QAAQ;wBACvB,CAAC,CAAC,KAAK;wBACP,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,SAAS;4BACvD,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;4BAClB,CAAC,CAAC,sBAAsB,CAAC;YACjC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iDAAiD,MAAM,KAAK,OAAO,EAAE,EACrE,KAAK,CACN,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,0DAA0D,MAAM,EAAE,CACnE,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YAC1C,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAY,EAAE,EAAE;gBAC3D,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBAC1C,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC;gBAChC,CAAC;qBAAM,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;oBACrE,gBAAgB,GAAG,cAAc,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;gBACvD,CAAC;gBACD,OAAO,GAAG,GAAG,gBAAgB,CAAC;YAChC,CAAC,EAAE,CAAC,CAAC,CAAC;QACR,CAAC;QAED,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;QACpD,IAAI,OAAO,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YAC9C,cAAc,GAAG,qBAAqB,CAAC;QACzC,CAAC;QAED,MAAM,SAAS,GAAyB;YACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC;YAC5B,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC;YACnB,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,cAAc;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;YACtB,SAAS,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;YAC/B,eAAe,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC;SAC5C,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAOD,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,MAAM,EAAE,CAAC,CAAC;QAE1E,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,gCAAgC,EAAE;YACrE,gBAAgB,EAAE,MAAM;SACzB,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,8CAA8C,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE,EACxE,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,MAAM,EAAE,CAAC,CAAC;IAC3E,CAAC;CACF,CAAA;AA7HY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAImC,kCAAe;GAHlD,uBAAuB,CA6HnC"}