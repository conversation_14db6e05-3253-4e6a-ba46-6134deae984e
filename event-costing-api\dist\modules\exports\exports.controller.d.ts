import { User } from '@supabase/supabase-js';
import { ExportsService } from './exports.service';
import { CreateExportDto } from './dto/create-export.dto';
import { ExportResponseDto } from './dto/export-response.dto';
import { ExportStatusResponseDto } from './dto/export-status-response.dto';
export declare class ExportsController {
    private readonly exportsService;
    private readonly logger;
    constructor(exportsService: ExportsService);
    initiateExport(createExportDto: CreateExportDto, user: User): Promise<ExportResponseDto>;
    getExportsByCalculation(calculationId: string, user: User): Promise<ExportStatusResponseDto[]>;
    getExportStatus(id: string, user: User): Promise<ExportStatusResponseDto>;
}
