import { ServiceCategoriesService } from './service-categories.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
export declare class ServiceCategoriesController {
    private readonly serviceCategoriesService;
    private readonly logger;
    constructor(serviceCategoriesService: ServiceCategoriesService);
    create(createDto: CreateServiceCategoryDto): Promise<ServiceCategoryDto>;
    findAll(): Promise<ServiceCategoryDto[]>;
    findOne(id: string): Promise<ServiceCategoryDto>;
    update(id: string, updateDto: UpdateServiceCategoryDto): Promise<ServiceCategoryDto>;
    remove(id: string): Promise<void>;
}
