"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var StorageService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../supabase/supabase.service");
let StorageService = StorageService_1 = class StorageService {
    supabaseService;
    logger = new common_1.Logger(StorageService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
        this.initializeBuckets().catch(error => {
            this.logger.error('Failed to initialize storage buckets', error);
        });
    }
    async initializeBuckets() {
        this.logger.log('Initializing storage buckets');
        const requiredBuckets = ['profiles', 'calculation-exports'];
        for (const bucketName of requiredBuckets) {
            await this.ensureBucketExists(bucketName);
        }
    }
    async ensureBucketExists(bucketName) {
        this.logger.log(`Ensuring bucket exists: ${bucketName}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: buckets, error: listError } = await supabase.storage.listBuckets();
            if (listError) {
                this.logger.error(`Failed to list buckets: ${listError.message}`, listError.stack);
                throw new common_1.InternalServerErrorException('Failed to check if bucket exists');
            }
            const bucketExists = buckets.some(bucket => bucket.name === bucketName);
            if (!bucketExists) {
                this.logger.log(`Bucket ${bucketName} does not exist, creating it`);
                const { error: createError } = await supabase.storage.createBucket(bucketName, {
                    public: true,
                });
                if (createError) {
                    this.logger.error(`Failed to create bucket ${bucketName}: ${createError.message}`, createError.stack);
                    throw new common_1.InternalServerErrorException(`Failed to create bucket ${bucketName}`);
                }
                this.logger.log(`Bucket ${bucketName} created successfully`);
            }
            else {
                this.logger.log(`Bucket ${bucketName} already exists`);
            }
        }
        catch (error) {
            this.logger.error(`Unexpected error checking/creating bucket ${bucketName}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw new common_1.InternalServerErrorException(`Failed to ensure bucket ${bucketName} exists`);
        }
    }
    async uploadFile(bucketName, filePath, fileBuffer, contentType, options) {
        this.logger.log(`Uploading file to ${bucketName}/${filePath}`);
        await this.ensureBucketExists(bucketName);
        const supabase = this.supabaseService.getClient();
        try {
            const { error: uploadError } = await supabase.storage
                .from(bucketName)
                .upload(filePath, fileBuffer, {
                upsert: options?.upsert ?? true,
                contentType,
            });
            if (uploadError) {
                this.logger.error(`Failed to upload file to ${bucketName}/${filePath}: ${uploadError.message}`, uploadError.stack);
                throw new common_1.InternalServerErrorException(`Failed to upload file: ${uploadError.message}`);
            }
            this.logger.log(`File uploaded successfully to ${bucketName}/${filePath}`);
            return filePath;
        }
        catch (error) {
            if (error instanceof common_1.InternalServerErrorException) {
                throw error;
            }
            this.logger.error(`Unexpected error uploading file to ${bucketName}/${filePath}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw new common_1.InternalServerErrorException('Failed to upload file due to an unexpected error.');
        }
    }
    async getSignedUrl(bucketName, filePath, expiresIn = 3600) {
        if (!filePath) {
            return null;
        }
        this.logger.debug(`Generating signed URL for ${bucketName}/${filePath}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: urlData, error: urlError } = await supabase.storage
                .from(bucketName)
                .createSignedUrl(filePath, expiresIn);
            if (urlError) {
                this.logger.error(`Failed to create signed URL for ${bucketName}/${filePath}: ${urlError.message}`);
                return null;
            }
            return urlData?.signedUrl ?? null;
        }
        catch (error) {
            this.logger.error(`Unexpected error generating signed URL for ${bucketName}/${filePath}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            return null;
        }
    }
    getPublicUrl(bucketName, filePath) {
        this.logger.debug(`Generating public URL for ${bucketName}/${filePath}`);
        const supabase = this.supabaseService.getClient();
        const { data } = supabase.storage.from(bucketName).getPublicUrl(filePath);
        return data.publicUrl;
    }
    async deleteFile(bucketName, filePath) {
        this.logger.log(`Deleting file ${bucketName}/${filePath}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase.storage
            .from(bucketName)
            .remove([filePath]);
        if (error) {
            this.logger.error(`Failed to delete file ${bucketName}/${filePath}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to delete file.');
        }
        this.logger.log(`File ${bucketName}/${filePath} deleted successfully`);
    }
    async getFileMetadata(bucketName, filePath) {
        this.logger.log(`Getting metadata for file ${bucketName}/${filePath}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: files, error: listError } = await supabase.storage
                .from(bucketName)
                .list(filePath.split('/').slice(0, -1).join('/'), {
                limit: 100,
                offset: 0,
                sortBy: { column: 'name', order: 'asc' },
            });
            if (listError) {
                this.logger.error(`Failed to list files in ${bucketName}/${filePath.split('/').slice(0, -1).join('/')}: ${listError.message}`, listError.stack);
                throw new common_1.InternalServerErrorException('Failed to get file metadata.');
            }
            const fileName = filePath.split('/').pop();
            const file = files?.find(f => f.name === fileName);
            if (!file) {
                this.logger.warn(`File ${bucketName}/${filePath} not found`);
                return null;
            }
            const publicUrl = this.getPublicUrl(bucketName, filePath);
            const metadata = {
                name: file.name,
                size: file.metadata?.size || 0,
                mimetype: file.metadata?.mimetype || 'application/octet-stream',
                created_at: file.created_at,
                updated_at: file.updated_at,
                last_accessed_at: file.last_accessed_at,
                publicUrl,
            };
            return metadata;
        }
        catch (error) {
            this.logger.error(`Unexpected error getting metadata for ${bucketName}/${filePath}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw new common_1.InternalServerErrorException('Failed to get file metadata.');
        }
    }
    async listFiles(bucketName, folderPath = '') {
        this.logger.log(`Listing files in ${bucketName}/${folderPath}`);
        const supabase = this.supabaseService.getClient();
        try {
            const { data: files, error } = await supabase.storage
                .from(bucketName)
                .list(folderPath, {
                limit: 100,
                offset: 0,
                sortBy: { column: 'name', order: 'asc' },
            });
            if (error) {
                this.logger.error(`Failed to list files in ${bucketName}/${folderPath}: ${error.message}`, error.stack);
                throw new common_1.InternalServerErrorException('Failed to list files.');
            }
            return files.map(file => {
                const filePath = folderPath ? `${folderPath}/${file.name}` : file.name;
                const publicUrl = this.getPublicUrl(bucketName, filePath);
                const isFolder = file.id.endsWith('/');
                const fileMetadata = {
                    name: file.name,
                    size: file.metadata?.size || 0,
                    mimetype: file.metadata?.mimetype ||
                        (isFolder ? 'folder' : 'application/octet-stream'),
                    created_at: file.created_at,
                    updated_at: file.updated_at,
                    last_accessed_at: file.last_accessed_at,
                    publicUrl: isFolder ? '' : publicUrl,
                    isFolder,
                };
                return fileMetadata;
            });
        }
        catch (error) {
            this.logger.error(`Unexpected error listing files in ${bucketName}/${folderPath}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw new common_1.InternalServerErrorException('Failed to list files.');
        }
    }
};
exports.StorageService = StorageService;
exports.StorageService = StorageService = StorageService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], StorageService);
//# sourceMappingURL=storage.service.js.map