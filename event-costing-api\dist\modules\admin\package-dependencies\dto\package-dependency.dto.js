"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageDependencyDto = exports.DependentPackageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DependentPackageDto {
    name;
}
exports.DependentPackageDto = DependentPackageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Package name' }),
    __metadata("design:type", String)
], DependentPackageDto.prototype, "name", void 0);
class PackageDependencyDto {
    id;
    package_id;
    dependent_package_id;
    dependency_type;
    description;
    created_at;
    updated_at;
    dependent_package;
}
exports.PackageDependencyDto = PackageDependencyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Dependency record UUID', format: 'uuid' }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The package that has the dependency',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The package that is depended upon (or conflicted with)',
        format: 'uuid',
    }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "dependent_package_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Type of dependency (e.g., REQUIRES)' }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "dependency_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description', nullable: true }),
    __metadata("design:type", Object)
], PackageDependencyDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation timestamp' }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update timestamp' }),
    __metadata("design:type", String)
], PackageDependencyDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dependent package information',
        type: DependentPackageDto,
        nullable: true
    }),
    __metadata("design:type", Object)
], PackageDependencyDto.prototype, "dependent_package", void 0);
//# sourceMappingURL=package-dependency.dto.js.map