"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ServiceCategoriesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceCategoriesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const service_categories_service_1 = require("./service-categories.service");
const create_service_category_dto_1 = require("./dto/create-service-category.dto");
const update_service_category_dto_1 = require("./dto/update-service-category.dto");
const service_category_dto_1 = require("./dto/service-category.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
let ServiceCategoriesController = ServiceCategoriesController_1 = class ServiceCategoriesController {
    serviceCategoriesService;
    logger = new common_1.Logger(ServiceCategoriesController_1.name);
    constructor(serviceCategoriesService) {
        this.serviceCategoriesService = serviceCategoriesService;
    }
    create(createDto) {
        this.logger.log(`Received request to create service category: ${JSON.stringify(createDto)}`);
        return this.serviceCategoriesService.create(createDto);
    }
    findAll() {
        this.logger.log('Received request to find all service categories');
        return this.serviceCategoriesService.findAll();
    }
    findOne(id) {
        this.logger.log(`Received request to find service category with ID: ${id}`);
        return this.serviceCategoriesService.findOne(id);
    }
    update(id, updateDto) {
        this.logger.log(`Received request to update service category ${id}: ${JSON.stringify(updateDto)}`);
        return this.serviceCategoriesService.update(id, updateDto);
    }
    remove(id) {
        this.logger.log(`Received request to delete service category with ID: ${id}`);
        return this.serviceCategoriesService.remove(id);
    }
};
exports.ServiceCategoriesController = ServiceCategoriesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new service category' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Service category created successfully.',
        type: service_category_dto_1.ServiceCategoryDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict (e.g., unique constraint)',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_service_category_dto_1.CreateServiceCategoryDto]),
    __metadata("design:returntype", Promise)
], ServiceCategoriesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all service categories' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of service categories.',
        type: [service_category_dto_1.ServiceCategoryDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ServiceCategoriesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific service category by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'UUID of the service category' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service category details.',
        type: service_category_dto_1.ServiceCategoryDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Service category not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ServiceCategoriesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific service category' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'UUID of the service category to update',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service category updated successfully.',
        type: service_category_dto_1.ServiceCategoryDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Service category not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_service_category_dto_1.UpdateServiceCategoryDto]),
    __metadata("design:returntype", Promise)
], ServiceCategoriesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Soft-delete a specific service category' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'UUID of the service category to delete',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Service category soft-deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Service category not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ServiceCategoriesController.prototype, "remove", null);
exports.ServiceCategoriesController = ServiceCategoriesController = ServiceCategoriesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin: Service Categories'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/service-categories'),
    __metadata("design:paramtypes", [service_categories_service_1.ServiceCategoriesService])
], ServiceCategoriesController);
//# sourceMappingURL=service-categories.controller.js.map