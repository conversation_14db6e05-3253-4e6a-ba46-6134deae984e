"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VenueReferenceDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class VenueReferenceDto {
    id;
    name;
    address;
    city_id;
    city_name;
    classification;
    capacity;
    image_url;
    features;
}
exports.VenueReferenceDto = VenueReferenceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        description: 'Venue ID',
    }),
    __metadata("design:type", String)
], VenueReferenceDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        description: 'Venue name',
    }),
    __metadata("design:type", String)
], VenueReferenceDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue address',
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        format: 'uuid',
        nullable: true,
        description: 'City ID',
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "city_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'City name',
    }),
    __metadata("design:type", String)
], VenueReferenceDto.prototype, "city_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue classification (outdoor, hotel, indoor, premium, luxury)',
        enum: ['outdoor', 'hotel', 'indoor', 'premium', 'luxury'],
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "classification", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: Number,
        nullable: true,
        description: 'Maximum attendee capacity',
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "capacity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: String,
        nullable: true,
        description: 'Venue image URL',
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "image_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'array',
        items: { type: 'string' },
        nullable: true,
        description: 'Venue features and amenities',
    }),
    __metadata("design:type", Object)
], VenueReferenceDto.prototype, "features", void 0);
//# sourceMappingURL=venue-reference.dto.js.map