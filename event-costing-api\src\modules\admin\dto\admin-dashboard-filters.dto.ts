import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsEnum, Min, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Time period enum for dashboard filters
 */
export enum DashboardTimePeriod {
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_90_DAYS = 'last_90_days',
  LAST_YEAR = 'last_year',
  ALL_TIME = 'all_time',
}

/**
 * Dashboard view mode enum
 */
export enum DashboardViewMode {
  OVERVIEW = 'overview',
  DETAILED = 'detailed',
  ANALYTICS = 'analytics',
}

/**
 * DTO for admin dashboard filtering and customization
 */
export class AdminDashboardFiltersDto {
  @ApiPropertyOptional({
    description: 'Time period for activity and statistics',
    enum: DashboardTimePeriod,
    example: DashboardTimePeriod.LAST_30_DAYS,
    default: DashboardTimePeriod.LAST_30_DAYS,
  })
  @IsOptional()
  @IsEnum(DashboardTimePeriod)
  timePeriod?: DashboardTimePeriod;

  @ApiPropertyOptional({
    description: 'Number of days for recent activity',
    example: 7,
    default: 7,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(365)
  activityDays?: number;

  @ApiPropertyOptional({
    description: 'Dashboard view mode',
    enum: DashboardViewMode,
    example: DashboardViewMode.OVERVIEW,
    default: DashboardViewMode.OVERVIEW,
  })
  @IsOptional()
  @IsEnum(DashboardViewMode)
  viewMode?: DashboardViewMode;

  @ApiPropertyOptional({
    description: 'Include detailed statistics',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeStatistics?: boolean;

  @ApiPropertyOptional({
    description: 'Include recent activity',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeActivity?: boolean;

  @ApiPropertyOptional({
    description: 'Include system health metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeHealth?: boolean;

  @ApiPropertyOptional({
    description: 'Include performance metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePerformance?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by specific category ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by specific city ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by specific division ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  divisionId?: string;

  @ApiPropertyOptional({
    description: 'Filter by specific user ID',
    type: String,
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Custom date range start',
    type: String,
    format: 'date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsString()
  dateStart?: string;

  @ApiPropertyOptional({
    description: 'Custom date range end',
    type: String,
    format: 'date',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsString()
  dateEnd?: string;

  @ApiPropertyOptional({
    description: 'Refresh interval in seconds for real-time updates',
    example: 30,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(3600)
  refreshInterval?: number;

  @ApiPropertyOptional({
    description: 'Include cache performance metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCacheMetrics?: boolean;

  @ApiPropertyOptional({
    description: 'Include database performance metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDatabaseMetrics?: boolean;

  @ApiPropertyOptional({
    description: 'Include API usage metrics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeApiMetrics?: boolean;

  @ApiPropertyOptional({
    description: 'Limit for recent activity items',
    example: 20,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  activityLimit?: number;

  @ApiPropertyOptional({
    description: 'Include user activity breakdown',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeUserActivity?: boolean;

  @ApiPropertyOptional({
    description: 'Include system alerts and warnings',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAlerts?: boolean;

  @ApiPropertyOptional({
    description: 'Include growth trends and analytics',
    example: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeTrends?: boolean;
}
