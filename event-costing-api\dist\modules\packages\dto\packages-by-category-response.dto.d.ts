export declare class CategoryPackageOptionDto {
    id: string;
    option_name: string;
    description: string;
    price_adjustment: number;
    cost_adjustment: number;
    is_default_for_package: boolean;
    is_required: boolean;
}
export declare class CategoryPackageDto {
    id: string;
    name: string;
    description: string;
    quantity_basis: string;
    price: number;
    unit_base_cost: number;
    options?: CategoryPackageOptionDto[];
}
export declare class CategoryWithPackagesDto {
    id: string;
    name: string;
    display_order: number;
    packages: CategoryPackageDto[];
}
export declare class PackagesByCategoryResponseDto {
    categories: CategoryWithPackagesDto[];
}
