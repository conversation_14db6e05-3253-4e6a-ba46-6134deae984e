"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTemplateDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdateTemplateDto {
    name;
    description;
    event_type_id;
    template_start_date;
    template_end_date;
    is_public;
}
exports.UpdateTemplateDto = UpdateTemplateDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'The name of the template' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'A description for the template' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], UpdateTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event type ID (UUID reference to event_types table)',
        format: 'uuid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", Object)
], UpdateTemplateDto.prototype, "event_type_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional template start datetime (ISO 8601 format)',
        type: String,
        format: 'date-time',
        example: '2025-05-20T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    (0, class_validator_1.ValidateIf)((o) => o.template_start_date !== null),
    __metadata("design:type", Object)
], UpdateTemplateDto.prototype, "template_start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional template end datetime (ISO 8601 format)',
        type: String,
        format: 'date-time',
        example: '2025-05-20T23:59:59.999Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsISO8601)(),
    (0, class_validator_1.ValidateIf)((o) => o.template_end_date !== null),
    __metadata("design:type", Object)
], UpdateTemplateDto.prototype, "template_end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Set whether the template is publicly accessible',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateTemplateDto.prototype, "is_public", void 0);
//# sourceMappingURL=update-template.dto.js.map