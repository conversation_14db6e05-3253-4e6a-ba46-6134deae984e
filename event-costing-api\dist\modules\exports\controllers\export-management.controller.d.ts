import { ExportManagementService } from '../services/export-management.service';
import { ExportManagementDataDto } from '../dto/export-management-data.dto';
import { ExportManagementFiltersDto } from '../dto/export-management-filters.dto';
import { User } from '@supabase/supabase-js';
import { ExportFormat } from '../enums/export-format.enum';
export declare class ExportManagementController {
    private readonly exportManagementService;
    private readonly logger;
    constructor(exportManagementService: ExportManagementService);
    getManagementData(filters: ExportManagementFiltersDto, user: User): Promise<ExportManagementDataDto>;
    initiateBatchExports(requests: Array<{
        calculationId: string;
        format: ExportFormat;
        recipient?: string;
    }>, user: User): Promise<any>;
    getManagementSummary(user: User): Promise<any>;
}
