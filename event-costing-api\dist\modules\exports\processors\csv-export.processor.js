"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CsvExportProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CsvExportProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const exports_service_1 = require("../exports.service");
const export_storage_service_1 = require("../services/export-storage.service");
const export_generation_service_1 = require("../services/export-generation.service");
const calculations_service_1 = require("../../calculations/calculations.service");
const export_status_enum_1 = require("../enums/export-status.enum");
let CsvExportProcessor = CsvExportProcessor_1 = class CsvExportProcessor extends bullmq_1.WorkerHost {
    exportsService;
    storageService;
    generationService;
    calculationsService;
    logger = new common_1.Logger(CsvExportProcessor_1.name);
    constructor(exportsService, storageService, generationService, calculationsService) {
        super();
        this.exportsService = exportsService;
        this.storageService = storageService;
        this.generationService = generationService;
        this.calculationsService = calculationsService;
    }
    async process(job) {
        const { exportHistoryId, calculationId, userId } = job.data;
        this.logger.log(`[CSV] Processing job ${job.id} for export history ${exportHistoryId}`);
        this.logger.log(`[CSV] Calculation: ${calculationId}, User: ${userId}`);
        let generatedFileName = '';
        let storagePath = undefined;
        try {
            this.logger.log(`[CSV] Updating status to PROCESSING for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.PROCESSING);
            this.logger.log(`[CSV] Fetching calculation data for ${calculationId}`);
            const calculationData = await this.calculationsService.findCalculationForExport(calculationId, userId);
            if (!calculationData) {
                throw new common_1.InternalServerErrorException(`[CSV] Calculation data not found for ID: ${calculationId}`);
            }
            this.logger.log(`[CSV] Calculation data fetched successfully for ${calculationId}`);
            this.logger.log(`[CSV] Transforming calculation data for ${calculationId}`);
            const transformedData = this.generationService.transformCalculationData(calculationData);
            this.logger.log(`[CSV] Data transformation completed for ${calculationId}`);
            this.logger.log(`[CSV] Generating CSV buffer for ${calculationId}`);
            const sanitizedCalcName = calculationData.name
                .replace(/[^a-z0-9]/gi, '_')
                .toLowerCase();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            generatedFileName = `${sanitizedCalcName}_${timestamp}.csv`;
            const fileBuffer = await this.generationService.generateCsvBuffer(transformedData);
            if (!fileBuffer) {
                throw new common_1.InternalServerErrorException('[CSV] File buffer not generated.');
            }
            this.logger.log(`[CSV] CSV buffer generated successfully, size: ${fileBuffer.length} bytes`);
            this.logger.log(`[CSV] Uploading file to storage: ${generatedFileName}`);
            storagePath = await this.storageService.uploadExportFile(userId, generatedFileName, fileBuffer, 'text/csv');
            this.logger.log(`[CSV] File uploaded successfully to: ${storagePath}`);
            this.logger.log(`[CSV] Updating status to COMPLETED for export ${exportHistoryId}`);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.COMPLETED, {
                storagePath: storagePath,
                fileName: generatedFileName,
                fileSize: fileBuffer.length,
                mimeType: 'text/csv',
            });
            this.logger.log(`[CSV] Job ${job.id} completed successfully for export history ${exportHistoryId}`);
        }
        catch (error) {
            this.logger.error(`[CSV] Job ${job.id} failed for export history ${exportHistoryId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            await this.exportsService.updateExportHistoryStatus(exportHistoryId, export_status_enum_1.ExportStatus.FAILED, {
                error: error instanceof Error ? error.message : String(error),
                fileName: generatedFileName || undefined,
                storagePath: storagePath || undefined,
            });
            throw error;
        }
    }
};
exports.CsvExportProcessor = CsvExportProcessor;
exports.CsvExportProcessor = CsvExportProcessor = CsvExportProcessor_1 = __decorate([
    (0, bullmq_1.Processor)('csv-exports'),
    __metadata("design:paramtypes", [exports_service_1.ExportsService,
        export_storage_service_1.ExportStorageService,
        export_generation_service_1.ExportGenerationService,
        calculations_service_1.CalculationsService])
], CsvExportProcessor);
//# sourceMappingURL=csv-export.processor.js.map