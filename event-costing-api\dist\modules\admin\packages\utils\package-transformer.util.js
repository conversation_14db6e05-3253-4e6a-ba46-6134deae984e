"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageTransformerUtil = void 0;
class PackageTransformerUtil {
    static transformPackagesWithRelations(packages, categoriesMap, divisionsMap, pricesMap, citiesMap) {
        return packages.map(pkg => {
            const priceInfo = pricesMap.get(pkg.id) || {
                price: null,
                unitBaseCost: null,
                currencySymbol: 'Rp',
                hasPricing: false,
            };
            const cities = citiesMap.get(pkg.id) || [];
            return {
                id: pkg.id,
                name: pkg.name,
                description: pkg.description,
                category_id: pkg.category_id,
                categoryName: categoriesMap.get(pkg.category_id) || undefined,
                division_id: pkg.division_id,
                divisionName: divisionsMap.get(pkg.division_id) || undefined,
                variation_group_code: pkg.variation_group_code,
                seq_number: pkg.seq_number || 0,
                quantity_basis: pkg.quantity_basis,
                created_at: pkg.created_at,
                updated_at: pkg.updated_at,
                created_by: pkg.created_by || '',
                updated_by: pkg.updated_by,
                is_deleted: pkg.is_deleted,
                cityNames: cities,
                price: priceInfo.price,
                unitBaseCost: priceInfo.unitBaseCost,
                currencySymbol: priceInfo.currencySymbol,
                hasPricing: priceInfo.hasPricing,
            };
        });
    }
    static transformSinglePackageWithRelations(pkg, categoryName, divisionName, priceInfo, cities) {
        const defaultPriceInfo = {
            price: null,
            unitBaseCost: null,
            currencySymbol: 'Rp',
            hasPricing: false,
        };
        const finalPriceInfo = priceInfo || defaultPriceInfo;
        return {
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            category_id: pkg.category_id,
            categoryName: categoryName || undefined,
            division_id: pkg.division_id,
            divisionName: divisionName || undefined,
            variation_group_code: pkg.variation_group_code,
            seq_number: pkg.seq_number || 0,
            quantity_basis: pkg.quantity_basis,
            created_at: pkg.created_at,
            updated_at: pkg.updated_at,
            created_by: pkg.created_by || '',
            updated_by: pkg.updated_by,
            is_deleted: pkg.is_deleted,
            cityNames: cities || [],
            price: finalPriceInfo.price,
            unitBaseCost: finalPriceInfo.unitBaseCost,
            currencySymbol: finalPriceInfo.currencySymbol,
            hasPricing: finalPriceInfo.hasPricing,
        };
    }
    static extractUniqueIds(packages, field) {
        return [
            ...new Set(packages
                .filter(pkg => pkg[field])
                .map(pkg => pkg[field])),
        ];
    }
    static groupPackagesByField(packages, field) {
        const grouped = new Map();
        packages.forEach(pkg => {
            const fieldValue = pkg[field];
            if (fieldValue) {
                if (!grouped.has(fieldValue)) {
                    grouped.set(fieldValue, []);
                }
                grouped.get(fieldValue).push(pkg);
            }
        });
        return grouped;
    }
    static filterPackages(packages, criteria) {
        return packages.filter(pkg => {
            if (criteria.isDeleted !== undefined && pkg.is_deleted !== criteria.isDeleted) {
                return false;
            }
            if (criteria.categoryId && pkg.category_id !== criteria.categoryId) {
                return false;
            }
            if (criteria.divisionId && pkg.division_id !== criteria.divisionId) {
                return false;
            }
            if (criteria.hasName && !pkg.name?.toLowerCase().includes(criteria.hasName.toLowerCase())) {
                return false;
            }
            return true;
        });
    }
    static sortPackages(packages, field, order = 'asc') {
        return [...packages].sort((a, b) => {
            const aValue = a[field];
            const bValue = b[field];
            if (aValue === null || aValue === undefined)
                return 1;
            if (bValue === null || bValue === undefined)
                return -1;
            let comparison = 0;
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                comparison = aValue.localeCompare(bValue);
            }
            else if (typeof aValue === 'number' && typeof bValue === 'number') {
                comparison = aValue - bValue;
            }
            else {
                comparison = String(aValue).localeCompare(String(bValue));
            }
            return order === 'desc' ? -comparison : comparison;
        });
    }
    static paginatePackages(packages, offset = 0, limit = 20) {
        const total = packages.length;
        const data = packages.slice(offset, offset + limit);
        return {
            data,
            count: total,
            offset,
            limit,
            hasMore: offset + limit < total,
        };
    }
    static validatePackageStructure(pkg) {
        const errors = [];
        if (!pkg.id) {
            errors.push('Package ID is required');
        }
        if (!pkg.name || typeof pkg.name !== 'string') {
            errors.push('Package name is required and must be a string');
        }
        if (pkg.category_id && typeof pkg.category_id !== 'string') {
            errors.push('Category ID must be a string');
        }
        if (pkg.division_id && typeof pkg.division_id !== 'string') {
            errors.push('Division ID must be a string');
        }
        if (pkg.quantity_basis && typeof pkg.quantity_basis !== 'string') {
            errors.push('Quantity basis must be a string');
        }
        if (pkg.is_deleted !== undefined && typeof pkg.is_deleted !== 'boolean') {
            errors.push('is_deleted must be a boolean');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    static cleanPackageData(pkg) {
        const cleaned = {};
        Object.keys(pkg).forEach(key => {
            const value = pkg[key];
            if (value !== null && value !== undefined) {
                cleaned[key] = value;
            }
        });
        return cleaned;
    }
}
exports.PackageTransformerUtil = PackageTransformerUtil;
//# sourceMappingURL=package-transformer.util.js.map