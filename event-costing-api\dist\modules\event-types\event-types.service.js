"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventTypesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let EventTypesService = class EventTypesService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .select('*')
            .eq('is_active', true)
            .order('display_order', { ascending: true });
        if (error) {
            throw new Error(`Failed to fetch event types: ${error.message}`);
        }
        return data || [];
    }
    async findAllAdmin() {
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .select('*')
            .order('display_order', { ascending: true });
        if (error) {
            throw new Error(`Failed to fetch event types: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id) {
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .select('*')
            .eq('id', id)
            .single();
        if (error) {
            throw new common_1.NotFoundException(`Event type with ID ${id} not found`);
        }
        return data;
    }
    async create(createEventTypeDto) {
        const { data: existing } = await this.supabaseService
            .getClient()
            .from('event_types')
            .select('id, name, code')
            .or(`name.eq.${createEventTypeDto.name},code.eq.${createEventTypeDto.code}`);
        if (existing && existing.length > 0) {
            const existingItem = existing[0];
            if (existingItem.name === createEventTypeDto.name) {
                throw new common_1.ConflictException(`Event type with name "${createEventTypeDto.name}" already exists`);
            }
            if (existingItem.code === createEventTypeDto.code) {
                throw new common_1.ConflictException(`Event type with code "${createEventTypeDto.code}" already exists`);
            }
        }
        const eventTypeData = {
            ...createEventTypeDto,
            color: createEventTypeDto.color || 'blue',
            display_order: createEventTypeDto.display_order || 9999,
        };
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .insert(eventTypeData)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create event type: ${error.message}`);
        }
        return data;
    }
    async update(id, updateEventTypeDto) {
        await this.findOne(id);
        if (updateEventTypeDto.name || updateEventTypeDto.code) {
            const conditions = [];
            if (updateEventTypeDto.name) {
                conditions.push(`name.eq.${updateEventTypeDto.name}`);
            }
            if (updateEventTypeDto.code) {
                conditions.push(`code.eq.${updateEventTypeDto.code}`);
            }
            const { data: existing } = await this.supabaseService
                .getClient()
                .from('event_types')
                .select('id, name, code')
                .neq('id', id)
                .or(conditions.join(','));
            if (existing && existing.length > 0) {
                const existingItem = existing[0];
                if (updateEventTypeDto.name &&
                    existingItem.name === updateEventTypeDto.name) {
                    throw new common_1.ConflictException(`Event type with name "${updateEventTypeDto.name}" already exists`);
                }
                if (updateEventTypeDto.code &&
                    existingItem.code === updateEventTypeDto.code) {
                    throw new common_1.ConflictException(`Event type with code "${updateEventTypeDto.code}" already exists`);
                }
            }
        }
        const updateData = {
            ...updateEventTypeDto,
            updated_at: new Date().toISOString(),
        };
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update event type: ${error.message}`);
        }
        return data;
    }
    async remove(id) {
        await this.findOne(id);
        const { error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .update({
            is_active: false,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id);
        if (error) {
            throw new Error(`Failed to delete event type: ${error.message}`);
        }
    }
    async getEventTypeByCode(code) {
        const { data, error } = await this.supabaseService
            .getClient()
            .from('event_types')
            .select('*')
            .eq('code', code)
            .eq('is_active', true)
            .single();
        if (error) {
            return null;
        }
        return data;
    }
};
exports.EventTypesService = EventTypesService;
exports.EventTypesService = EventTypesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], EventTypesService);
//# sourceMappingURL=event-types.service.js.map