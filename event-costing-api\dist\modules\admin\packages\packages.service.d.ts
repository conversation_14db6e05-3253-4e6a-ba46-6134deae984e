import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { PackageDto } from './dto/package.dto';
import { PackageListQueryDto } from './dto/package-list-query.dto';
import { BatchUpdatePackagesDto } from './dto/batch-update-packages.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
import { PackageCrudService } from './services/package-crud.service';
import { PackageQueryService } from './services/package-query.service';
import { PackageRelationsService } from './services/package-relations.service';
import { PackageBatchService } from './services/package-batch.service';
export declare class PackagesService {
    private readonly packageCrudService;
    private readonly packageQueryService;
    private readonly packageRelationsService;
    private readonly packageBatchService;
    private readonly logger;
    constructor(packageCrudService: PackageCrudService, packageQueryService: PackageQueryService, packageRelationsService: PackageRelationsService, packageBatchService: PackageBatchService);
    create(createPackageDto: CreatePackageDto): Promise<PackageDto>;
    findAll(queryDto: PackageListQueryDto): Promise<PaginatedResponseDto<PackageDto>>;
    findOne(id: string): Promise<PackageDto>;
    update(id: string, updatePackageDto: UpdatePackageDto): Promise<PackageDto>;
    remove(id: string): Promise<void>;
    updateStatus(id: string, isActive: boolean): Promise<PackageDto>;
    batchUpdate(batchUpdateDto: BatchUpdatePackagesDto): Promise<{
        updatedCount: number;
        errors: any[];
    }>;
}
