"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportsModule = void 0;
const common_1 = require("@nestjs/common");
const exports_service_1 = require("./exports.service");
const exports_controller_1 = require("./exports.controller");
const supabase_module_1 = require("../../core/supabase/supabase.module");
const calculations_module_1 = require("../calculations/calculations.module");
const bullmq_1 = require("@nestjs/bullmq");
const exports_processor_1 = require("./exports.processor");
const csv_export_processor_1 = require("./processors/csv-export.processor");
const pdf_export_processor_1 = require("./processors/pdf-export.processor");
const xlsx_export_processor_1 = require("./processors/xlsx-export.processor");
const export_generation_service_1 = require("./services/export-generation.service");
const export_storage_service_1 = require("./services/export-storage.service");
const export_management_service_1 = require("./services/export-management.service");
const export_management_controller_1 = require("./controllers/export-management.controller");
const auth_module_1 = require("../auth/auth.module");
let ExportsModule = class ExportsModule {
};
exports.ExportsModule = ExportsModule;
exports.ExportsModule = ExportsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            auth_module_1.AuthModule,
            supabase_module_1.SupabaseModule,
            (0, common_1.forwardRef)(() => calculations_module_1.CalculationsModule),
            bullmq_1.BullModule.registerQueue({ name: 'csv-exports' }),
            bullmq_1.BullModule.registerQueue({ name: 'pdf-exports' }),
            bullmq_1.BullModule.registerQueue({ name: 'xlsx-exports' }),
        ],
        controllers: [
            exports_controller_1.ExportsController,
            export_management_controller_1.ExportManagementController,
        ],
        providers: [
            exports_service_1.ExportsService,
            exports_processor_1.ExportsProcessor,
            csv_export_processor_1.CsvExportProcessor,
            pdf_export_processor_1.PdfExportProcessor,
            xlsx_export_processor_1.XlsxExportProcessor,
            export_generation_service_1.ExportGenerationService,
            export_storage_service_1.ExportStorageService,
            export_management_service_1.ExportManagementService,
        ],
        exports: [
            exports_service_1.ExportsService,
            export_management_service_1.ExportManagementService,
        ],
    })
], ExportsModule);
//# sourceMappingURL=exports.module.js.map