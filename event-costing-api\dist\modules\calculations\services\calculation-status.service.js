"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationStatusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationStatusService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const calculation_status_enum_1 = require("../enums/calculation-status.enum");
let CalculationStatusService = CalculationStatusService_1 = class CalculationStatusService {
    supabaseService;
    logger = new common_1.Logger(CalculationStatusService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async updateStatus(id, updateStatusDto, userId) {
        const newStatus = updateStatusDto.status;
        const allowedStatuses = [
            calculation_status_enum_1.CalculationStatus.DRAFT,
            calculation_status_enum_1.CalculationStatus.COMPLETED,
            calculation_status_enum_1.CalculationStatus.CANCELED,
        ];
        if (!allowedStatuses.includes(newStatus)) {
            this.logger.warn(`Invalid status ${newStatus} attempted for calculation ${id}`);
            throw new common_1.BadRequestException(`Invalid status: ${newStatus}`);
        }
        this.logger.log(`User ${userId} attempting to update status of calculation ${id} to ${newStatus}`);
        const supabase = this.supabaseService.getClient();
        const { data: currentCalc, error: fetchError } = await supabase
            .from('calculation_history')
            .select('status, created_by')
            .eq('id', id)
            .eq('is_deleted', false)
            .single();
        if (fetchError) {
            this.logger.error(`Error fetching calculation ${id} for status update: ${fetchError.message}`);
            if (fetchError.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Calculation with ID ${id} not found.`);
            }
            throw new common_1.InternalServerErrorException('Could not retrieve calculation.');
        }
        if (!currentCalc) {
            throw new common_1.NotFoundException(`Calculation with ID ${id} not found.`);
        }
        if (currentCalc.created_by !== userId) {
            this.logger.warn(`User ${userId} attempted to update status on unowned calc ${id}`);
            throw new common_1.ForbiddenException('Access denied to this calculation.');
        }
        if (!this.isValidStatusTransition(currentCalc.status, newStatus)) {
            throw new common_1.BadRequestException(`Invalid status transition from ${currentCalc.status} to ${newStatus}`);
        }
        const { error: updateError } = await supabase
            .from('calculation_history')
            .update({ status: newStatus })
            .eq('id', id);
        if (updateError) {
            this.logger.error(`Failed to update status for calculation ${id}: ${updateError.message}`, updateError.stack);
            throw new common_1.InternalServerErrorException('Could not update calculation status.');
        }
        this.logger.log(`Successfully updated status of calculation ${id} to ${newStatus}`);
    }
    async getCurrentStatus(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('status')
            .eq('id', calculationId)
            .eq('is_deleted', false)
            .single();
        if (error) {
            this.logger.error(`Error fetching status for calculation ${calculationId}: ${error.message}`);
            throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found.`);
        }
        return data.status;
    }
    isValidStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            [calculation_status_enum_1.CalculationStatus.DRAFT]: [
                calculation_status_enum_1.CalculationStatus.COMPLETED,
                calculation_status_enum_1.CalculationStatus.CANCELED,
                calculation_status_enum_1.CalculationStatus.DRAFT,
            ],
            [calculation_status_enum_1.CalculationStatus.COMPLETED]: [
                calculation_status_enum_1.CalculationStatus.DRAFT,
                calculation_status_enum_1.CalculationStatus.CANCELED,
            ],
            [calculation_status_enum_1.CalculationStatus.CANCELED]: [
                calculation_status_enum_1.CalculationStatus.DRAFT,
            ],
        };
        const allowedTransitions = validTransitions[currentStatus] || [];
        return allowedTransitions.includes(newStatus);
    }
    async getPossibleNextStatuses(calculationId) {
        const currentStatus = await this.getCurrentStatus(calculationId);
        const validTransitions = {
            [calculation_status_enum_1.CalculationStatus.DRAFT]: [
                calculation_status_enum_1.CalculationStatus.COMPLETED,
                calculation_status_enum_1.CalculationStatus.CANCELED,
            ],
            [calculation_status_enum_1.CalculationStatus.COMPLETED]: [
                calculation_status_enum_1.CalculationStatus.DRAFT,
                calculation_status_enum_1.CalculationStatus.CANCELED,
            ],
            [calculation_status_enum_1.CalculationStatus.CANCELED]: [calculation_status_enum_1.CalculationStatus.DRAFT],
        };
        return validTransitions[currentStatus] || [];
    }
    async bulkUpdateStatus(calculationIds, newStatus, userId) {
        const results = {
            success: [],
            failed: [],
        };
        for (const id of calculationIds) {
            try {
                await this.updateStatus(id, { status: newStatus }, userId);
                results.success.push(id);
            }
            catch (error) {
                this.logger.error(`Failed to update status for calculation ${id}: ${error.message}`);
                results.failed.push(id);
            }
        }
        return results;
    }
    async getCalculationsByStatus(userId, status, limit = 20, offset = 0) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('id, name, status, created_at, updated_at, total')
            .eq('created_by', userId)
            .eq('status', status)
            .eq('is_deleted', false)
            .order('updated_at', { ascending: false })
            .range(offset, offset + limit - 1);
        if (error) {
            this.logger.error(`Error fetching calculations by status ${status} for user ${userId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to retrieve calculations by status.');
        }
        return data || [];
    }
    async getStatusStatistics(userId) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('calculation_history')
            .select('status')
            .eq('created_by', userId)
            .eq('is_deleted', false);
        if (error) {
            this.logger.error(`Error fetching status statistics for user ${userId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to retrieve status statistics.');
        }
        const statistics = {};
        data.forEach(calc => {
            statistics[calc.status] = (statistics[calc.status] || 0) + 1;
        });
        return statistics;
    }
    canModifyCalculation(status) {
        return status === calculation_status_enum_1.CalculationStatus.DRAFT;
    }
    canDeleteCalculation(status) {
        return [calculation_status_enum_1.CalculationStatus.DRAFT, calculation_status_enum_1.CalculationStatus.CANCELED].includes(status);
    }
    async autoTransitionStatus(calculationId) {
        const currentStatus = await this.getCurrentStatus(calculationId);
        if (currentStatus === calculation_status_enum_1.CalculationStatus.DRAFT) {
        }
        return null;
    }
};
exports.CalculationStatusService = CalculationStatusService;
exports.CalculationStatusService = CalculationStatusService = CalculationStatusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationStatusService);
//# sourceMappingURL=calculation-status.service.js.map