import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtValidationService } from '../services/jwt-validation.service';
export interface JwtPayload {
    sub: string;
    email: string;
    iat: number;
    exp: number;
    aud: string;
    role?: string;
    [key: string]: any;
}
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private readonly configService;
    private readonly jwtValidationService;
    private readonly logger;
    constructor(configService: ConfigService, jwtValidationService: JwtValidationService);
    validate(payload: JwtPayload): Promise<import("../services/jwt-validation.service").UserWithProfile>;
}
export {};
