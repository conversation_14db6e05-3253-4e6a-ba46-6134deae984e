declare enum PackageQuantityBasis {
    PER_EVENT = "PER_EVENT",
    PER_DAY = "PER_DAY",
    PER_ATTENDEE = "PER_ATTENDEE",
    PER_ITEM = "PER_ITEM",
    PER_ITEM_PER_DAY = "PER_ITEM_PER_DAY",
    PER_ATTENDEE_PER_DAY = "PER_ATTENDEE_PER_DAY"
}
export declare class PackageDto {
    id: string;
    name: string;
    description?: string | null;
    category_id?: string | null;
    division_id?: string | null;
    variation_group_code?: string | null;
    seq_number: number;
    quantity_basis: PackageQuantityBasis;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by?: string | null;
    is_deleted: boolean;
    categoryName?: string;
    divisionName?: string;
    cityNames?: string[];
    venueNames?: string[];
    unitBaseCost?: string;
    price?: string;
    currencySymbol?: string;
    hasPricing?: boolean;
    hasOptions?: boolean;
    hasDependencies?: boolean;
}
export {};
