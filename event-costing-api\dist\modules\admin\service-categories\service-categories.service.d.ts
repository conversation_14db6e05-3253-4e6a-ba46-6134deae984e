import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
export declare class ServiceCategoriesService {
    private readonly supabaseService;
    private readonly logger;
    private readonly tableName;
    constructor(supabaseService: SupabaseService);
    create(createDto: CreateServiceCategoryDto): Promise<ServiceCategoryDto>;
    findAll(): Promise<ServiceCategoryDto[]>;
    findOne(id: string): Promise<ServiceCategoryDto>;
    update(id: string, updateDto: UpdateServiceCategoryDto): Promise<ServiceCategoryDto>;
    remove(id: string): Promise<void>;
    private handleSupabaseError;
}
