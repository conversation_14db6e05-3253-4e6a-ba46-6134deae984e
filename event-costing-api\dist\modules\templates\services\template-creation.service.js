"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TemplateCreationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplateCreationService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const template_venue_service_1 = require("./template-venue.service");
const template_constants_1 = require("../constants/template.constants");
let TemplateCreationService = TemplateCreationService_1 = class TemplateCreationService {
    supabaseService;
    templateVenueService;
    logger = new common_1.Logger(TemplateCreationService_1.name);
    constructor(supabaseService, templateVenueService) {
        this.supabaseService = supabaseService;
        this.templateVenueService = templateVenueService;
    }
    async createTemplateFromCalculation(createDto, user) {
        this.logger.log(`Creating template '${createDto.name}' from calculation ${createDto.calculationId} for user ${user.id}`);
        const supabase = this.supabaseService.getClient();
        const { data: calculationData, error: calculationError } = await supabase
            .from('calculation_history')
            .select('attendees, event_type_id, city_id, currency_id, event_start_date, event_end_date, taxes, discount')
            .eq('id', createDto.calculationId)
            .single();
        if (calculationError) {
            this.logger.error(`Error fetching calculation ${createDto.calculationId}: ${calculationError.message}`, calculationError.stack);
            throw new common_1.InternalServerErrorException('Failed to fetch calculation details.');
        }
        if (!calculationData) {
            throw new common_1.NotFoundException(`Calculation with ID ${createDto.calculationId} not found.`);
        }
        this.logger.log(`Fetched calculation data: attendees=${calculationData.attendees}, event_type_id=${calculationData.event_type_id}, city_id=${calculationData.city_id}, currency_id=${calculationData.currency_id}`);
        const { data: lineItemsData, error: lineItemsError } = await supabase
            .from('calculation_line_items')
            .select('id, package_id, item_quantity, item_quantity_basis')
            .eq('calculation_id', createDto.calculationId);
        if (lineItemsError) {
            throw new common_1.InternalServerErrorException('Failed to fetch line items.');
        }
        const lineItems = lineItemsData || [];
        const { data: customItemsData, error: customItemsError } = await supabase
            .from('calculation_custom_items')
            .select('item_name, description, item_quantity, unit_price, unit_cost, currency_id, category_id, city_id, item_quantity_basis, quantity_basis')
            .eq('calculation_id', createDto.calculationId);
        if (customItemsError) {
            throw new common_1.InternalServerErrorException('Failed to fetch custom items.');
        }
        const customItems = customItemsData || [];
        if (lineItems.length === 0 && customItems.length === 0) {
            throw new common_1.NotFoundException('Calculation not found or has no items (standard or custom).');
        }
        const lineItemIds = lineItems.map(item => item.id);
        const { data: lineItemOptionsData, error: optionsError } = await supabase
            .from('calculation_line_item_options')
            .select('line_item_id, option_id')
            .in('line_item_id', lineItemIds);
        if (optionsError) {
            throw new common_1.InternalServerErrorException('Failed to fetch item options.');
        }
        const lineItemOptions = lineItemOptionsData || [];
        let venueIds = [];
        if (createDto.venueIds && createDto.venueIds.length > 0) {
            venueIds = createDto.venueIds;
            this.logger.log(`Using ${venueIds.length} venue IDs provided in the request`);
        }
        else {
            const { data: venueData, error: venueError } = await supabase
                .from('calculation_venues')
                .select('venue_id')
                .eq('calculation_id', createDto.calculationId);
            if (venueError) {
                this.logger.error(`Error fetching venues for calculation ${createDto.calculationId}: ${venueError.message}`, venueError.stack);
            }
            venueIds = venueData?.map(v => v.venue_id) || [];
            this.logger.log(`Fetched ${venueIds.length} venue IDs from calculation ${createDto.calculationId}`);
        }
        const optionsMap = new Map();
        lineItemOptions.forEach(opt => {
            const existing = optionsMap.get(opt.line_item_id) || [];
            existing.push(opt.option_id);
            optionsMap.set(opt.line_item_id, existing);
        });
        const packageSelections = lineItems
            .filter(item => item.package_id)
            .map(item => ({
            package_id: item.package_id,
            option_ids: optionsMap.get(item.id) || [],
            item_quantity: item.item_quantity,
            item_quantity_basis: item.item_quantity_basis,
        }));
        const templateData = {
            name: createDto.name,
            description: createDto.description,
            package_selections: packageSelections,
            custom_items: customItems,
            created_by: user.id,
            is_public: false,
            event_type_id: createDto.eventTypeId || calculationData.event_type_id || null,
            city_id: createDto.cityId || calculationData.city_id,
            currency_id: createDto.currencyId || calculationData.currency_id,
            attendees: createDto.attendees ?? calculationData.attendees,
            template_start_date: createDto.templateStartDate || calculationData.event_start_date || null,
            template_end_date: createDto.templateEndDate || calculationData.event_end_date || null,
            taxes: calculationData.taxes || null,
            discount: calculationData.discount || null,
        };
        this.logger.log(`Template data with calculation defaults: ${JSON.stringify({
            ...templateData,
            package_selections: `[${packageSelections.length} items]`,
            custom_items: `[${customItems.length} items]`,
            attendees_source: createDto.attendees !== undefined ? 'DTO' : 'calculation',
            city_source: createDto.cityId ? 'DTO' : 'calculation',
            currency_source: createDto.currencyId ? 'DTO' : 'calculation',
        })}`);
        const { data: insertedData, error: insertError } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .insert(templateData)
            .select(template_constants_1.TemplateConstants.SUMMARY_SELECT_FIELDS)
            .single();
        if (insertError) {
            this.logger.error(`Error inserting template: ${insertError.message}`, insertError.stack);
            throw new common_1.InternalServerErrorException('Could not create template.');
        }
        if (!insertedData) {
            throw new common_1.InternalServerErrorException('Failed to retrieve created template.');
        }
        if (venueIds.length > 0) {
            await this.templateVenueService.createTemplateVenueAssociations(insertedData.id, venueIds);
        }
        this.logger.log(`Successfully created template ${insertedData.id}`);
        const resultDto = {
            id: insertedData.id,
            name: insertedData.name,
            description: insertedData.description ?? undefined,
            event_type_id: insertedData.event_type_id ?? undefined,
            city_id: insertedData.city_id ?? undefined,
            currency_id: insertedData.currency_id ?? undefined,
            attendees: insertedData.attendees ?? undefined,
            template_start_date: insertedData.template_start_date
                ? new Date(insertedData.template_start_date)
                : undefined,
            template_end_date: insertedData.template_end_date
                ? new Date(insertedData.template_end_date)
                : undefined,
            category_id: insertedData.category_id ?? undefined,
            created_at: new Date(insertedData.created_at),
            updated_at: new Date(insertedData.updated_at),
            created_by: insertedData.created_by,
            is_public: insertedData.is_public,
            is_deleted: insertedData.is_deleted || false,
            taxes: insertedData.taxes || undefined,
            discount: insertedData.discount || undefined,
        };
        return resultDto;
    }
    async createTemplate(createDto, user) {
        this.logger.log(`Creating basic template '${createDto.name}' for user ${user.id}`);
        const supabase = this.supabaseService.getClient();
        const templateData = {
            name: createDto.name,
            description: createDto.description || null,
            event_type_id: createDto.event_type_id || null,
            attendees: createDto.attendees || null,
            template_start_date: createDto.template_start_date || null,
            template_end_date: createDto.template_end_date || null,
            is_public: createDto.is_public,
            city_id: createDto.city_id || null,
            category_id: createDto.category_id || null,
            currency_id: createDto.currency_id || null,
            created_by: user.id,
            package_selections: createDto.package_selections || [],
        };
        this.logger.log(`Creating template with data: ${JSON.stringify({
            ...templateData,
            package_selections: `[${(createDto.package_selections || []).length} items]`,
        })}`);
        const { data: insertedData, error: insertError } = await supabase
            .from(template_constants_1.TemplateConstants.TABLE_NAME)
            .insert(templateData)
            .select(template_constants_1.TemplateConstants.SUMMARY_SELECT_FIELDS)
            .single();
        if (insertError) {
            this.logger.error(`Error inserting template: ${insertError.message}`, insertError.stack);
            throw new common_1.InternalServerErrorException('Could not create template.');
        }
        if (!insertedData) {
            throw new common_1.InternalServerErrorException('Failed to retrieve created template.');
        }
        if (createDto.venue_ids && createDto.venue_ids.length > 0) {
            await this.templateVenueService.createTemplateVenueAssociations(insertedData.id, createDto.venue_ids);
        }
        this.logger.log(`Successfully created template ${insertedData.id}`);
        const resultDto = {
            id: insertedData.id,
            name: insertedData.name,
            description: insertedData.description ?? undefined,
            event_type_id: insertedData.event_type_id ?? undefined,
            city_id: insertedData.city_id ?? undefined,
            currency_id: insertedData.currency_id ?? undefined,
            attendees: insertedData.attendees ?? undefined,
            template_start_date: insertedData.template_start_date
                ? new Date(insertedData.template_start_date)
                : undefined,
            template_end_date: insertedData.template_end_date
                ? new Date(insertedData.template_end_date)
                : undefined,
            category_id: insertedData.category_id ?? undefined,
            created_at: new Date(insertedData.created_at),
            updated_at: new Date(insertedData.updated_at),
            created_by: insertedData.created_by,
            is_public: insertedData.is_public,
            is_deleted: insertedData.is_deleted || false,
        };
        return resultDto;
    }
};
exports.TemplateCreationService = TemplateCreationService;
exports.TemplateCreationService = TemplateCreationService = TemplateCreationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        template_venue_service_1.TemplateVenueService])
], TemplateCreationService);
//# sourceMappingURL=template-creation.service.js.map