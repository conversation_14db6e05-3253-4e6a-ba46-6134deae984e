import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { ExportsService } from '../exports.service';
import { CalculationsService } from '../../calculations/calculations.service';
import { ExportManagementDataDto } from '../dto/export-management-data.dto';
import { ExportManagementFiltersDto } from '../dto/export-management-filters.dto';
import { CreateExportDto } from '../dto/create-export.dto';
import { ExportFormat } from '../enums/export-format.enum';
import { ExportStatus } from '../enums/export-status.enum';

/**
 * Service responsible for providing consolidated export management data
 * Implements the consolidated endpoint pattern for export operations
 */
@Injectable()
export class ExportManagementService {
  private readonly logger = new Logger(ExportManagementService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly exportsService: ExportsService,
    private readonly calculationsService: CalculationsService,
  ) {}

  /**
   * Get complete export management data in a single API call
   * Replaces the need for multiple separate API calls for export management
   *
   * @param filters - Export management filters
   * @param user - The authenticated user
   * @returns Complete export management data with metadata
   */
  async getExportManagementData(
    filters: ExportManagementFiltersDto = {},
    user: User,
  ): Promise<ExportManagementDataDto> {
    this.logger.log(`Fetching export management data for user: ${user.email}`);

    const startTime = Date.now();

    try {
      // Parallel data fetching with error handling
      const [
        exportsResult,
        calculationsResult,
        statisticsResult,
        recentActivityResult,
      ] = await Promise.allSettled([
        this.getUserExports(filters, user),
        this.getUserCalculations(user),
        this.getExportStatistics(user),
        this.getRecentExportActivity(filters.activityDays || 7, user),
      ]);

      // Handle partial failures gracefully
      const exports = this.extractResult(exportsResult, 'exports', {
        data: [],
        totalCount: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0,
      });
      const calculations = this.extractResult(
        calculationsResult,
        'calculations',
        [],
      );
      const statistics = this.extractResult(statisticsResult, 'statistics', {
        totalExports: 0,
        completedExports: 0,
        failedExports: 0,
        pendingExports: 0,
        exportsByFormat: {},
        exportsByStatus: {},
      });
      const recentActivity = this.extractResult(
        recentActivityResult,
        'recentActivity',
        [],
      );

      const loadTime = Date.now() - startTime;
      const errors = this.collectErrors([
        exportsResult,
        calculationsResult,
        statisticsResult,
        recentActivityResult,
      ]);

      const result: ExportManagementDataDto = {
        exports,
        calculations,
        statistics,
        recentActivity,
        supportedFormats: Object.values(ExportFormat),
        filters: {
          applied: filters,
          available: {
            formats: Object.values(ExportFormat),
            statuses: Object.values(ExportStatus),
            calculations: calculations.map(calc => ({
              id: calc.id,
              name: calc.name,
            })),
          },
        },
        metadata: {
          loadTime,
          cacheVersion: '1.0',
          userId: user.id,
          errors,
          timestamp: new Date().toISOString(),
          totalExports: exports.totalCount,
          appliedFilters: Object.keys(filters).length,
        },
      };

      this.logger.log(
        `Successfully fetched export management data in ${loadTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to fetch export management data`, error.stack);
      throw new InternalServerErrorException(
        'Failed to load export management data. Please try again.',
      );
    }
  }

  /**
   * Initiate multiple exports in batch
   * Allows creating multiple exports for different calculations or formats
   */
  async initiateBatchExports(
    requests: Array<{
      calculationId: string;
      format: ExportFormat;
      recipient?: string;
    }>,
    user: User,
  ): Promise<
    Array<{ calculationId: string; exportId: string; status: string }>
  > {
    this.logger.log(
      `User ${user.email} initiating batch export for ${requests.length} items`,
    );

    const results: Array<{
      calculationId: string;
      exportId: string;
      status: string;
    }> = [];

    for (const request of requests) {
      try {
        const createDto: CreateExportDto = {
          calculationId: request.calculationId,
          format: request.format,
          recipient: request.recipient,
        };

        const result = await this.exportsService.initiateExport(
          createDto,
          user,
        );

        results.push({
          calculationId: request.calculationId,
          exportId: result.exportId,
          status: 'initiated',
        });
      } catch (error) {
        this.logger.error(
          `Failed to initiate export for calculation ${request.calculationId}:`,
          error,
        );

        results.push({
          calculationId: request.calculationId,
          exportId: '',
          status: 'failed',
        });
      }
    }

    return results;
  }

  /**
   * Get user exports with filters
   */
  private async getUserExports(
    filters: ExportManagementFiltersDto,
    user: User,
  ) {
    const supabase = this.supabaseService.getClient();

    let query = supabase
      .from('export_history')
      .select(
        `
        id,
        calculation_id,
        format,
        status,
        file_url,
        download_url,
        error_message,
        created_at,
        updated_at,
        calculations (
          id,
          name,
          status
        )
      `,
      )
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.calculationId) {
      query = query.eq('calculation_id', filters.calculationId);
    }
    if (filters.format) {
      query = query.eq('format', filters.format);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.dateStart) {
      query = query.gte('created_at', filters.dateStart);
    }
    if (filters.dateEnd) {
      query = query.lte('created_at', filters.dateEnd);
    }

    // Apply pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const offset = (page - 1) * pageSize;

    query = query.range(offset, offset + pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      data: data || [],
      totalCount: count || 0,
      page,
      pageSize,
      totalPages: Math.ceil((count || 0) / pageSize),
    };
  }

  /**
   * Get user calculations for export selection
   */
  private async getUserCalculations(user: User) {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('calculations')
      .select('id, name, status, created_at')
      .eq('created_by', user.id)
      .eq('status', 'finalized') // Only finalized calculations can be exported
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) {
      throw error;
    }

    return data || [];
  }

  /**
   * Get export statistics for the user
   */
  private async getExportStatistics(user: User) {
    const supabase = this.supabaseService.getClient();

    // Get export counts by status and format
    const { data: exports, error } = await supabase
      .from('export_history')
      .select('format, status')
      .eq('user_id', user.id);

    if (error) {
      throw error;
    }

    const totalExports = exports?.length || 0;
    const completedExports =
      exports?.filter(e => e.status === 'completed').length || 0;
    const failedExports =
      exports?.filter(e => e.status === 'failed').length || 0;
    const pendingExports =
      exports?.filter(e => ['pending', 'processing'].includes(e.status))
        .length || 0;

    // Group by format
    const exportsByFormat =
      exports?.reduce(
        (acc, exp) => {
          acc[exp.format] = (acc[exp.format] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ) || {};

    // Group by status
    const exportsByStatus =
      exports?.reduce(
        (acc, exp) => {
          acc[exp.status] = (acc[exp.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ) || {};

    return {
      totalExports,
      completedExports,
      failedExports,
      pendingExports,
      exportsByFormat,
      exportsByStatus,
    };
  }

  /**
   * Get recent export activity
   */
  private async getRecentExportActivity(days: number, user: User) {
    const supabase = this.supabaseService.getClient();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const cutoffIso = cutoffDate.toISOString();

    const { data, error } = await supabase
      .from('export_history')
      .select(
        `
        id,
        calculation_id,
        format,
        status,
        created_at,
        calculations (
          name
        )
      `,
      )
      .eq('user_id', user.id)
      .gte('created_at', cutoffIso)
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) {
      throw error;
    }

    return (data || []).map(item => ({
      id: item.id,
      calculationId: item.calculation_id,
      calculationName: (item.calculations as any)?.name || 'Unknown',
      format: item.format,
      status: item.status,
      timestamp: item.created_at,
    }));
  }

  /**
   * Extract result from Promise.allSettled result
   */
  private extractResult<T>(
    result: PromiseSettledResult<T>,
    name: string,
    defaultValue?: T,
  ): T {
    if (result.status === 'fulfilled') {
      return result.value;
    }

    this.logger.warn(
      `Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`,
    );

    if (defaultValue !== undefined) {
      return defaultValue;
    }

    // For non-critical data, return empty array/object
    return [] as unknown as T;
  }

  /**
   * Collect errors from Promise.allSettled results
   */
  private collectErrors(results: PromiseSettledResult<any>[]): string[] {
    return results
      .filter(result => result.status === 'rejected')
      .map(
        result =>
          (result as PromiseRejectedResult).reason?.message || 'Unknown error',
      );
  }
}
