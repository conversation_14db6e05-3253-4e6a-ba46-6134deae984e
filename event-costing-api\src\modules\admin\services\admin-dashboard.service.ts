import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CategoriesService } from '../../categories/categories.service';
import { CitiesService } from '../../cities/cities.service';
import { DivisionsService } from '../../divisions/divisions.service';
import { AdminDashboardDataDto } from '../dto/admin-dashboard-data.dto';
import { AdminDashboardFiltersDto } from '../dto/admin-dashboard-filters.dto';

/**
 * Service responsible for providing consolidated admin dashboard data
 * Implements the consolidated endpoint pattern for admin operations
 */
@Injectable()
export class AdminDashboardService {
  private readonly logger = new Logger(AdminDashboardService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly categoriesService: CategoriesService,
    private readonly citiesService: CitiesService,
    private readonly divisionsService: DivisionsService,
  ) {}

  /**
   * Get complete admin dashboard data in a single API call
   * Replaces the need for multiple separate API calls for admin dashboard
   *
   * @param filters - Dashboard filters
   * @param user - The authenticated admin user
   * @returns Complete admin dashboard data with metadata
   */
  async getAdminDashboardData(
    filters: AdminDashboardFiltersDto = {},
    user: User,
  ): Promise<AdminDashboardDataDto> {
    this.logger.log(`Fetching admin dashboard data for user: ${user.email}`);

    const startTime = Date.now();

    try {
      // Parallel data fetching with error handling
      const [
        overviewResult,
        categoriesResult,
        citiesResult,
        divisionsResult,
        packagesResult,
        templatesResult,
        calculationsResult,
        usersResult,
        recentActivityResult,
      ] = await Promise.allSettled([
        this.getSystemOverview(),
        this.getCategories(),
        this.getCities(),
        this.getDivisions(),
        this.getPackagesOverview(),
        this.getTemplatesOverview(),
        this.getCalculationsOverview(),
        this.getUsersOverview(),
        this.getRecentActivity(filters.activityDays || 7),
      ]);

      // Handle partial failures gracefully
      const overview = this.extractResult(overviewResult, 'overview', {
        totalPackages: 0,
        totalTemplates: 0,
        totalCalculations: 0,
        totalUsers: 0,
        totalCategories: 0,
        totalCities: 0,
        totalDivisions: 0,
      });
      const categories = this.extractResult(categoriesResult, 'categories', []);
      const cities = this.extractResult(citiesResult, 'cities', []);
      const divisions = this.extractResult(divisionsResult, 'divisions', []);
      const packages = this.extractResult(packagesResult, 'packages', {
        totalCount: 0,
        activeCount: 0,
        deletedCount: 0,
      });
      const templates = this.extractResult(templatesResult, 'templates', {
        totalCount: 0,
        activeCount: 0,
        publicCount: 0,
        privateCount: 0,
      });
      const calculations = this.extractResult(
        calculationsResult,
        'calculations',
        {
          totalCount: 0,
          draftCount: 0,
          finalizedCount: 0,
        },
      );
      const users = this.extractResult(usersResult, 'users', {
        totalCount: 0,
        activeCount: 0,
      });
      const recentActivity = this.extractResult(
        recentActivityResult,
        'recentActivity',
        [],
      );

      const loadTime = Date.now() - startTime;
      const errors = this.collectErrors([
        overviewResult,
        categoriesResult,
        citiesResult,
        divisionsResult,
        packagesResult,
        templatesResult,
        calculationsResult,
        usersResult,
        recentActivityResult,
      ]);

      const result: AdminDashboardDataDto = {
        overview,
        categories,
        cities,
        divisions,
        packages,
        templates,
        calculations,
        users,
        recentActivity,
        filters: {
          applied: filters,
        },
        metadata: {
          loadTime,
          cacheVersion: '1.0',
          userId: user.id,
          errors,
          timestamp: new Date().toISOString(),
          dataPoints: {
            categoriesCount: categories.length,
            citiesCount: cities.length,
            divisionsCount: divisions.length,
            packagesCount: packages.totalCount || 0,
            templatesCount: templates.totalCount || 0,
            calculationsCount: calculations.totalCount || 0,
            usersCount: users.totalCount || 0,
            recentActivityCount: recentActivity.length,
          },
        },
      };

      this.logger.log(
        `Successfully fetched admin dashboard data in ${loadTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to fetch admin dashboard data`, error.stack);
      throw new InternalServerErrorException(
        'Failed to load admin dashboard data. Please try again.',
      );
    }
  }

  /**
   * Get system overview statistics
   */
  private async getSystemOverview() {
    const supabase = this.supabaseService.getClient();

    // Get counts for various entities
    const [
      packagesCount,
      templatesCount,
      calculationsCount,
      usersCount,
      categoriesCount,
      citiesCount,
      divisionsCount,
    ] = await Promise.allSettled([
      supabase.from('packages').select('id', { count: 'exact', head: true }),
      supabase.from('templates').select('id', { count: 'exact', head: true }),
      supabase
        .from('calculations')
        .select('id', { count: 'exact', head: true }),
      supabase.from('users').select('id', { count: 'exact', head: true }),
      supabase.from('categories').select('id', { count: 'exact', head: true }),
      supabase.from('cities').select('id', { count: 'exact', head: true }),
      supabase.from('divisions').select('id', { count: 'exact', head: true }),
    ]);

    return {
      totalPackages: this.extractCount(packagesCount),
      totalTemplates: this.extractCount(templatesCount),
      totalCalculations: this.extractCount(calculationsCount),
      totalUsers: this.extractCount(usersCount),
      totalCategories: this.extractCount(categoriesCount),
      totalCities: this.extractCount(citiesCount),
      totalDivisions: this.extractCount(divisionsCount),
    };
  }

  /**
   * Get all active categories
   */
  private async getCategories() {
    return this.categoriesService.findAll();
  }

  /**
   * Get all active cities
   */
  private async getCities() {
    return this.citiesService.findAll();
  }

  /**
   * Get all active divisions
   */
  private async getDivisions() {
    return this.divisionsService.findAll();
  }

  /**
   * Get packages overview
   */
  private async getPackagesOverview() {
    const supabase = this.supabaseService.getClient();

    // Get package statistics
    const [totalResult, activeResult, deletedResult] = await Promise.allSettled(
      [
        supabase.from('packages').select('id', { count: 'exact', head: true }),
        supabase
          .from('packages')
          .select('id', { count: 'exact', head: true })
          .eq('is_deleted', false),
        supabase
          .from('packages')
          .select('id', { count: 'exact', head: true })
          .eq('is_deleted', true),
      ],
    );

    return {
      totalCount: this.extractCount(totalResult),
      activeCount: this.extractCount(activeResult),
      deletedCount: this.extractCount(deletedResult),
    };
  }

  /**
   * Get templates overview
   */
  private async getTemplatesOverview() {
    const supabase = this.supabaseService.getClient();

    // Get template statistics
    const [totalResult, activeResult, publicResult, privateResult] =
      await Promise.allSettled([
        supabase.from('templates').select('id', { count: 'exact', head: true }),
        supabase
          .from('templates')
          .select('id', { count: 'exact', head: true })
          .eq('is_active', true),
        supabase
          .from('templates')
          .select('id', { count: 'exact', head: true })
          .eq('is_public', true),
        supabase
          .from('templates')
          .select('id', { count: 'exact', head: true })
          .eq('is_public', false),
      ]);

    return {
      totalCount: this.extractCount(totalResult),
      activeCount: this.extractCount(activeResult),
      publicCount: this.extractCount(publicResult),
      privateCount: this.extractCount(privateResult),
    };
  }

  /**
   * Get calculations overview
   */
  private async getCalculationsOverview() {
    const supabase = this.supabaseService.getClient();

    // Get calculation statistics
    const [totalResult, draftResult, finalizedResult] =
      await Promise.allSettled([
        supabase
          .from('calculations')
          .select('id', { count: 'exact', head: true }),
        supabase
          .from('calculations')
          .select('id', { count: 'exact', head: true })
          .eq('status', 'draft'),
        supabase
          .from('calculations')
          .select('id', { count: 'exact', head: true })
          .eq('status', 'finalized'),
      ]);

    return {
      totalCount: this.extractCount(totalResult),
      draftCount: this.extractCount(draftResult),
      finalizedCount: this.extractCount(finalizedResult),
    };
  }

  /**
   * Get users overview
   */
  private async getUsersOverview() {
    const supabase = this.supabaseService.getClient();

    // Get user statistics
    const [totalResult, activeResult] = await Promise.allSettled([
      supabase.from('users').select('id', { count: 'exact', head: true }),
      supabase
        .from('users')
        .select('id', { count: 'exact', head: true })
        .eq('is_active', true),
    ]);

    return {
      totalCount: this.extractCount(totalResult),
      activeCount: this.extractCount(activeResult),
    };
  }

  /**
   * Get recent activity
   */
  private async getRecentActivity(days: number = 7) {
    const supabase = this.supabaseService.getClient();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const cutoffIso = cutoffDate.toISOString();

    // Get recent activity from various tables
    const [recentPackages, recentTemplates, recentCalculations] =
      await Promise.allSettled([
        supabase
          .from('packages')
          .select('id, name, created_at, updated_at')
          .gte('created_at', cutoffIso)
          .order('created_at', { ascending: false })
          .limit(10),
        supabase
          .from('templates')
          .select('id, name, created_at, updated_at')
          .gte('created_at', cutoffIso)
          .order('created_at', { ascending: false })
          .limit(10),
        supabase
          .from('calculations')
          .select('id, name, created_at, updated_at')
          .gte('created_at', cutoffIso)
          .order('created_at', { ascending: false })
          .limit(10),
      ]);

    const activities: Array<{
      type: string;
      id: string;
      name: string;
      action: string;
      timestamp: string;
    }> = [];

    // Process recent packages
    if (recentPackages.status === 'fulfilled' && recentPackages.value.data) {
      activities.push(
        ...recentPackages.value.data.map(item => ({
          type: 'package',
          id: item.id,
          name: item.name,
          action: 'created',
          timestamp: item.created_at,
        })),
      );
    }

    // Process recent templates
    if (recentTemplates.status === 'fulfilled' && recentTemplates.value.data) {
      activities.push(
        ...recentTemplates.value.data.map(item => ({
          type: 'template',
          id: item.id,
          name: item.name,
          action: 'created',
          timestamp: item.created_at,
        })),
      );
    }

    // Process recent calculations
    if (
      recentCalculations.status === 'fulfilled' &&
      recentCalculations.value.data
    ) {
      activities.push(
        ...recentCalculations.value.data.map(item => ({
          type: 'calculation',
          id: item.id,
          name: item.name,
          action: 'created',
          timestamp: item.created_at,
        })),
      );
    }

    // Sort by timestamp and limit
    return activities
      .sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      )
      .slice(0, 20);
  }

  /**
   * Extract count from Promise.allSettled result
   */
  private extractCount(result: PromiseSettledResult<any>): number {
    if (result.status === 'fulfilled' && result.value.count !== null) {
      return result.value.count;
    }
    return 0;
  }

  /**
   * Extract result from Promise.allSettled result
   */
  private extractResult<T>(
    result: PromiseSettledResult<T>,
    name: string,
    defaultValue?: T,
  ): T {
    if (result.status === 'fulfilled') {
      return result.value;
    }

    this.logger.warn(
      `Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`,
    );

    if (defaultValue !== undefined) {
      return defaultValue;
    }

    // For non-critical data, return empty array/object
    return [] as unknown as T;
  }

  /**
   * Collect errors from Promise.allSettled results
   */
  private collectErrors(results: PromiseSettledResult<any>[]): string[] {
    return results
      .filter(result => result.status === 'rejected')
      .map(
        result =>
          (result as PromiseRejectedResult).reason?.message || 'Unknown error',
      );
  }
}
