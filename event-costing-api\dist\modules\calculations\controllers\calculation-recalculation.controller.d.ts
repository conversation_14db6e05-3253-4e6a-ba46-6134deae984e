import { CalculationCrudService } from '../services/calculation-crud.service';
import { User } from '@supabase/supabase-js';
export declare class RecalculationResponseDto {
    success: boolean;
    message: string;
    timestamp: string;
}
export declare class CalculationRecalculationController {
    private readonly calculationCrudService;
    private readonly logger;
    constructor(calculationCrudService: CalculationCrudService);
    private validateCalculationOwnership;
    recalculateCalculation(calculationId: string, user: User): Promise<RecalculationResponseDto>;
}
