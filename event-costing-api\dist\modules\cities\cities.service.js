"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CitiesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CitiesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
let CitiesService = CitiesService_1 = class CitiesService {
    supabaseService;
    logger = new common_1.Logger(CitiesService_1.name);
    tableName = 'cities';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll() {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('cities')
            .select('id, name')
            .order('name', { ascending: true });
        if (error) {
            this.logger.error(`Failed to fetch cities: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve cities.');
        }
        return data || [];
    }
    async createCity(createCityDto) {
        this.logger.debug(`Creating city: ${createCityDto.name}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .insert({
            name: createCityDto.name,
        })
            .select('id, name')
            .single();
        if (error) {
            if (error.code === '23505') {
                this.logger.warn(`Attempted to create duplicate city name: ${createCityDto.name}`);
                throw new common_1.ConflictException(`A city with the name "${createCityDto.name}" already exists.`);
            }
            this.logger.error(`Failed to create city: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not create city.');
        }
        if (!data) {
            this.logger.error('City insert succeeded but returned no data.');
            throw new common_1.InternalServerErrorException('Failed to retrieve newly created city.');
        }
        this.logger.log(`Successfully created city ID: ${data.id}, Name: ${data.name}`);
        return data;
    }
    async updateCity(id, updateCityDto) {
        this.logger.debug(`Updating city ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const updateData = {};
        if (updateCityDto.name) {
            updateData.name = updateCityDto.name;
        }
        if (Object.keys(updateData).length === 0) {
            return this.findOne(id);
        }
        const { data, error } = await supabase
            .from(this.tableName)
            .update(updateData)
            .eq('id', id)
            .select('id, name')
            .single();
        if (error) {
            if (error.code === 'PGRST116') {
                this.logger.warn(`City not found for update: ID ${id}`);
                throw new common_1.NotFoundException(`City with ID ${id} not found.`);
            }
            if (error.code === '23505') {
                this.logger.warn(`Attempted duplicate city name during update: ${updateData.name}`);
                throw new common_1.ConflictException(`A city with the name "${updateData.name}" already exists.`);
            }
            this.logger.error(`Failed to update city ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not update city.');
        }
        if (!data) {
            throw new common_1.NotFoundException(`City with ID ${id} not found.`);
        }
        this.logger.log(`Successfully updated city ID: ${id}`);
        return data;
    }
    async deleteCity(id) {
        this.logger.debug(`Deleting city ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.tableName)
            .delete()
            .eq('id', id);
        if (error) {
            this.logger.error(`Failed to delete city ${id}: ${error.message}`, error.stack);
            if (error.code === '23503') {
                this.logger.warn(`Attempted to delete city ${id} which is still referenced.`);
                throw new common_1.ConflictException(`Cannot delete city because it is referenced by other records (e.g., calculations, packages).`);
            }
            throw new common_1.InternalServerErrorException('Could not delete city.');
        }
        if (count === 0) {
            this.logger.warn(`City not found for deletion: ID ${id}`);
            throw new common_1.NotFoundException(`City with ID ${id} not found.`);
        }
        this.logger.log(`Successfully deleted city ID: ${id}`);
    }
    async findOne(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.tableName)
            .select('id, name')
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`City with ID ${id} not found.`);
        }
        return data;
    }
};
exports.CitiesService = CitiesService;
exports.CitiesService = CitiesService = CitiesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CitiesService);
//# sourceMappingURL=cities.service.js.map