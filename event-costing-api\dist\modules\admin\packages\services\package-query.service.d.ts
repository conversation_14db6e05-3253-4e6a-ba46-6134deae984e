import { SupabaseService } from 'src/core/supabase/supabase.service';
import { PackageDto } from '../dto/package.dto';
import { PackageListQueryDto } from '../dto/package-list-query.dto';
import { PaginatedResponseDto } from 'src/shared/dtos/paginated-response.dto';
export declare class PackageQueryService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    findAllPackages(queryDto: PackageListQueryDto): Promise<PaginatedResponseDto<PackageDto>>;
    findPackageById(id: string): Promise<PackageDto>;
    getPackageIds(packages: any[]): string[];
    fetchCategoriesMap(categoryIds: string[]): Promise<Map<string, string>>;
    fetchDivisionsMap(divisionIds: string[]): Promise<Map<string, string>>;
    fetchPackagePricesMap(packageIds: string[]): Promise<Map<string, any>>;
    private fetchCurrenciesMap;
    fetchPackageCitiesMap(packageIds: string[]): Promise<Map<string, string[]>>;
    private fetchCitiesMap;
}
