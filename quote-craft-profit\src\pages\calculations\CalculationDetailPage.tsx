/**
 * Main calculation detail page
 * PERFORMANCE OPTIMIZED: Eliminated object recreation and stabilized hook dependencies
 * PHASE 4 OPTIMIZATION: Simplified state combination logic and streamlined hook chain
 * PHASE 1 OPTIMIZATION: Merged with container component to reduce 4-layer hierarchy to 3 layers
 * Eliminates unnecessary wrapper component while preserving all functionality
 */
import React, { useMemo, useCallback } from "react";
import { useParams } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import {
  useRenderTracker,
  useObjectReferenceTracker,
} from "@/lib/renderTracker";

// Import hooks - PHASE 4 OPTIMIZATION: Using comprehensive hook
import { useCalculationDetailComplete } from "./hooks/core/useCalculationDetailComplete";

// Import components
import CalculationDetailHeader from "./components/detail/layout/CalculationDetailHeader";
import CalculationDetailContent from "./components/detail/layout/CalculationDetailContent";
import CalculationDetailError from "./components/detail/CalculationDetailError";
import CalculationErrorBoundary from "./components/common/CalculationErrorBoundary";
import CalculationDetailSkeleton from "./components/common/CalculationDetailSkeleton";
import {
  MigrationStatusIndicator,
  useMigrationStatus,
} from "./components/common/MigrationStatusIndicator";

// Import context provider
import { CalculationProvider } from "./contexts";
// Import UUID validation utility
import { useSafeUUID } from "@/lib/uuidValidation";

const CalculationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // CRITICAL FIX: Use safe UUID validation to prevent race conditions
  const { isValid, validId, shouldLoadData } = useSafeUUID(id);

  // PERFORMANCE FIX: Always call hooks, but conditionally enable data fetching
  // This prevents conditional hook calls while maintaining performance
  const hookResult = useCalculationDetailComplete(validId || "");

  // PERFORMANCE OPTIMIZATION: Memoize fallback actions to prevent recreation
  const fallbackActions = useMemo(
    () => ({
      handleNavigateBack: () => window.history.back(),
      handleDelete: async () => {},
      handleStatusChange: async () => {},
      handleNavigateToList: () => (window.location.href = "/calculations"),
    }),
    []
  );

  // PERFORMANCE OPTIMIZATION: Memoize the final result based on validation
  const { state, actions, calculation, isLoading, isError } = useMemo(() => {
    if (!shouldLoadData) {
      return {
        state: null,
        actions: fallbackActions,
        calculation: null,
        isLoading: false,
        isError: false,
      };
    }
    return hookResult;
  }, [shouldLoadData, hookResult, fallbackActions]);

  // PERFORMANCE OPTIMIZATION: Get migration status (hook must be called at top level)
  const migrationStatus = useMigrationStatus(state);

  // DEBUG: Track main component renders and state changes
  useRenderTracker(
    "CalculationDetailPage",
    {
      id,
      isValid,
      shouldLoadData,
      hasState: !!state,
      hasActions: !!actions,
      hasCalculation: !!calculation,
      isLoading,
      isError,
      calculationName: calculation?.name,
    },
    { logLevel: "detailed" }
  );

  useObjectReferenceTracker("state", state);
  useObjectReferenceTracker("actions", actions);

  // PERFORMANCE OPTIMIZATION: Memoize callback functions to prevent child re-renders
  const handleRetry = useCallback(() => window.location.reload(), []);

  // PERFORMANCE OPTIMIZATION: Memoize migration indicator props
  const migrationIndicatorProps = useMemo(
    () => ({
      ...migrationStatus,
      visible: process.env.NODE_ENV === "development",
    }),
    [migrationStatus]
  );

  // CRITICAL FIX: Check for valid ID to prevent race conditions
  if (!isValid) {
    return (
      <MainLayout>
        <CalculationDetailError message="Invalid calculation ID format" />
      </MainLayout>
    );
  }

  // Show loading state with enhanced skeleton
  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-6">
          <CalculationDetailSkeleton />
        </div>
      </MainLayout>
    );
  }

  // Show error state
  if (isError || !calculation) {
    return (
      <MainLayout>
        <CalculationDetailError
          message="Error loading calculation details"
          onNavigateBack={actions.handleNavigateBack}
        />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <CalculationErrorBoundary
        calculationId={id}
        onRetry={handleRetry}
        onNavigateBack={actions.handleNavigateBack}
      >
        {/* Header */}
        <CalculationDetailHeader
          name={calculation.name}
          status={calculation.status}
          onNavigateBack={actions.handleNavigateBack}
        />

        {/* Main Content with Context Provider */}
        <CalculationProvider
          calculationId={id}
          state={state as any} // TODO: Fix type compatibility
          actions={actions}
        >
          <CalculationDetailContent />
        </CalculationProvider>

        {/* Migration Status Indicator (development only) */}
        <MigrationStatusIndicator {...migrationIndicatorProps} />
      </CalculationErrorBoundary>
    </MainLayout>
  );
};

export default CalculationDetailPage;
