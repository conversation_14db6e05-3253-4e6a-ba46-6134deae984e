import { CitiesService } from './cities.service';
import { CityDto } from './dto/city.dto';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
export declare class AdminCitiesController {
    private readonly citiesService;
    private readonly logger;
    constructor(citiesService: CitiesService);
    createCity(createCityDto: CreateCityDto): Promise<CityDto>;
    updateCity(id: string, updateCityDto: UpdateCityDto): Promise<CityDto>;
    deleteCity(id: string): Promise<void>;
}
