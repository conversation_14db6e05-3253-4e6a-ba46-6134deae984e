"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageVenuesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageVenuesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let PackageVenuesService = PackageVenuesService_1 = class PackageVenuesService {
    supabaseService;
    logger = new common_1.Logger(PackageVenuesService_1.name);
    PACKAGE_VENUES_TABLE = 'package_venues';
    VENUES_TABLE = 'venues';
    PACKAGES_TABLE = 'packages';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async addVenueToPackage(packageId, venueId) {
        this.logger.log(`Attempting to add venue ${venueId} to package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.PACKAGE_VENUES_TABLE)
            .insert({ package_id: packageId, venue_id: venueId })
            .select('id');
        const typedData = data;
        if (error) {
            this.logger.error(`Error adding venue ${venueId} to package ${packageId}: ${error.message}`);
            if (error.code === '23505') {
                throw new common_1.ConflictException('This venue is already associated with the package.');
            }
            if (error.code === '23503') {
                throw new common_1.NotFoundException('Package or Venue not found.');
            }
            throw new common_1.InternalServerErrorException('Failed to add venue to package.');
        }
        return { id: typedData[0].id };
    }
    async listVenuesForPackage(packageId) {
        this.logger.log(`Fetching venues for package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.PACKAGE_VENUES_TABLE)
            .select(`
        venue: ${this.VENUES_TABLE} (
          id,
          name,
          address,
          city_id
        )
      `)
            .eq('package_id', packageId)
            .eq('is_deleted', false)
            .returns();
        if (error) {
            this.logger.error(`Error fetching venues for package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Error fetching venues for package.');
        }
        const venues = data
            ?.map(item => item.venue)
            .filter((venue) => venue !== null)
            .map(venue => ({
            id: venue.id,
            name: venue.name,
            address: venue.address,
            city_id: venue.city_id
        })) || [];
        if (venues.length === 0) {
            const { count: packageCount } = await supabase
                .from(this.PACKAGES_TABLE)
                .select('*', { count: 'exact', head: true })
                .eq('id', packageId);
            if (packageCount === 0) {
                throw new common_1.NotFoundException(`Package with ID ${packageId} not found.`);
            }
        }
        return venues;
    }
    async removeVenueFromPackage(packageId, venueId) {
        this.logger.log(`Removing venue ${venueId} from package ${packageId}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from(this.PACKAGE_VENUES_TABLE)
            .delete()
            .eq('package_id', packageId)
            .eq('venue_id', venueId);
        if (error) {
            this.logger.error(`Error removing venue ${venueId} from package ${packageId}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to remove venue from package.');
        }
        this.logger.log(`Successfully removed venue ${venueId} from package ${packageId}`);
    }
};
exports.PackageVenuesService = PackageVenuesService;
exports.PackageVenuesService = PackageVenuesService = PackageVenuesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageVenuesService);
//# sourceMappingURL=package-venues.service.js.map