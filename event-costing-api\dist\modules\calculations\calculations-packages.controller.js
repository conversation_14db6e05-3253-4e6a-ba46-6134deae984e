"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CalculationsPackagesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationsPackagesController = void 0;
const common_1 = require("@nestjs/common");
const calculations_service_1 = require("./calculations.service");
const packages_service_1 = require("../packages/packages.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_current_user_decorator_1 = require("../auth/decorators/get-current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const packages_by_category_response_dto_1 = require("../packages/dto/packages-by-category-response.dto");
let CalculationsPackagesController = CalculationsPackagesController_1 = class CalculationsPackagesController {
    calculationsService;
    packagesService;
    logger = new common_1.Logger(CalculationsPackagesController_1.name);
    constructor(calculationsService, packagesService) {
        this.calculationsService = calculationsService;
        this.packagesService = packagesService;
    }
    async getAvailablePackagesByCategory(calculationId, includeOptions = false, user) {
        this.logger.log(`User ${user.email} fetching available packages for calculation ID: ${calculationId} (includeOptions: ${includeOptions})`);
        try {
            await this.calculationsService.checkCalculationOwnership(calculationId, user.id);
            const calculation = await this.calculationsService.findCalculationById(calculationId, user);
            if (!calculation) {
                throw new common_1.NotFoundException(`Calculation with ID ${calculationId} not found`);
            }
            if (!calculation.currency?.id) {
                throw new common_1.NotFoundException(`Calculation ${calculationId} has no currency set`);
            }
            const venueId = calculation.venues && calculation.venues.length > 0
                ? calculation.venues[0].id
                : undefined;
            return this.packagesService.getPackagesByCategory(calculation.currency.id, calculation.city?.id, venueId, includeOptions);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Error fetching available packages for calculation ${calculationId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve available packages');
        }
    }
};
exports.CalculationsPackagesController = CalculationsPackagesController;
__decorate([
    (0, common_1.Get)(':id/available-packages'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available packages organized by category for a calculation',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', format: 'uuid' }),
    (0, swagger_1.ApiQuery)({
        name: 'includeOptions',
        required: false,
        type: Boolean,
        description: 'Whether to include package options in the response',
    }),
    (0, swagger_1.ApiOkResponse)({ type: packages_by_category_response_dto_1.PackagesByCategoryResponseDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('includeOptions')),
    __param(2, (0, get_current_user_decorator_1.GetCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, Object]),
    __metadata("design:returntype", Promise)
], CalculationsPackagesController.prototype, "getAvailablePackagesByCategory", null);
exports.CalculationsPackagesController = CalculationsPackagesController = CalculationsPackagesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Calculation Packages'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('calculations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [calculations_service_1.CalculationsService,
        packages_service_1.PackagesService])
], CalculationsPackagesController);
//# sourceMappingURL=calculations-packages.controller.js.map