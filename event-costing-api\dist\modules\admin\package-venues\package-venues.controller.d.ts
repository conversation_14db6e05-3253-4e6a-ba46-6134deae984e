import { PackageVenuesService } from './package-venues.service';
import { PackageVenueDto } from './dto/package-venue.dto';
import { AddPackageVenueDto } from './dto/add-package-venue.dto';
export declare class PackageVenuesController {
    private readonly packageVenuesService;
    private readonly logger;
    constructor(packageVenuesService: PackageVenuesService);
    addVenue(packageId: string, addDto: AddPackageVenueDto): Promise<{
        id: string;
    }>;
    listVenues(packageId: string): Promise<PackageVenueDto[]>;
    removeVenue(packageId: string, venueId: string): Promise<void>;
}
