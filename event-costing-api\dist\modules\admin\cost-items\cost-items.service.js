"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CostItemsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostItemsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let CostItemsService = CostItemsService_1 = class CostItemsService {
    supabaseService;
    logger = new common_1.Logger(CostItemsService_1.name);
    TABLE_NAME = 'cost_items';
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(createDto) {
        this.logger.log(`Attempting to create cost item: ${JSON.stringify(createDto)}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .insert({
            ...createDto,
        })
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error creating cost item: ${error.message}`, error.stack);
            if (error.code === '23505') {
                throw new common_1.ConflictException(`Cost item with code '${createDto.item_code}' may already exist.`);
            }
            throw new common_1.InternalServerErrorException(`Failed to create cost item: ${error.message}`);
        }
        if (!data) {
            throw new common_1.InternalServerErrorException('Cost item creation failed unexpectedly (no data returned).');
        }
        this.logger.log(`Cost item created successfully with ID: ${data.id}`);
        return data;
    }
    async findAll() {
        this.logger.log('Fetching all cost items');
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select('*')
            .eq('is_deleted', false)
            .order('name', { ascending: true });
        if (error) {
            this.logger.error(`Error fetching cost items: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to fetch cost items: ${error.message}`);
        }
        return (data ?? []);
    }
    async findOne(id) {
        this.logger.log(`Fetching cost item with ID: ${id}`);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .select('*')
            .match({ id: id, is_deleted: false })
            .single();
        if (error) {
            this.logger.error(`Error fetching cost item ${id}: ${error.message}`, error.stack);
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Cost item with ID ${id} not found.`);
            }
            throw new common_1.InternalServerErrorException(`Failed to fetch cost item ${id}: ${error.message}`);
        }
        if (!data) {
            throw new common_1.NotFoundException(`Cost item with ID ${id} not found.`);
        }
        return data;
    }
    async update(id, updateDto) {
        this.logger.log(`Attempting to update cost item ${id}: ${JSON.stringify(updateDto)}`);
        const supabase = this.supabaseService.getClient();
        if (Object.keys(updateDto).length === 0) {
            this.logger.warn(`Update called for cost item ${id} with no data. Fetching current.`);
            return this.findOne(id);
        }
        const { data, error } = await supabase
            .from(this.TABLE_NAME)
            .update(updateDto)
            .match({ id: id, is_deleted: false })
            .select('*')
            .single();
        if (error) {
            this.logger.error(`Error updating cost item ${id}: ${error.message}`, error.stack);
            if (error.code === 'PGRST116') {
                throw new common_1.NotFoundException(`Cost item with ID ${id} not found.`);
            }
            if (error.code === '23505') {
                throw new common_1.ConflictException(`Update failed due to unique constraint (e.g., item code '${updateDto.item_code}' already exists).`);
            }
            throw new common_1.InternalServerErrorException(`Failed to update cost item ${id}: ${error.message}`);
        }
        if (!data) {
            throw new common_1.NotFoundException(`Cost item with ID ${id} could not be updated (not found or unexpected state).`);
        }
        this.logger.log(`Cost item ${id} updated successfully.`);
        return data;
    }
    async remove(id) {
        this.logger.log(`Attempting to soft-delete cost item ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error, count } = await supabase
            .from(this.TABLE_NAME)
            .update({ is_deleted: true, deleted_at: new Date().toISOString() })
            .match({ id: id, is_deleted: false });
        if (error) {
            this.logger.error(`Error soft-deleting cost item ${id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`Failed to delete cost item ${id}: ${error.message}`);
        }
        if (count === 0) {
            throw new common_1.NotFoundException(`Cost item with ID ${id} not found or already deleted.`);
        }
        this.logger.log(`Cost item ${id} soft-deleted successfully.`);
    }
};
exports.CostItemsService = CostItemsService;
exports.CostItemsService = CostItemsService = CostItemsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CostItemsService);
//# sourceMappingURL=cost-items.service.js.map