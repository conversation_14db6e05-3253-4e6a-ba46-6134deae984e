"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminVenuesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminVenuesController = void 0;
const common_1 = require("@nestjs/common");
const venues_service_1 = require("./venues.service");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_role_guard_1 = require("../auth/guards/admin-role.guard");
const admin_venue_dto_1 = require("./dto/admin-venue.dto");
let AdminVenuesController = AdminVenuesController_1 = class AdminVenuesController {
    venuesService;
    logger = new common_1.Logger(AdminVenuesController_1.name);
    constructor(venuesService) {
        this.venuesService = venuesService;
    }
    async findAll(queryDto) {
        this.logger.log(`Admin request to get all venues with query: ${JSON.stringify(queryDto)}`);
        return this.venuesService.findAllAdmin(queryDto);
    }
    async findOne(id) {
        this.logger.log(`Admin request to get venue with ID: ${id}`);
        return this.venuesService.findOneAdmin(id);
    }
    async create(createVenueDto) {
        this.logger.log(`Admin request to create venue: ${JSON.stringify(createVenueDto)}`);
        return this.venuesService.create(createVenueDto);
    }
    async update(id, updateVenueDto) {
        this.logger.log(`Admin request to update venue with ID ${id}: ${JSON.stringify(updateVenueDto)}`);
        return this.venuesService.update(id, updateVenueDto);
    }
    async remove(id) {
        this.logger.log(`Admin request to delete venue with ID: ${id}`);
        await this.venuesService.remove(id);
    }
    async restore(id) {
        this.logger.log(`Admin request to restore venue with ID: ${id}`);
        await this.venuesService.restore(id);
    }
};
exports.AdminVenuesController = AdminVenuesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all venues (admin)' }),
    (0, swagger_1.ApiQuery)({ type: admin_venue_dto_1.ListVenuesQueryDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of venues with pagination',
        type: admin_venue_dto_1.PaginatedVenuesResponse,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_venue_dto_1.ListVenuesQueryDto]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a venue by ID (admin)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Venue details',
        type: admin_venue_dto_1.AdminVenueDto,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new venue' }),
    (0, swagger_1.ApiBody)({ type: admin_venue_dto_1.CreateVenueDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Venue created successfully',
        type: admin_venue_dto_1.AdminVenueDto,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_venue_dto_1.CreateVenueDto]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a venue' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: admin_venue_dto_1.UpdateVenueDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Venue updated successfully',
        type: admin_venue_dto_1.AdminVenueDto,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, admin_venue_dto_1.UpdateVenueDto]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a venue (soft delete)' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Venue deleted successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/restore'),
    (0, swagger_1.ApiOperation)({ summary: 'Restore a deleted venue' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, format: 'uuid' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Venue restored successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminVenuesController.prototype, "restore", null);
exports.AdminVenuesController = AdminVenuesController = AdminVenuesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Venues'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/venues'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    __metadata("design:paramtypes", [venues_service_1.VenuesService])
], AdminVenuesController);
//# sourceMappingURL=admin-venues.controller.js.map