"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminEventTypesController = exports.EventTypesController = void 0;
const common_1 = require("@nestjs/common");
const event_types_service_1 = require("./event-types.service");
const event_type_dto_1 = require("./dto/event-type.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let EventTypesController = class EventTypesController {
    eventTypesService;
    constructor(eventTypesService) {
        this.eventTypesService = eventTypesService;
    }
    async findAll() {
        const data = await this.eventTypesService.findAll();
        return { data };
    }
    async findOne(id) {
        const data = await this.eventTypesService.findOne(id);
        return { data };
    }
};
exports.EventTypesController = EventTypesController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTypesController.prototype, "findOne", null);
exports.EventTypesController = EventTypesController = __decorate([
    (0, common_1.Controller)('event-types'),
    __metadata("design:paramtypes", [event_types_service_1.EventTypesService])
], EventTypesController);
let AdminEventTypesController = class AdminEventTypesController {
    eventTypesService;
    constructor(eventTypesService) {
        this.eventTypesService = eventTypesService;
    }
    async findAllAdmin() {
        const data = await this.eventTypesService.findAllAdmin();
        return { data };
    }
    async findOne(id) {
        const data = await this.eventTypesService.findOne(id);
        return { data };
    }
    async create(createEventTypeDto) {
        const data = await this.eventTypesService.create(createEventTypeDto);
        return { data };
    }
    async update(id, updateEventTypeDto) {
        const data = await this.eventTypesService.update(id, updateEventTypeDto);
        return { data };
    }
    async remove(id) {
        await this.eventTypesService.remove(id);
    }
};
exports.AdminEventTypesController = AdminEventTypesController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminEventTypesController.prototype, "findAllAdmin", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminEventTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [event_type_dto_1.CreateEventTypeDto]),
    __metadata("design:returntype", Promise)
], AdminEventTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, event_type_dto_1.UpdateEventTypeDto]),
    __metadata("design:returntype", Promise)
], AdminEventTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminEventTypesController.prototype, "remove", null);
exports.AdminEventTypesController = AdminEventTypesController = __decorate([
    (0, common_1.Controller)('admin/event-types'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [event_types_service_1.EventTypesService])
], AdminEventTypesController);
//# sourceMappingURL=event-types.controller.js.map