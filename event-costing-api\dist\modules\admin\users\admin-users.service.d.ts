import { SupabaseService } from '../../../core/supabase/supabase.service';
import { AdminUserDto } from './dto/admin-user.dto';
import { RoleDto } from './dto/role.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class AdminUsersService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    findAll(): Promise<AdminUserDto[]>;
    findOne(id: string): Promise<AdminUserDto>;
    updateRole(id: string, updateUserRoleDto: UpdateUserRoleDto): Promise<void>;
    updateStatus(id: string, updateUserStatusDto: UpdateUserStatusDto): Promise<void>;
    getRoles(): Promise<RoleDto[]>;
    createUser(createUserDto: CreateUserDto): Promise<AdminUserDto>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<AdminUserDto>;
}
