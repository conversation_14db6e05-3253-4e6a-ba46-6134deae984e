{"version": 3, "file": "calculation-template.service.js", "sourceRoot": "", "sources": ["../../../src/modules/calculations/calculation-template.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAKwB;AACxB,2EAAuE;AAOvE,8FAAyF;AACzF,6EAAoE;AAG7D,IAAM,0BAA0B,kCAAhC,MAAM,0BAA0B;IAIlB;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,4BAA0B,CAAC,IAAI,CAAC,CAAC;IAEtE,YACmB,eAAgC,EAChC,uBAAgD;QADhD,oBAAe,GAAf,eAAe,CAAiB;QAChC,4BAAuB,GAAvB,uBAAuB,CAAyB;IAChE,CAAC;IAEJ,KAAK,CAAC,kBAAkB,CACtB,UAAkB,EAClB,aAA+C,EAC/C,IAAU;QAEV,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,QAAQ,IAAI,CAAC,EAAE,gDAAgD,UAAU,wBAAwB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CACjI,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAGlD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;aAChE,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;aACpB,EAAE,CAAC,mCAAmC,IAAI,CAAC,EAAE,EAAE,CAAC;aAChD,WAAW,EAAgB,CAAC;QAE/B,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,UAAU,aAAa,IAAI,CAAC,EAAE,KAAK,aAAa,CAAC,OAAO,EAAE,EACrF,aAAa,CAAC,KAAK,CACpB,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,mCAAmC,CACpC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,YAAY,UAAU,sBAAsB,IAAI,CAAC,EAAE,gBAAgB,CACpE,CAAC;YACF,MAAM,IAAI,0BAAiB,CACzB,oBAAoB,UAAU,8BAA8B,CAC7D,CAAC;QACJ,CAAC;QAGD,MAAM,iBAAiB,GACrB,YAAY,CAAC,kBAAkB,IAAI,EAAE,CAAC;QAExC,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,OAAO,EAAE,aAAa,CAAC,MAAM,IAAI,YAAY,CAAC,OAAO;YACrD,SAAS,EAAE,aAAa,CAAC,QAAQ,IAAI,IAAI;YACzC,QAAQ,EAAE,aAAa,CAAC,OAAO,IAAI,IAAI;YACvC,gBAAgB,EAAE,aAAa,CAAC,cAAc,IAAI,YAAY,CAAC,mBAAmB;YAClF,cAAc,EAAE,aAAa,CAAC,YAAY,IAAI,YAAY,CAAC,iBAAiB;YAC5E,SAAS,EAAE,aAAa,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS;YAC5D,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,sBAAsB,YAAY,CAAC,aAAa,EAAE;YAChF,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,MAAM,EAAE,2CAAiB,CAAC,KAAK;YAC/B,QAAQ,EAAE,CAAC;YACX,KAAK,EAAE,YAAY,CAAC,KAAK,IAAI,IAAI;YACjC,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,IAAI;YACvC,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;SACpB,CAAC;QAGF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aACzD,IAAI,CAAC,qBAAqB,CAAC;aAC3B,MAAM,CAAC,eAAe,CAAC;aACvB,MAAM,CAAC,IAAI,CAAC;aACZ,MAAM,EAAkB,CAAC;QAE5B,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sDAAsD,UAAU,KAAK,WAAW,EAAE,OAAO,EAAE,EAC3F,WAAW,EAAE,KAAK,CACnB,CAAC;YACF,MAAM,IAAI,qCAA4B,CACpC,sCAAsC,CACvC,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAW,OAAO,CAAC,EAAE,CAAC;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,uBAAuB,gBAAgB,kBAAkB,UAAU,EAAE,CACtE,CAAC;QAGF,IAAI,QAAQ,GAAa,EAAE,CAAC;QAE5B,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAEhE,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,SAAS,QAAQ,CAAC,MAAM,iDAAiD,gBAAgB,EAAE,CAC5F,CAAC;QACJ,CAAC;aAAM,CAAC;YAEN,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;iBAC/D,IAAI,CAAC,iBAAiB,CAAC;iBACvB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YAEjC,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,UAAU,KAAK,UAAU,CAAC,OAAO,EAAE,EACzE,UAAU,CAAC,KAAK,CACjB,CAAC;YAEJ,CAAC;iBAAM,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvD,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,SAAS,QAAQ,CAAC,MAAM,4CAA4C,gBAAgB,EAAE,CACvF,CAAC;YACJ,CAAC;QACH,CAAC;QAGD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC7C,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,OAAO;aAClB,CAAC,CAAC,CAAC;YAEJ,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;iBAC/C,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,MAAM,CAAC,aAAa,CAAC,CAAC;YAEzB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sCAAsC,gBAAgB,KAAK,gBAAgB,CAAC,OAAO,EAAE,EACrF,gBAAgB,CAAC,KAAK,CACvB,CAAC;YAEJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sBAAsB,QAAQ,CAAC,MAAM,0BAA0B,gBAAgB,EAAE,CAClF,CAAC;YACJ,CAAC;QACH,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,uBAAuB,CAAC,kCAAkC,CACnE,gBAAgB,EAChB,YAAY,CAAC,WAAW,EACxB,iBAAiB,EACjB,IAAI,CACL,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,gDAAgD,gBAAgB,GAAG,CACpE,CAAC;QACJ,CAAC;QAAC,OAAO,SAAkB,EAAE,CAAC;YAC5B,MAAM,YAAY,GAChB,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrE,MAAM,UAAU,GACd,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iDAAiD,gBAAgB,kBAAkB,UAAU,qDAAqD,YAAY,EAAE,EAChK,UAAU,CACX,CAAC;QAEJ,CAAC;QAGD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,2DAA2D,gBAAgB,EAAE,CAC9E,CAAC;YACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAC5C,gCAAgC,EAChC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CACvC,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,oEAAoE,gBAAgB,KAAK,QAAQ,CAAC,OAAO,EAAE,EAC3G,QAAQ,CAAC,KAAK,CACf,CAAC;YAEJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,4DAA4D,gBAAgB,EAAE,CAC/E,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,WAAoB,EAAE,CAAC;YAC9B,MAAM,YAAY,GAChB,WAAW,YAAY,KAAK;gBAC1B,CAAC,CAAC,WAAW,CAAC,OAAO;gBACrB,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC1B,MAAM,UAAU,GACd,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kEAAkE,gBAAgB,MAAM,YAAY,EAAE,EACtG,UAAU,CACX,CAAC;QAEJ,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF,CAAA;AA9MY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;qCAKyB,kCAAe;QACP,mDAAuB;GALxD,0BAA0B,CA8MtC"}