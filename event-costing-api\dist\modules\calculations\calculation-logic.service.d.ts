import { SupabaseService } from '../../core/supabase/supabase.service';
import { CalculationTotalsDto } from './dto/calculation-totals.dto';
export declare class CalculationLogicService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    fetchAndCalculateTotals(calcId: string): Promise<CalculationTotalsDto | null>;
    recalculateTotals(calcId: string): Promise<void>;
}
