/**
 * UUID Validation Utilities
 * 
 * Provides utilities for validating UUID format to prevent race conditions
 * and backend validation errors when using route parameters.
 * 
 * RACE CONDITION PREVENTION:
 * These utilities help prevent API calls with invalid UUIDs during route transitions
 * where useParams() might return undefined or empty strings.
 */

/**
 * Regular expression for validating UUID v4 format
 * Matches the standard UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
 * where x is any hexadecimal digit and y is one of 8, 9, A, or B
 */
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * Validates if a string is a valid UUID format
 * 
 * @param id - The string to validate
 * @returns true if the string is a valid UUID, false otherwise
 * 
 * @example
 * ```typescript
 * isValidUUID('123e4567-e89b-12d3-a456-************') // true
 * isValidUUID('invalid-uuid') // false
 * isValidUUID('') // false
 * isValidUUID(undefined) // false
 * ```
 */
export const isValidUUID = (id: string | undefined | null): id is string => {
  if (!id || typeof id !== 'string') {
    return false;
  }
  return UUID_REGEX.test(id);
};

/**
 * Validates a UUID and returns it if valid, otherwise returns undefined
 * Useful for conditional hook execution and preventing API calls with invalid IDs
 * 
 * @param id - The UUID string to validate
 * @returns The UUID string if valid, undefined otherwise
 * 
 * @example
 * ```typescript
 * const { id } = useParams();
 * const validId = getValidUUID(id);
 * 
 * // Only make API call if ID is valid
 * const { data } = useQuery({
 *   queryKey: ['data', validId],
 *   queryFn: () => fetchData(validId!),
 *   enabled: !!validId,
 * });
 * ```
 */
export const getValidUUID = (id: string | undefined | null): string | undefined => {
  return isValidUUID(id) ? id : undefined;
};

/**
 * Hook for safely getting and validating UUID from route parameters
 * Prevents race conditions by ensuring only valid UUIDs are returned
 * 
 * @param id - The ID from useParams()
 * @returns Object with validation status and safe ID
 * 
 * @example
 * ```typescript
 * const { id } = useParams();
 * const { isValid, validId, shouldLoadData } = useSafeUUID(id);
 * 
 * const { data } = useQuery({
 *   queryKey: ['data', validId],
 *   queryFn: () => fetchData(validId!),
 *   enabled: shouldLoadData,
 * });
 * 
 * if (!isValid) {
 *   return <ErrorComponent message="Invalid ID" />;
 * }
 * ```
 */
export const useSafeUUID = (id: string | undefined | null) => {
  const isValid = isValidUUID(id);
  const validId = isValid ? id : undefined;
  const shouldLoadData = isValid;

  return {
    isValid,
    validId,
    shouldLoadData,
  };
};

/**
 * Type guard for UUID strings
 * Useful for TypeScript type narrowing
 * 
 * @param value - The value to check
 * @returns true if value is a valid UUID string
 */
export const isUUIDString = (value: unknown): value is string => {
  return typeof value === 'string' && isValidUUID(value);
};

/**
 * Validates multiple UUIDs at once
 * Useful for batch operations or when multiple IDs are required
 * 
 * @param ids - Array of UUID strings to validate
 * @returns Object with validation results
 * 
 * @example
 * ```typescript
 * const { allValid, validIds, invalidIds } = validateMultipleUUIDs([id1, id2, id3]);
 * 
 * if (!allValid) {
 *   console.error('Invalid UUIDs:', invalidIds);
 *   return;
 * }
 * 
 * // All IDs are valid, proceed with API call
 * const data = await fetchMultipleData(validIds);
 * ```
 */
export const validateMultipleUUIDs = (ids: (string | undefined | null)[]) => {
  const validIds: string[] = [];
  const invalidIds: (string | undefined | null)[] = [];

  ids.forEach(id => {
    if (isValidUUID(id)) {
      validIds.push(id);
    } else {
      invalidIds.push(id);
    }
  });

  return {
    allValid: invalidIds.length === 0,
    validIds,
    invalidIds,
    validCount: validIds.length,
    invalidCount: invalidIds.length,
  };
};

/**
 * Error class for UUID validation failures
 * Provides structured error information for debugging
 */
export class UUIDValidationError extends Error {
  constructor(
    public readonly invalidId: string | undefined | null,
    public readonly context?: string
  ) {
    super(`Invalid UUID format: ${invalidId}${context ? ` (${context})` : ''}`);
    this.name = 'UUIDValidationError';
  }
}

/**
 * Validates UUID and throws error if invalid
 * Useful for strict validation scenarios
 * 
 * @param id - The UUID to validate
 * @param context - Optional context for error message
 * @throws UUIDValidationError if UUID is invalid
 * 
 * @example
 * ```typescript
 * try {
 *   assertValidUUID(id, 'calculation detail page');
 *   // Proceed with API call
 * } catch (error) {
 *   if (error instanceof UUIDValidationError) {
 *     // Handle UUID validation error
 *   }
 * }
 * ```
 */
export const assertValidUUID = (
  id: string | undefined | null,
  context?: string
): asserts id is string => {
  if (!isValidUUID(id)) {
    throw new UUIDValidationError(id, context);
  }
};

/**
 * Default export for convenience
 */
export default {
  isValidUUID,
  getValidUUID,
  useSafeUUID,
  isUUIDString,
  validateMultipleUUIDs,
  UUIDValidationError,
  assertValidUUID,
};
