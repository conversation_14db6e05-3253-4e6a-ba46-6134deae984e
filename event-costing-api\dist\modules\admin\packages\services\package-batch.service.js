"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackageBatchService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageBatchService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../../core/supabase/supabase.service");
let PackageBatchService = PackageBatchService_1 = class PackageBatchService {
    supabaseService;
    logger = new common_1.Logger(PackageBatchService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async batchUpdatePackages(batchUpdateDto) {
        this.logger.log(`Attempting to batch update ${batchUpdateDto.packages.length} packages`);
        const supabase = this.supabaseService.getClient();
        const errors = [];
        let updatedCount = 0;
        const batchSize = 10;
        const batches = [];
        for (let i = 0; i < batchUpdateDto.packages.length; i += batchSize) {
            batches.push(batchUpdateDto.packages.slice(i, i + batchSize));
        }
        for (const batch of batches) {
            const updatePromises = batch.map(async (packageUpdate) => {
                try {
                    const updateData = {};
                    if (packageUpdate.categoryId) {
                        updateData.category_id = packageUpdate.categoryId;
                    }
                    if (packageUpdate.divisionId) {
                        updateData.division_id = packageUpdate.divisionId;
                    }
                    if (Object.keys(updateData).length === 0) {
                        return {
                            success: true,
                            id: packageUpdate.id,
                            message: 'No changes to apply',
                        };
                    }
                    const { data, error } = await supabase
                        .from('packages')
                        .update(updateData)
                        .eq('id', packageUpdate.id)
                        .select('id');
                    if (error) {
                        this.logger.error(`Error updating package ${packageUpdate.id}: ${error.message}`, error.stack);
                        return {
                            success: false,
                            id: packageUpdate.id,
                            error: error.message,
                        };
                    }
                    updatedCount++;
                    return { success: true, id: packageUpdate.id };
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    const errorStack = error instanceof Error ? error.stack : undefined;
                    this.logger.error(`Unexpected error updating package ${packageUpdate.id}: ${errorMessage}`, errorStack);
                    return {
                        success: false,
                        id: packageUpdate.id,
                        error: errorMessage,
                    };
                }
            });
            const results = await Promise.all(updatePromises);
            results
                .filter(result => !result.success)
                .forEach(result => errors.push(result));
        }
        this.logger.log(`Batch update completed. Updated ${updatedCount} packages with ${errors.length} errors.`);
        return {
            updatedCount,
            errors,
        };
    }
    async batchDeletePackages(packageIds) {
        this.logger.log(`Attempting to batch delete ${packageIds.length} packages`);
        const supabase = this.supabaseService.getClient();
        const errors = [];
        let deletedCount = 0;
        const batchSize = 10;
        const batches = [];
        for (let i = 0; i < packageIds.length; i += batchSize) {
            batches.push(packageIds.slice(i, i + batchSize));
        }
        for (const batch of batches) {
            const deletePromises = batch.map(async (packageId) => {
                try {
                    const { error, count } = await supabase
                        .from('packages')
                        .update({ is_deleted: true })
                        .match({ id: packageId, is_deleted: false });
                    if (error) {
                        this.logger.error(`Error deleting package ${packageId}: ${error.message}`, error.stack);
                        return {
                            success: false,
                            id: packageId,
                            error: error.message,
                        };
                    }
                    if (count === 0) {
                        return {
                            success: false,
                            id: packageId,
                            error: 'Package not found or already deleted',
                        };
                    }
                    deletedCount++;
                    return { success: true, id: packageId };
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    const errorStack = error instanceof Error ? error.stack : undefined;
                    this.logger.error(`Unexpected error deleting package ${packageId}: ${errorMessage}`, errorStack);
                    return {
                        success: false,
                        id: packageId,
                        error: errorMessage,
                    };
                }
            });
            const results = await Promise.all(deletePromises);
            results
                .filter(result => !result.success)
                .forEach(result => errors.push(result));
        }
        this.logger.log(`Batch delete completed. Deleted ${deletedCount} packages with ${errors.length} errors.`);
        return {
            deletedCount,
            errors,
        };
    }
    async batchUpdatePackageStatus(packageIds, isActive) {
        this.logger.log(`Attempting to batch update status for ${packageIds.length} packages to ${isActive ? 'active' : 'inactive'}`);
        const supabase = this.supabaseService.getClient();
        const errors = [];
        let updatedCount = 0;
        const batchSize = 10;
        const batches = [];
        for (let i = 0; i < packageIds.length; i += batchSize) {
            batches.push(packageIds.slice(i, i + batchSize));
        }
        for (const batch of batches) {
            const updatePromises = batch.map(async (packageId) => {
                try {
                    const { data, error } = await supabase
                        .from('packages')
                        .update({
                        is_deleted: !isActive,
                        updated_at: new Date().toISOString(),
                    })
                        .eq('id', packageId)
                        .select('id');
                    if (error) {
                        this.logger.error(`Error updating status for package ${packageId}: ${error.message}`, error.stack);
                        return {
                            success: false,
                            id: packageId,
                            error: error.message,
                        };
                    }
                    if (!data || data.length === 0) {
                        return {
                            success: false,
                            id: packageId,
                            error: 'Package not found',
                        };
                    }
                    updatedCount++;
                    return { success: true, id: packageId };
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    const errorStack = error instanceof Error ? error.stack : undefined;
                    this.logger.error(`Unexpected error updating status for package ${packageId}: ${errorMessage}`, errorStack);
                    return {
                        success: false,
                        id: packageId,
                        error: errorMessage,
                    };
                }
            });
            const results = await Promise.all(updatePromises);
            results
                .filter(result => !result.success)
                .forEach(result => errors.push(result));
        }
        this.logger.log(`Batch status update completed. Updated ${updatedCount} packages with ${errors.length} errors.`);
        return {
            updatedCount,
            errors,
        };
    }
};
exports.PackageBatchService = PackageBatchService;
exports.PackageBatchService = PackageBatchService = PackageBatchService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], PackageBatchService);
//# sourceMappingURL=package-batch.service.js.map