import { SupabaseService } from 'src/core/supabase/supabase.service';
import { CreatePackageDto } from '../dto/create-package.dto';
import { UpdatePackageDto } from '../dto/update-package.dto';
import { PackageDto } from '../dto/package.dto';
export declare class PackageCrudService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    createPackage(createPackageDto: CreatePackageDto): Promise<string>;
    updatePackage(id: string, updatePackageDto: UpdatePackageDto): Promise<PackageDto>;
    deletePackage(id: string): Promise<void>;
    updatePackageStatus(id: string, isActive: boolean): Promise<PackageDto>;
    private handleDatabaseError;
    private handleUpdateError;
}
