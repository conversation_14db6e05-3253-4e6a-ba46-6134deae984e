import { DivisionsService } from './divisions.service';
import { CreateDivisionDto } from './dto/create-division.dto';
import { UpdateDivisionDto } from './dto/update-division.dto';
import { DivisionDto } from './dto/division.dto';
export declare class AdminDivisionsController {
    private readonly divisionsService;
    private readonly logger;
    constructor(divisionsService: DivisionsService);
    createDivision(createDto: CreateDivisionDto): Promise<DivisionDto>;
    findOne(id: string): Promise<DivisionDto>;
    updateDivision(id: string, updateDto: UpdateDivisionDto): Promise<DivisionDto>;
    deleteDivision(id: string): Promise<void>;
}
