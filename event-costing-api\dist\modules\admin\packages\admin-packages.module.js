"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminPackagesModule = void 0;
const common_1 = require("@nestjs/common");
const auth_module_1 = require("../../auth/auth.module");
const admin_module_1 = require("../../auth/admin.module");
const packages_controller_js_1 = require("./packages.controller.js");
const packages_service_js_1 = require("./packages.service.js");
const package_crud_service_js_1 = require("./services/package-crud.service.js");
const package_query_service_js_1 = require("./services/package-query.service.js");
const package_relations_service_js_1 = require("./services/package-relations.service.js");
const package_batch_service_js_1 = require("./services/package-batch.service.js");
let AdminPackagesModule = class AdminPackagesModule {
};
exports.AdminPackagesModule = AdminPackagesModule;
exports.AdminPackagesModule = AdminPackagesModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule, admin_module_1.AdminModule],
        controllers: [packages_controller_js_1.PackagesController],
        providers: [
            packages_service_js_1.PackagesService,
            package_crud_service_js_1.PackageCrudService,
            package_query_service_js_1.PackageQueryService,
            package_relations_service_js_1.PackageRelationsService,
            package_batch_service_js_1.PackageBatchService,
        ],
    })
], AdminPackagesModule);
//# sourceMappingURL=admin-packages.module.js.map