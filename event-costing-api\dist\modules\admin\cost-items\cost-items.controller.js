"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CostItemsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CostItemsController = void 0;
const common_1 = require("@nestjs/common");
const cost_items_service_1 = require("./cost-items.service");
const create_cost_item_dto_1 = require("./dto/create-cost-item.dto");
const update_cost_item_dto_1 = require("./dto/update-cost-item.dto");
const cost_item_dto_1 = require("./dto/cost-item.dto");
const swagger_1 = require("@nestjs/swagger");
const admin_role_guard_1 = require("../../auth/guards/admin-role.guard");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CostItemsController = CostItemsController_1 = class CostItemsController {
    costItemsService;
    logger = new common_1.Logger(CostItemsController_1.name);
    constructor(costItemsService) {
        this.costItemsService = costItemsService;
    }
    create(createCostItemDto) {
        this.logger.log(`Received request to create cost item: ${JSON.stringify(createCostItemDto)}`);
        return this.costItemsService.create(createCostItemDto);
    }
    findAll() {
        this.logger.log('Received request to find all cost items');
        return this.costItemsService.findAll();
    }
    findOne(id) {
        this.logger.log(`Received request to find cost item with ID: ${id}`);
        return this.costItemsService.findOne(id);
    }
    update(id, updateCostItemDto) {
        this.logger.log(`Received request to update cost item ${id}: ${JSON.stringify(updateCostItemDto)}`);
        return this.costItemsService.update(id, updateCostItemDto);
    }
    remove(id) {
        this.logger.log(`Received request to delete cost item with ID: ${id}`);
        return this.costItemsService.remove(id);
    }
};
exports.CostItemsController = CostItemsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new cost item' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Cost item created successfully.',
        type: cost_item_dto_1.CostItemDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request (e.g., validation error)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict (e.g., duplicate item code)',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cost_item_dto_1.CreateCostItemDto]),
    __metadata("design:returntype", Promise)
], CostItemsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all cost items' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of cost items.',
        type: [cost_item_dto_1.CostItemDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CostItemsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific cost item by ID' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'The ID of the cost item',
        format: 'uuid',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cost item details.',
        type: cost_item_dto_1.CostItemDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cost item not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CostItemsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific cost item' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'The ID of the cost item to update',
        format: 'uuid',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cost item updated successfully.',
        type: cost_item_dto_1.CostItemDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request (e.g., validation error)',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cost item not found' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict (e.g., duplicate item code on update)',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cost_item_dto_1.UpdateCostItemDto]),
    __metadata("design:returntype", Promise)
], CostItemsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Soft-delete a specific cost item' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'The ID of the cost item to delete',
        format: 'uuid',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Cost item soft-deleted successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cost item not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CostItemsController.prototype, "remove", null);
exports.CostItemsController = CostItemsController = CostItemsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Cost Items'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_role_guard_1.AdminRoleGuard),
    (0, common_1.Controller)('admin/cost-items'),
    __metadata("design:paramtypes", [cost_items_service_1.CostItemsService])
], CostItemsController);
//# sourceMappingURL=cost-items.controller.js.map