/**
 * Comprehensive calculation detail hook
 * PERFORMANCE OPTIMIZED: Fixed object recreation and stabilized dependencies
 * PHASE 4 OPTIMIZATION: Consolidates all calculation detail state and actions
 *
 * This hook combines:
 * - Core calculation data (useCalculationDetail)
 * - Taxes and discounts state (useTaxesAndDiscounts)
 * - Calculation actions (useCalculationActions)
 *
 * Eliminates the need for manual state combination in components
 */

import { useMemo, useCallback } from "react";
import { useCalculationDetail } from "./useCalculationDetail";
import { useTaxesAndDiscounts } from "../financial/useTaxesAndDiscounts";
import { useCalculationActions } from "../useCalculationActions";
import { isValidUUID } from "@/lib/uuidValidation";

/**
 * Complete calculation detail hook that combines all necessary state and actions
 * @param id - The calculation ID (must be a valid UUID)
 * @returns Combined state and actions for the calculation detail page
 */
export const useCalculationDetailComplete = (id: string) => {
  // CRITICAL FIX: Validate UUID format to prevent race conditions
  const isValidId = isValidUUID(id);

  // Core calculation data with optimized parallel loading
  // Always call hooks but conditionally enable data fetching
  const calculationDetail = useCalculationDetail(id, {
    enabled: isValidId, // Only fetch if ID is valid
  });
  const { calculation, isLoading, isError } = calculationDetail;

  // Taxes and discounts state management
  // Always call hook but conditionally initialize with valid data
  const taxesAndDiscounts = useTaxesAndDiscounts(
    id,
    isValidId ? calculation?.taxes : [],
    isValidId ? calculation?.discount : null
  );

  // PERFORMANCE OPTIMIZATION: Memoize saveTaxesAndDiscount to prevent actions recreation
  const memoizedSaveTaxesAndDiscount = useCallback(
    (status?: "draft" | "completed" | "canceled") => {
      if (!isValidId) return Promise.resolve(false);
      return taxesAndDiscounts.saveTaxesAndDiscount(status);
    },
    [isValidId, taxesAndDiscounts.saveTaxesAndDiscount]
  );

  // Calculation actions (status changes, deletion, navigation)
  // Always call hook but use memoized save function
  const actions = useCalculationActions({
    calculationId: id,
    saveTaxesAndDiscount: memoizedSaveTaxesAndDiscount,
  });

  // PERFORMANCE OPTIMIZATION: Memoize combined state with stable dependencies
  const combinedState = useMemo(() => {
    if (!isValidId) {
      return null; // Return null for invalid IDs
    }

    return {
      ...calculationDetail,
      ...taxesAndDiscounts,
    };
  }, [
    isValidId,
    // Only depend on specific values, not entire objects
    calculationDetail.calculation?.id,
    calculationDetail.calculation?.name,
    calculationDetail.calculation?.status,
    calculationDetail.lineItems?.length,
    calculationDetail.isLoading,
    calculationDetail.isError,
    taxesAndDiscounts.taxes?.length,
    taxesAndDiscounts.discount?.value,
    taxesAndDiscounts.isUpdating,
  ]);

  // CRITICAL FIX: Memoize the return object with minimal dependencies
  return useMemo(
    () => ({
      // Combined state
      state: combinedState,

      // Actions (already memoized in useCalculationActions)
      actions,

      // Individual pieces for convenience
      calculation,
      isLoading: isValidId ? isLoading : false, // Don't show loading for invalid IDs
      isError: isValidId ? isError : false, // Don't show error for invalid IDs (handled by validation)

      // Calculation ID for context provider
      calculationId: id,

      // Validation status
      isValidId,
    }),
    [
      combinedState,
      actions,
      calculation?.id, // Only depend on ID, not entire object
      calculation?.name,
      calculation?.status,
      isLoading,
      isError,
      id,
      isValidId,
    ]
  );
};
