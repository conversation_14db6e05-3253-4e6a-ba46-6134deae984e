/**
 * Comprehensive calculation detail hook
 * PHASE 4 OPTIMIZATION: Consolidates all calculation detail state and actions
 *
 * This hook combines:
 * - Core calculation data (useCalculationDetail)
 * - Taxes and discounts state (useTaxesAndDiscounts)
 * - Calculation actions (useCalculationActions)
 *
 * Eliminates the need for manual state combination in components
 */

import { useMemo } from "react";
import { useCalculationDetail } from "./useCalculationDetail";
import { useTaxesAndDiscounts } from "../financial/useTaxesAndDiscounts";
import { useCalculationActions } from "../useCalculationActions";
import { isValidUUID } from "@/lib/uuidValidation";

/**
 * Complete calculation detail hook that combines all necessary state and actions
 * @param id - The calculation ID (must be a valid UUID)
 * @returns Combined state and actions for the calculation detail page
 */
export const useCalculationDetailComplete = (id: string) => {
  // CRITICAL FIX: Validate UUID format to prevent race conditions
  const isValidId = isValidUUID(id);

  // Core calculation data with optimized parallel loading
  // Only fetch data if ID is valid to prevent backend validation errors
  const calculationDetail = useCalculationDetail(isValidId ? id : "");
  const { calculation, isLoading, isError } = calculationDetail;

  // Taxes and discounts state management
  // Only initialize if ID is valid to prevent unnecessary API calls
  const taxesAndDiscounts = useTaxesAndDiscounts(
    isValidId ? id : "",
    calculation?.taxes,
    calculation?.discount
  );

  // Calculation actions (status changes, deletion, navigation)
  // Only initialize if ID is valid
  const actions = useCalculationActions({
    calculationId: isValidId ? id : "",
    saveTaxesAndDiscount: taxesAndDiscounts.saveTaxesAndDiscount,
  });

  // Combine all state into a single object (memoized for performance)
  const combinedState = useMemo(() => {
    return {
      ...calculationDetail,
      ...taxesAndDiscounts,
    };
  }, [calculationDetail, taxesAndDiscounts]);

  // CRITICAL FIX: Memoize the return object to prevent infinite re-renders
  return useMemo(
    () => ({
      // Combined state
      state: combinedState,

      // Actions
      actions,

      // Individual pieces for convenience
      calculation,
      isLoading: isValidId ? isLoading : false, // Don't show loading for invalid IDs
      isError: isValidId ? isError : !isValidId, // Show error for invalid IDs

      // Calculation ID for context provider
      calculationId: id,

      // Validation status
      isValidId,
    }),
    [combinedState, actions, calculation, isLoading, isError, id, isValidId]
  );
};
